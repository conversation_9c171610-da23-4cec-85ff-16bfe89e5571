const { onRequest, onCall } = require("firebase-functions/v2/https");
const { setGlobalOptions } = require("firebase-functions/v2");
const logger = require("firebase-functions/logger");
const functions = require("firebase-functions");
const admin = require("firebase-admin");
const nodemailer = require("nodemailer");

const {
  onDocumentCreated,
  // onDocumentCreated,
  // onDocumentUpdated,
  // onDocumentDeleted,
  // Change,
  // FirestoreEvent
} = require("firebase-functions/v2/firestore");

setGlobalOptions({ maxInstances: 10 });

var transporter = nodemailer.createTransport({
  host: "smtp.gmail.com",
  port: 465,
  secure: true,
  auth: {
    user: "<EMAIL>",
    pass: "ezbhhsudlcrguerw",
    // pass: 'zniorhjmhmatlzfd'
  },
});

admin.initializeApp();
const db = admin.firestore();

const sendSMS = false;

const smsKey =
  "s8HBXYiRVzef9753TAmOlEvow4FWypng1IQqhtdbCSr62k0jZaNG03Qjg5ReqMEKdc2S4pFsuUnCwZtz";

// exports.paymentCallback = onCall(async (dataa) => {
//   try {
//     const data = dataa.data;
//     console.log(data);
//     const url = "https://api.phonepe.com/apis/hermes/pg/v1/pay";
//     console.log(data);
//     console.log(data.xVerify);
//     console.log(data.encodedPayload);
//     const options = {
//       method: "POST",
//       headers: {
//         accept: "application/json",
//         "Content-Type": "application/json",
//         "X-VERIFY": data.xVerify,
//       },
//       body: JSON.stringify({ request: data.encodedPayload }),
//     };
//     return await fetch(url, options)
//       .then((res) => res.json())
//       .then((json) => {
//         console.log(json);
//         console.log(json["data"]["instrumentResponse"]["redirectInfo"]);
//         return json;
//       })
//       .catch((err) => {
//         console.error("error:" + err);
//         return err;
//       });
//   } catch (error) {
//     console.error(error.message);
//     return { success: false };
//   }
// });

// exports.updateuser = functions.firestore
//   .document("users/{userId}")
//   .onUpdate(async (snap, context) => {
//     console.log("event.id");
//     console.log(snap.id);
//   });

// exports.newInquiryEmail = functions.firestore
//   .document("inquiries/{userId}")
//   .onCreate(async (snap, context) => {
//     console.log("event.id");
//     console.log(snap.id);
//     const text = `<html>
//         <body>
//         Inquiry on: ${snap.data()["product"]}<br><br>
//         Name: ${snap.data()["name"]}<br><br>
//         Contact: ${snap.data()["contact"]}<br><br>
//         Email: ${snap.data()["email"]}<br><br>
//         DOB: ${snap.data()["dob"]}<br><br>
//         Comment: ${snap.data()["comment"]}<br><br>
//         Product: ${snap.data()["product"]}<br><br>
//         Product Link: ${snap.data()["productLink"]}<br><br>
//         </body>
//         </html>`;
//     sendEmailToUser("<EMAIL>", "New Inquiry", text);
//   });

// exports.createOrder = onCall(async (request) => {

// exports.requestSentEmail = onDocumentCreated(
//   "contactRequest/{userId}",
//   (event) => {
//     const snapshot = event.data;
//     if (!snapshot) {
//       console.log("No data associated with the event");
//       return;
//     }
//     const data = snapshot.data();
//     console.log("Got Data");
//     const htmlPart = `<html>
//     <body>
//     Name: ${data.name}<br><br>
//     Contact: ${data.mobile}<br><br>
//     Email: ${data.email}<br><br>
//     DOB: ${data.dob}<br><br>
//     Message: ${data.message}<br><br>
//     </body>
//     </html>`;
//     sendEmailToUser("<EMAIL>", "New Inquiry", htmlPart);
//     console.log("Email sent");
//   }
// );

// exports.verifyPayment = onRequest(async (request, response) => {
//   try {
//     let bufferObj = Buffer.from(request.body["response"], "base64");
//     let decodedString = bufferObj.toString("utf8");
//     console.log("The decoded string:", decodedString);
//     const paymentResponse = JSON.parse(decodedString);
//     if (paymentResponse["code"] === "PAYMENT_SUCCESS") {
//       const bookingId = paymentResponse["data"]["merchantTransactionId"];
//       console.log(bookingId);
//       console.log(paymentResponse["data"]);

//       // let payload = request.body['payload'];
//       // console.log(payload['order']['entity']['receipt']);
//       const orderDocRef = db.collection("orders").doc(bookingId);
//       const setsDocRef = db.collection("sets").doc("sets");
//       await db.runTransaction(async (transaction) => {
//         const orderDoc = await transaction.get(orderDocRef);
//         const orderData = orderDoc.data();
//         console.log(orderData);
//         if (orderData["processed"]) return; // ===================== TODO: Uncomment ===================== //
//         const setsDoc = await transaction.get(setsDocRef);
//         let newOrderNo = setsDoc.data()["orderNo"] + 1;
//         console.log("newOrderNo");
//         console.log(newOrderNo);
//         let dataVaried = false;
//         let pendingTrans = [];
//         // Process all itmes
//         for (let index = 0; index < orderData["items"].length; index++) {
//           // for (const orderItem in [orderData['items']]) {
//           const orderItem = orderData["items"][index];
//           const prodDoc = db.collection("products").doc(orderItem["pId"]);
//           let inventoryItemDoc = await transaction.get(prodDoc);
//           console.log("got data");
//           const inventoryItemData = inventoryItemDoc.data();
//           // console.log(inventoryItemData['variants'][orderItem['vId']]);
//           // const arrayOfVariants = Object.entries(inventoryItemData['variants']).map((e) => (e[1]));
//           const inventoryItemVariant =
//             inventoryItemData["variants"][orderItem["vId"]];
//           // .firstWhereOrNull((vrnt) => vrnt.vId == orderItem.vId);
//           if (inventoryItemVariant) {
//             pendingTrans.push({
//               documentId: inventoryItemDoc.id,
//               data: {
//                 fieldName: `variants.${orderItem["vId"]}.qty`,
//                 // value: inventoryItemVariant['qty'] - orderItem['qty']
//                 value: admin.firestore.FieldValue.increment(-orderItem["qty"]),
//               },
//             });
//             if (orderItem["qty"] > inventoryItemVariant["qty"]) {
//               dataVaried = true;
//               orderItem["missingQty"] =
//                 orderItem["qty"] - inventoryItemVariant["qty"];
//             }
//           } else {
//             dataVaried = true;
//             orderItem["missingQty"] = orderItem["qty"];
//           }
//           ////
//         }
//         // console.log(pendingTrans);
//         for (let index = 0; index < pendingTrans.length; index++) {
//           // for (const pTrans in pendingTrans) {
//           const pTrans = pendingTrans[index];
//           // console.log(pTrans);
//           console.log(`B4 prod ${index}`);
//           transaction.update(
//             db.collection("products").doc(pTrans["documentId"]),
//             {
//               [pTrans["data"]["fieldName"]]: pTrans["data"]["value"],
//             }
//           );
//         }
//         console.log("B4 update Ne oID");
//         transaction.update(setsDocRef, { orderNo: newOrderNo });
//         // Update Order Data
//         orderData["orderNo"] = newOrderNo;
//         // orderData['status'] = "Paid";
//         orderData["isPaid"] = true;
//         orderData["processed"] = true;
//         orderData["paymentId"] = paymentResponse["data"]["transactionId"]; // payload['payment']['entity']['id'];
//         // Update if any data is changed
//         if (dataVaried) {
//           console.log("Data Varied");
//         }
//         if (orderData["firstOrder"] == true) {
//           console.log("B4 firsst order");
//           transaction.update(db.collection("users").doc(orderData["uid"]), {
//             firstOrderDone: true,
//           });
//         }
//         // console.log(orderData);
//         console.log("B4 last");
//         transaction.update(orderDocRef, orderData);
//         pushOrderPlacedEmail(
//           orderData["email"],
//           orderData["address"]["firstName"] + orderData["address"]["lastName"],
//           newOrderNo
//         );
//         pushOrderPlacedSMS(
//           orderData["phone"],
//           orderData["address"]["firstName"] + orderData["address"]["lastName"],
//           newOrderNo
//         );
//       });
//       console.log("DONE");
//     }
//     return response.end();
//   } catch (error) {
//     console.error("Error creating order:", error);
//     return response.status(400).end();
//   }
// });

// exports.fulfilledEmailSMS = onCall(async (request) => {
//   try {
//     const emailText = `<html>
//         <body>
//         Dear, ${request.data.name} your order number ${request.data.orderNo} has been shipped by ${request.data.shippingCarrier} with tracking id: ${request.data.trackingId}\n
//         <br><p>For tracking order <a href=${request.data.trackingLink}>Click here</a></p>
//         </body>
//         </html>`;
//     const smsText = `
//         Dear, ${request.data.name} your order number ${request.data.orderNo} has been shipped by ${request.data.shippingCarrier} with tracking id: ${request.data.trackingId}\nFor tracking order visit ${request.data.trackingLink}`;
//     sendEmailToUser(request.data.email, "Order Fulfilled", emailText);
//     sendSMSToUser(smsText, request.data.phone);
//     return true;
//   } catch (error) {
//     console.error("Error in fulfillment email sms:", error);
//     return { error: "An error occurred while sending email sms." };
//   }
// });

// exports.orderPlacedEmailSMS = onCall(async (request) => {
//   pushOrderPlacedEmail(
//     request.data.email,
//     request.data.name,
//     request.data.orderNo
//   );
//   pushOrderPlacedSMS(
//     request.data.phone,
//     request.data.name,
//     request.data.orderNo
//   );
// });

exports.sendOtpEmail = onCall(async (request) => {
  const emailText = `<html>
    <body>
    <p>${request.data.otp} is your OTP. Valid for 5 minutes.</p>
    </body>
    </html>`;
  sendEmailToUser(request.data.email, "Frenyz", emailText);
});

async function pushOrderPlacedEmail(email, fullname, orderNo) {
  try {
    const text = `<html>
        <body>
        Dear, ${fullname} your order number ${orderNo} has been successfully placed.<br>
        Thanks for shopping at Frenyz.
        </body>
        </html>`;
    sendEmailToUser(email, "Order Fulfilled", text);
    return true;
  } catch (error) {
    console.error("Error in order placed email:", error);
    return { error: "An error occurred while sending email." };
  }
}

async function pushOrderPlacedSMS(phoneNo, fullname, orderNo) {
  try {
    const text = `
        Dear, ${fullname} your order number ${orderNo} has been successfully placed.\nThanks for shopping at Frenyz.
        `;
    sendSMSToUser(text, phoneNo);
    return true;
  } catch (error) {
    console.error("Error in order placed sms:", error);
    return { error: "An error occurred while sending sms." };
  }
}

async function sendEmailToUser(to, subject, html) {
  try {
    const mailOptions = {
      from: {
        name: "Frenyz",
        address: "<EMAIL>",
      },
      to: to,
      subject: subject,
      html: html,
    };
    return transporter.sendMail(mailOptions, (error, data) => {
      if (error) {
        console.log(error);
        return;
      }
      console.log("Sent!");
    });
  } catch (error) {
    console.log(error);
  }
}

// async function sendSMSToUser(text, number) {
//   try {
//     if (!sendSMS) return;
//     var req = unirest("POST", "https://www.fast2sms.com/dev/bulkV2");

//     req.headers({
//       authorization: smsKey,
//     });

//     req.form({
//       message: text,
//       language: "english",
//       route: "q",
//       numbers: number.replace(/\D/g, "").slice(-10),
//     });

//     req.end(function (res) {
//       if (res.error) throw new Error(res.error);

//       console.log(res.body);
//     });
//   } catch (error) {
//     console.log(error);
//   }
// }
/*
async function initRefund(amount, receiptNo, paymentId) {
    try {
        const order = await razorpay.payments.refund.refund(paymentId, {
            "amount": amount,
            "speed": "optimum",
            "receipt": receiptNo
        });
        return response.json(order);
    } catch (error) {
        console.error('Error creating order:', error);
        response.status(500).json({ error: 'An error occurred while creating the order.' });
    }
};
 */
// ======================================================== //

/* 
exports.tester = onRequest(async (request, response) => {
    // sendSMSToUser("This is test message", "6353817704");
    const paymentResponse = request.body;
    const bookingId = paymentResponse["data"]["merchantTransactionId"];
    console.log(bookingId);
    console.log(paymentResponse["data"]);

    // let payload = request.body['payload'];
    // console.log(payload['order']['entity']['receipt']);
    const orderDocRef = db.collection('orders').doc(bookingId);
    const setsDocRef = db.collection('sets').doc('sets');
    await db.runTransaction(async (transaction) => {
        const orderDoc = await transaction.get(orderDocRef);
        const orderData = orderDoc.data();
        console.log(orderData);
        if (orderData['processed']) return; // ===================== TODO: Uncomment ===================== //
        const setsDoc = await transaction.get(setsDocRef);
        let newOrderNo = setsDoc.data()['orderNo'] + 1;
        console.log("newOrderNo");
        console.log(newOrderNo);
        let dataVaried = false;
        let pendingTrans = [];
        // Process all itmes
        for (let index = 0; index < orderData['items'].length; index++) {
            // for (const orderItem in [orderData['items']]) {
            const orderItem = orderData['items'][index];
            const prodDoc = db.collection('products').doc(orderItem['pId']);
            let inventoryItemDoc =
                await transaction.get(prodDoc);
            console.log("got data");
            const inventoryItemData = inventoryItemDoc.data();
            // console.log(inventoryItemData['variants'][orderItem['vId']]);
            // const arrayOfVariants = Object.entries(inventoryItemData['variants']).map((e) => (e[1]));
            const inventoryItemVariant = inventoryItemData['variants'][orderItem['vId']];
            // .firstWhereOrNull((vrnt) => vrnt.vId == orderItem.vId);
            if (inventoryItemVariant) {
                pendingTrans.push({
                    documentId: inventoryItemDoc.id,
                    data: {
                        fieldName: `variants.${orderItem['vId']}.qty`,
                        // value: inventoryItemVariant['qty'] - orderItem['qty']
                        value: admin.firestore.FieldValue.increment(-orderItem['qty'])
                    }
                });
                if (orderItem['qty'] > inventoryItemVariant['qty']) {
                    dataVaried = true;
                    orderItem['missingQty'] = orderItem['qty'] - inventoryItemVariant['qty'];
                }
            } else {
                dataVaried = true;
                orderItem['missingQty'] = orderItem['qty'];
            }
            ////
        }
        // console.log(pendingTrans);
        for (let index = 0; index < pendingTrans.length; index++) {
            // for (const pTrans in pendingTrans) {
            const pTrans = pendingTrans[index];
            // console.log(pTrans);
            console.log(`B4 prod ${index}`);
            transaction.update(db.collection('products').doc(pTrans['documentId']), {
                [pTrans['data']['fieldName']]: pTrans['data']['value']
            });
        }
        console.log("B4 update Ne oID");
        transaction.update(setsDocRef, { 'orderNo': newOrderNo });
        // Update Order Data
        orderData['orderNo'] = newOrderNo;
        orderData['status'] = "Paid";
        orderData['isPaid'] = true;
        orderData['processed'] = true;
        orderData['paymentId'] = paymentResponse["data"]["transactionId"];// payload['payment']['entity']['id'];
        // Update if any data is changed
        if (dataVaried) {
            console.log("Data Varied");
        }
        if (orderData['firstOrder'] == true) {
            console.log("B4 firsst order");
            transaction.update(db.collection('products').doc(orderData['uid']), { 'firstOrderDone': true });
        }
        // console.log(orderData);
        console.log("B4 last");
        transaction.update(orderDocRef, orderData);
        pushOrderPlacedEmail(orderData['email'], orderData['address']['firstName'] + orderData['address']['lastName'], newOrderNo);
        pushOrderPlacedSMS(orderData['phone'], orderData['address']['firstName'] + orderData['address']['lastName'], newOrderNo);
    });
    console.log("DONE");
}); */

/* exports.createOrder = onCall(async (request) => {
    // exports.createOrder = onRequest(async (request, response) => {
    try {
        // console.log(data);
        // console.log(request.body);
        // const { amount, receiptNo } = request.body;

        const options = {
            amount: request.data.amount,
            // amount: amount,
            currency: 'INR',
            // receipt: receiptNo,
            receipt: request.data.receiptNo,
        };

        const order = await razorpay.orders.create(options);
        console.log(order.status);
        return order;
    } catch (error) {
        console.error('Error creating order:', error);
        return { error: 'An error occurred while creating the order.' };
    }
}); */
