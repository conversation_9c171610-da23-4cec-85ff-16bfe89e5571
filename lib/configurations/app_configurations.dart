import 'package:speed_force_franchise/constants/assets.dart';
import 'package:speed_force_franchise/constants/colors.dart';

// late IApiEndPoints apiEndPoints;
// late IStorageManager storageManager;
late IAppAssetsConstants assetsConstants;
late IColorsConstants colorsConstants;
// late NetworkManager networkManager;
// late IAppHeaders? iAppHeaders;

Future<void> initialBaseSetup() async {
  // storageManager = IStorageManager.getInstance();
  // await storageManager.init();
  assetsConstants = IAppAssetsConstants.getInstance();
  // apiEndPoints = IApiEndPoints.getInstance();
  colorsConstants = IColorsConstants.getInstance();
  // networkManager = NetworkManager();
  // iAppHeaders = AppHeaders();
  // await sharedPreferences.loadData(config);
}
