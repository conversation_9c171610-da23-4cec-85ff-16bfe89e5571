import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';

enum RequestType { get, post, put, delete }

enum BottomNavigationBarScreens {
  services,
  parts,
  accounts,
  more,
}

enum RouteScreens {
  root,
  splash,
  login,
  baseModule,
  addGarage,
  homeScreen,
  createRepairOrder,
  repairDetails,
  confirmOrder,
  inventory,

  openOrders,
  addInsuranceProvider,
  createInvoice,
  addStock,
  addVendor,
  addPart,
  addMakeModels,
  viewAlert,
  purchaseOrder,
  editPurchaseOrder,
  myPurchaseOrders,
  stockIn,
  myCustomers,
  myVendors,
  customerDetails,
  repairOrderDetails,
  orderSearch,
  vehicleSearch,
  webview,
  csvUpload,
  addExpense,
  reports,
  totalCustomersReports,
  monthlyCustomersReports,
  sparePartsConsumption,
  misReports
}

enum SnackBarType {
  success,
  error,
  info,
}

enum FirebaseCollections {
  customers,
  customerVehicles,
  franchises,
  orders,
  vehiclesCompanies,
  manufacturers,
  models,
  transactions,
  services,
  parts,
  stock,
  categories,
  tags,
  vendors,
  invoices,
  puchaseOrders,
  stockIn,
  advertises,
  expenseLabels,
  expenses,
  partsPurchases,
  appSettings,
}

extension FirebaseCollectionsExtension on FirebaseCollections {
  String get name {
    switch (this) {
      case FirebaseCollections.customers:
        return 'Customers';
      case FirebaseCollections.customerVehicles:
        return 'CustomersVehicles';
      case FirebaseCollections.franchises:
        return 'Franchises';
      case FirebaseCollections.orders:
        return 'Orders';
      case FirebaseCollections.vehiclesCompanies:
        return 'VehicleCompanies';
      case FirebaseCollections.manufacturers:
        return 'Manufacturers';
      case FirebaseCollections.models:
        return 'Models';
      case FirebaseCollections.transactions:
        return 'Transactions';
      case FirebaseCollections.tags:
        return 'Tags';
      case FirebaseCollections.services:
        return 'Services';
      case FirebaseCollections.parts:
        return 'Parts';
      case FirebaseCollections.stock:
        return 'Stock';
      case FirebaseCollections.categories:
        return 'Categories';
      case FirebaseCollections.invoices:
        return 'Invoices';
      case FirebaseCollections.puchaseOrders:
        return 'PurchaseOrders';
      case FirebaseCollections.vendors:
        return 'Vendors';
      case FirebaseCollections.stockIn:
        return 'StockIn';
      case FirebaseCollections.advertises:
        return 'Advertises';
      case FirebaseCollections.expenseLabels:
        return 'ExpenseLabels';
      case FirebaseCollections.expenses:
        return 'Expenses';
      case FirebaseCollections.partsPurchases:
        return 'PartsPurchases';
      case FirebaseCollections.appSettings:
        return 'AppSettings';
    }
  }
}

class FBFunctions {
  static final ff = FirebaseFunctions.instance;
}

enum OrderStatus {
  created,
  workInProgress,
  ready,
  paymentDue,
  paymentDone,
  completed,
}

extension OrderStatusExtension on OrderStatus {
  String get name {
    switch (this) {
      case OrderStatus.created:
        return "CREATED";
      case OrderStatus.workInProgress:
        return "WORK IN PROGRESS";
      case OrderStatus.ready:
        return "READY";
      case OrderStatus.paymentDue:
        return "PAYMENT DUE";
      case OrderStatus.paymentDone:
        return "PAYMENT DONE";
      case OrderStatus.completed:
        return "COMPLETED";
    }
  }

  OrderStatus getFromName(String name) {
    switch (name) {
      case "CREATED":
        return OrderStatus.created;
      case "WORK IN PROGRESS":
        return OrderStatus.workInProgress;
      case "READY":
        return OrderStatus.ready;
      case "PAYMENT DUE":
        return OrderStatus.paymentDue;
      case "PAYMENT DONE":
        return OrderStatus.paymentDone;
      case "COMPLETED":
        return OrderStatus.completed;
      default:
        return OrderStatus.created;
    }
  }

  Color getColor() {
    switch (this) {
      case OrderStatus.created:
        return const Color.fromARGB(159, 45, 99, 249);
      case OrderStatus.workInProgress:
        return const Color(0xffFF642E);
      case OrderStatus.ready:
        return const Color(0xff9CD326);
      case OrderStatus.paymentDue:
        return const Color.fromARGB(255, 223, 58, 58);
      case OrderStatus.paymentDone:
      case OrderStatus.completed:
        return Colors.green;
    }
  }
}

enum PurchaseOrderStatus { open, completed }

extension PurchaseOrderStatusExtension on PurchaseOrderStatus {
  String get name {
    switch (this) {
      case PurchaseOrderStatus.open:
        return "OPEN";
      case PurchaseOrderStatus.completed:
        return "COMPLETED";
    }
  }

  PurchaseOrderStatus getFromName(String name) {
    switch (name) {
      case "OPEN":
        return PurchaseOrderStatus.open;
      case "COMPLETED":
        return PurchaseOrderStatus.completed;
      default:
        return PurchaseOrderStatus.open;
    }
  }
}

enum PaymentMode {
  upi,
  cash,
  card,
  cheque,
  bank,
  // paytm,
  // debitCard,
  // creditCard,
  // paymentLink,
  // bankTransfer,
  // voucher,
  // creditNote,
  // phonePe,
  // googlePay,
  // tds,
}

extension PaymentModeExtension on PaymentMode {
  String get name {
    switch (this) {
      case PaymentMode.upi:
        return "UPI";
      case PaymentMode.cash:
        return "CASH";
      case PaymentMode.card:
        return "CARD";
      case PaymentMode.cheque:
        return "CHEQUE";
      case PaymentMode.bank:
        return "BANK";

      // case PaymentMode.paytm:
      //   return "PAYTM";
      // case PaymentMode.debitCard:
      //   return "DEBIT CARD";
      // case PaymentMode.creditCard:
      //   return "CREDIT CARD";
      // case PaymentMode.paymentLink:
      //   return "PATYMENT LINK";
      // case PaymentMode.bankTransfer:
      //   return "BANK TRANSFER";
      // case PaymentMode.voucher:
      //   return "VOUCHER";
      // case PaymentMode.creditNote:
      //   return "CREDIT NOTE";
      // case PaymentMode.phonePe:
      //   return "PhonePe";
      // case PaymentMode.googlePay:
      //   return "Google Pay";
      // case PaymentMode.tds:
      //   return "TDS";
      default:
        return "CASH";
    }
  }

  PaymentMode getFromName(String name) {
    switch (name) {
      case "UPI":
        return PaymentMode.upi;
      case "CASH":
        return PaymentMode.cash;
      case "CARD":
        return PaymentMode.card;
      case "CHEQUE":
        return PaymentMode.cheque;
      case "BANK":
        return PaymentMode.bank;
      // case "PAYTM":
      //   return PaymentMode.paytm;
      // case "DEBIT CARD":
      //   return PaymentMode.debitCard;
      // case "CREDIT CARD":
      //   return PaymentMode.creditCard;
      // case "PATYMENT LINK":
      //   return PaymentMode.paymentLink;
      // case "BANK TRANSFER":
      //   return PaymentMode.bankTransfer;
      // case "VOUCHER":
      //   return PaymentMode.voucher;
      // case "CREDIT NOTE":
      //   return PaymentMode.creditNote;
      // case "PhonePe":
      //   return PaymentMode.phonePe;
      // case "Google Pay":
      //   return PaymentMode.googlePay;

      // case "TDS":
      //   return PaymentMode.tds;
      default:
        return PaymentMode.cash;
    }
  }
}

enum SearchOrdersBy {
  jobcards,
  invoiceNumber,
  dateRange,
}

enum SearchVehiclesBy {
  registrationNumber,
  customerNumber,
  vinNumber,
}

enum SearchDateRange {
  today,
  thisWeek,
  // thisMonth,
  lastMonth,
  dateRange,
}

extension SaerchDateRangeExtension on SearchDateRange {
  String get name {
    switch (this) {
      case SearchDateRange.today:
        return "Today";
      case SearchDateRange.thisWeek:
        return "Last 7 days";

      // case SearchDateRange.thisMonth:
      //   return "This Month";

      case SearchDateRange.lastMonth:
        return "Last 30 days";

      case SearchDateRange.dateRange:
        return "Date Range";
    }
  }
}

enum ViewOrdersBy {
  customerId,
  vehicleId,
  orderStatus,
}

enum ExpenseMode { paid, credit }

extension ExpenseModeExtention on ExpenseMode {
  ExpenseMode getFromName(String name) {
    switch (name) {
      case "Paid":
        return ExpenseMode.paid;
      case "Credit":
        return ExpenseMode.credit;
      default:
        return ExpenseMode.paid;
    }
  }
}
