import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/firebase_options.dart';
import 'package:speed_force_franchise/modules/login/controllers/login_controller.dart';
import 'package:speed_force_franchise/routing_manager/navigation_routes_generator.dart';
import 'package:speed_force_franchise/themes/light_theme.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/widgets/global_loader.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  await initialBaseSetup();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    Get.put(LoginController());

    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    return ScreenUtilInit(
      designSize: const Size(360, 690),
      minTextAdapt: true,
      // splitScreenMode: true,
      // Use builder only if you need to use library outside ScreenUtilInit context
      builder: (_, child) {
        return GlobalLoaderOverlay(
          // useDefaultLoading: false,
          overlayColor: Colors.black.withOpacity(0.5),
          overlayWidgetBuilder: (description) {
            //ignored progress for the moment
            return GlobalLoader(
              description: description,
            );
          },
          child: GetMaterialApp(
            debugShowCheckedModeBanner: false,
            // You can use the library anywhere in the app even in theme
            theme: LightTheme.getThemeData(),
            initialRoute: FirebaseAuth.instance.currentUser != null
                ? RouteScreens.homeScreen.name
                : RouteScreens.login.name,
            onGenerateRoute: NavigationRoutesGenerator.onGenerateRoute,
          ),
        );
      },
    );
  }
}
