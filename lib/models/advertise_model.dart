import 'dart:convert';

class AdvertiseModel {
  String? imageUrl;
  String? redirectLink;
  AdvertiseModel({
    this.imageUrl,
    this.redirectLink,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (imageUrl != null) {
      result.addAll({'imageUrl': imageUrl});
    }
    if (redirectLink != null) {
      result.addAll({'redirectLink': redirectLink});
    }

    return result;
  }

  factory AdvertiseModel.fromMap(Map<String, dynamic> map) {
    return AdvertiseModel(
      imageUrl: map['imageUrl'],
      redirectLink: map['redirectLink'],
    );
  }

  String toJson() => json.encode(toMap());

  factory AdvertiseModel.fromJson(String source) =>
      AdvertiseModel.fromMap(json.decode(source));
}
