import 'dart:convert';

class FranchiseDetails {
  String? ownerName;
  String? garageName;
  String? address;
  String? email;
  String? contactNumber;
  FranchiseDetails({
    this.ownerName,
    this.garageName,
    this.address,
    this.email,
    this.contactNumber,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (ownerName != null) {
      result.addAll({'ownerName': ownerName});
    }
    if (garageName != null) {
      result.addAll({'garageName': garageName});
    }
    if (address != null) {
      result.addAll({'address': address});
    }
    if (email != null) {
      result.addAll({'email': email});
    }
    if (contactNumber != null) {
      result.addAll({'contactNumber': contactNumber});
    }

    return result;
  }

  factory FranchiseDetails.fromMap(Map<String, dynamic> map) {
    return FranchiseDetails(
      ownerName: map['ownerName'],
      garageName: map['garageName'],
      address: map['address'],
      email: map['email'],
      contactNumber: map['contactNumber'],
    );
  }

  String toJson() => json.encode(toMap());

  factory FranchiseDetails.fromJson(String source) =>
      FranchiseDetails.fromMap(json.decode(source));
}
