import 'dart:convert';

import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/vendor_model.dart';

class PurchaseOrderModel {
  String? franchiseId;
  String? purchaseOrderId;
  String? createAt;
  String? vendorId;
  List<PartModel>? parts;
  String? instructionsForVendor;
  PurchaseOrderStatus? purchaseOrderStatus;

  //Logical properties
  VendorModel? vendorModel;

  PurchaseOrderModel({
    this.parts,
    this.franchiseId,
    this.purchaseOrderId,
    this.createAt,
    this.vendorId,
    this.instructionsForVendor,
    this.purchaseOrderStatus,
    this.vendorModel,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (franchiseId != null) {
      result.addAll({'franchiseId': franchiseId});
    }
    if (purchaseOrderId != null) {
      result.addAll({'purchaseOrderId': purchaseOrderId});
    }
    if (createAt != null) {
      result.addAll({'createAt': createAt});
    }
    if (vendorId != null) {
      result.addAll({'vendorId': vendorId});
    }
    result
        .addAll({'parts': parts?.map((x) => x.toPurchaseOrderMap()).toList()});
    if (instructionsForVendor != null) {
      result.addAll({'instructionsForVendor': instructionsForVendor});
    }
    if (purchaseOrderStatus != null) {
      result.addAll({'purchaseOrderStatus': purchaseOrderStatus?.name});
    }
    if (vendorModel != null) {
      result.addAll({'vendorModel': vendorModel!.toMap()});
    }

    return result;
  }

  factory PurchaseOrderModel.fromMap(Map<String, dynamic> map) {
    return PurchaseOrderModel(
      franchiseId: map['franchiseId'],
      purchaseOrderId: map['purchaseOrderId'],
      createAt: map['createAt'],
      vendorId: map['vendorId'],
      parts: List<PartModel>.from(
          map['parts']?.map((x) => PartModel.fromPurchaseOrderMap(x))),
      instructionsForVendor: map['instructionsForVendor'],
      purchaseOrderStatus: map['purchaseOrderStatus'] != null
          ? PurchaseOrderStatus.open.getFromName(map['purchaseOrderStatus'])
          : null,
      vendorModel: map['vendorModel'] != null
          ? VendorModel.fromMap(map['vendorModel'])
          : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory PurchaseOrderModel.fromJson(String source) =>
      PurchaseOrderModel.fromMap(json.decode(source));
}
