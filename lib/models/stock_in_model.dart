import 'dart:convert';

import 'package:speed_force_franchise/enums/enums.dart';

class StockInModel {
  String? franchiseId;
  String? stockInId;
  String? invoiceDate;
  String? invoiceNumber;
  String? referenceId;
  String? vendorId;
  PaymentMode? paymentMode;
  List<String>? invoiceImages;
  List<StockInPart>? stockInParts;
  double? amountPaid;
  double? total;

  StockInModel({
    this.franchiseId,
    this.stockInId,
    this.invoiceDate,
    this.invoiceNumber,
    this.referenceId,
    this.vendorId,
    this.paymentMode,
    this.invoiceImages,
    this.stockInParts,
    this.amountPaid,
    this.total,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (franchiseId != null) {
      result.addAll({'franchiseId': franchiseId});
    }
    if (stockInId != null) {
      result.addAll({'stockInId': stockInId});
    }
    if (invoiceDate != null) {
      result.addAll({'invoiceDate': invoiceDate});
    }
    if (invoiceNumber != null) {
      result.addAll({'invoiceNumber': invoiceNumber});
    }
    if (referenceId != null) {
      result.addAll({'referenceId': referenceId});
    }
    if (vendorId != null) {
      result.addAll({'vendorId': vendorId});
    }
    if (paymentMode != null) {
      result.addAll({'paymentMode': paymentMode?.name});
    }
    if (invoiceImages != null) {
      result.addAll({'invoiceImages': invoiceImages});
    }
    if (stockInParts != null) {
      result.addAll(
          {'stockInParts': stockInParts!.map((x) => x.toMap()).toList()});
    }
    if (amountPaid != null) {
      result.addAll({'amountPaid': amountPaid});
    }
    if (total != null) {
      result.addAll({'total': total});
    }

    return result;
  }

  factory StockInModel.fromMap(Map<String, dynamic> map) {
    return StockInModel(
      franchiseId: map['franchiseId'],
      stockInId: map['stockInId'],
      invoiceDate: map['invoiceDate'],
      invoiceNumber: map['invoiceNumber'],
      referenceId: map['referenceId'],
      vendorId: map['vendorId'],
      paymentMode: map['paymentMode'] != null
          ? PaymentMode.cash.getFromName(map['paymentMode'])
          : null,
      invoiceImages: List<String>.from(map['invoiceImages']),
      stockInParts: map['stockInParts'] != null
          ? List<StockInPart>.from(
              map['stockInParts']?.map((x) => StockInPart.fromMap(x)))
          : null,
      amountPaid: map['amountPaid']?.toDouble(),
      total: map['total']?.toDouble(),
    );
  }

  String toJson() => json.encode(toMap());

  factory StockInModel.fromJson(String source) =>
      StockInModel.fromMap(json.decode(source));
}

class StockInPart {
  String? partName;
  String? partId;
  double? newStock;
  double? purchasePrice;
  double? mrp;
  StockInPart({
    this.partName,
    this.partId,
    this.newStock,
    this.purchasePrice,
    this.mrp,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (partName != null) {
      result.addAll({'partName': partName});
    }
    if (partId != null) {
      result.addAll({'partId': partId});
    }
    if (newStock != null) {
      result.addAll({'newStock': newStock});
    }
    if (purchasePrice != null) {
      result.addAll({'purchasePrice': purchasePrice});
    }
    if (mrp != null) {
      result.addAll({'mrp': mrp});
    }

    return result;
  }

  factory StockInPart.fromMap(Map<String, dynamic> map) {
    return StockInPart(
      partName: map['partName'],
      partId: map['partId'],
      newStock: map['newStock']?.toDouble(),
      purchasePrice: map['purchasePrice']?.toDouble(),
      mrp: map['mrp']?.toDouble(),
    );
  }

  String toJson() => json.encode(toMap());

  factory StockInPart.fromJson(String source) =>
      StockInPart.fromMap(json.decode(source));
}
