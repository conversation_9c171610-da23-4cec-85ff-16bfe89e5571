import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/accounts/modules/add_expenses/models/expense_model.dart';
import 'package:speed_force_franchise/modules/accounts/modules/add_expenses/models/repair_order_expense_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/vehicle_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class AccountsController extends GetxController {
  bool isLoading = false;
  bool isFiltered = false;
  bool fetchexpense = false;

  List<RepairOrderModel> repairOrderModels = [];

  List<RepairOrderModel> searchedRepairInvoiceModels = [];
  List<RepairOrderExpenseModel> searchedExpenseModels = [];

  TextEditingController searchOrderTextEditingController =
      TextEditingController();
////////////////////////////////
  int selectedTab = 0;
  SearchDateRange selectedDateRange = SearchDateRange.today;

  List<RepairOrderExpenseModel> incomeExpenses = [];
  List<RepairOrderExpenseModel> totalIncomeExpenses = [];
  List<RepairOrderExpenseModel> totalIncome = [];
  List<RepairOrderModel> filteredInvvoices = [];
  List<RepairOrderExpenseModel> filteredExpenses = [];
  List<RepairOrderExpenseModel> searchedIncomeExpenses = [];

  DateTime startDate = DateTime.now();
  DateTime endDate = DateTime.now();

  void changeSelectedTab({required int changedTab}) {
    selectedTab = changedTab;
    update();
  }

  void changeSearchDateRange({
    required SearchDateRange dateRange,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    selectedDateRange = dateRange;

    DateTime? startFitlerDate = startDate;
    DateTime? endFilterDate = endDate;

    if (selectedDateRange == SearchDateRange.today) {
      startFitlerDate = DateTime.now();
      endFilterDate = DateTime.now();
    } else if (selectedDateRange == SearchDateRange.thisWeek) {
      startFitlerDate = DateTime.now().subtract(const Duration(days: 6));
      endFilterDate = DateTime.now();
    } else if (selectedDateRange == SearchDateRange.lastMonth) {
      startFitlerDate = DateTime.now().subtract(const Duration(days: 30));
      endFilterDate = DateTime.now();
    }

    if (startFitlerDate != null && endFilterDate != null) {
      this.startDate = startFitlerDate;
      this.endDate = endFilterDate;
      filterOrdersByDates(startDate: startFitlerDate, endDate: endFilterDate);
    }

    update();
  }

  void fetchIncomeExpenses() async {
    print(FirebaseAuth.instance.currentUser?.uid);
    incomeExpenses.clear();
    totalIncomeExpenses.clear();
    totalIncome.clear();
    filteredInvvoices.clear();
    searchedIncomeExpenses.clear();
    fetchexpense = true;
    QuerySnapshot<Map<String, dynamic>> expenseCollection =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.expenses.name)
            .where('franchiseId',
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .get();
    for (var expenseDoc in expenseCollection.docs) {
      ExpenseModel expenseModel = ExpenseModel.fromMap(expenseDoc.data());

      DocumentSnapshot<Map<String, dynamic>> orderDocumentSnaoshot =
          await FirebaseFirestore.instance
              .collection(FirebaseCollections.orders.name)
              .doc(expenseModel.repairOrderId)
              .get();

      Map<String, dynamic>? orderMap = orderDocumentSnaoshot.data();

      if (true)
      // if (orderMap != null)
      {
        RepairOrderModel repairOrderModel =
            RepairOrderModel.fromMap(orderDocumentSnaoshot.id, orderMap ?? {});

        totalIncomeExpenses.add(RepairOrderExpenseModel(
            expenseModel: expenseModel, repairOrderModel: repairOrderModel));

        changeSearchDateRange(dateRange: selectedDateRange);
      }
    }

    QuerySnapshot<Map<String, dynamic>> incomeCollection =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.invoices.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .where("orderStatus", isEqualTo: OrderStatus.completed.name)
            .get();

    for (var incomeDoc in incomeCollection.docs) {
      RepairOrderModel repairOrderModel =
          RepairOrderModel.fromInvoiceDetailsMap(
              incomeDoc.id, incomeDoc.data());

      totalIncome
          .add(RepairOrderExpenseModel(repairOrderModel: repairOrderModel));

      changeSearchDateRange(dateRange: selectedDateRange);
    }
    filterExpensesByDates(startDate: DateTime.now(), endDate: DateTime.now());
    fetchexpense = false;

    // fetchInvoices();
    update();
  }

  void fetchInvoices({
    String? orderStatus,
    String? vehicleId,
  }) async {
    isLoading = true;

    // update();

    QuerySnapshot<Map<String, dynamic>>? invoices;
    invoices = await FirebaseFirestore.instance
        .collection(FirebaseCollections.invoices.name)
        .where("franchiseId", isEqualTo: FirebaseAuth.instance.currentUser?.uid)
        .where("orderStatus", isEqualTo: OrderStatus.completed.name)
        .orderBy('completionDate', descending: true)
        .get();
    repairOrderModels.clear();

    if (invoices != null) {
      for (var invoice in invoices.docs) {
        Map<String, dynamic> invoiceMap = invoice.data();

        RepairOrderModel repairOrderModel =
            RepairOrderModel.fromInvoiceDetailsMap(invoice.id, invoiceMap);

        DocumentSnapshot<Map<String, dynamic>> customerDetailsSnapShot =
            await FirebaseFirestore.instance
                .collection(FirebaseCollections.customers.name)
                .doc(repairOrderModel.customerId)
                .get();

        Map<String, dynamic>? customerDetailsMap =
            customerDetailsSnapShot.data();
        if (customerDetailsMap != null) {
          repairOrderModel.customerDetailsModel =
              CustomerDetailsModel.fromMap(customerDetailsMap);
        }

        DocumentSnapshot<Map<String, dynamic>> vehicleDetailsSnapShot =
            await FirebaseFirestore.instance
                .collection(FirebaseCollections.customerVehicles.name)
                .doc(repairOrderModel.vehicleId)
                .get();

        Map<String, dynamic>? vehicleDetailsMap = vehicleDetailsSnapShot.data();
        if (vehicleDetailsMap != null) {
          repairOrderModel.vehicleDetailsModel =
              VehicleDetailsModel.fromMap(vehicleDetailsMap);
        }

        repairOrderModels.addIf(
            repairOrderModels
                .where((element) =>
                    element.invoiceId == repairOrderModel.invoiceId)
                .isEmpty,
            repairOrderModel);
      }
    }
    filterInvoiceByDates(startDate: DateTime.now(), endDate: DateTime.now());
    isLoading = false;
    update();
  }

  void filterInvoiceByDates(
      {required DateTime startDate, required DateTime endDate}) {
    isFiltered = true;
    filteredInvvoices.clear();
    for (RepairOrderModel invoice in repairOrderModels) {
      DateTime completionDate = invoice.completionDate!.toDate();
      completionDate = DateTime(
          completionDate.year, completionDate.month, completionDate.day);
      if (completionDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
          completionDate.isBefore(endDate.add(const Duration(days: 1)))) {
        filteredInvvoices.add(invoice);
      }
    }

    if (filteredInvvoices.isNotEmpty) {
      searchedRepairInvoiceModels = filteredInvvoices;
    }

    update();
  }

  void filterExpensesByDates(
      {required DateTime startDate, required DateTime endDate}) {
    isFiltered = true;
    filteredExpenses.clear();
    for (RepairOrderExpenseModel invoice in totalIncomeExpenses) {
      DateTime completionDate =
          DateTime.tryParse(invoice.expenseModel?.expenseDate! ?? "") ??
              DateTime.now();
      print(completionDate);
      completionDate = DateTime(
          completionDate.year, completionDate.month, completionDate.day);
      if (completionDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
          completionDate.isBefore(endDate.add(const Duration(days: 1)))) {
        filteredExpenses.add(invoice);
      }
    }

    if (filteredExpenses.isNotEmpty) {
      searchedExpenseModels = filteredExpenses;
    }

    update();
  }

  void clearFilter() {
    isFiltered = false;
    searchedRepairInvoiceModels.clear();
    searchedExpenseModels.clear();
    update();
  }

  void filterOrdersByDates(
      {required DateTime startDate, required DateTime endDate}) {
    List<RepairOrderExpenseModel> filteredExpenses = [];

    for (RepairOrderExpenseModel repairOrderExpenseModel
        in totalIncomeExpenses) {
      DateTime createdAt = DateTime.parse(
          repairOrderExpenseModel.expenseModel?.expenseDate ?? "");
      if (createdAt.isAfter(startDate.subtract(const Duration(days: 1))) &&
          createdAt.isBefore(endDate.add(const Duration(days: 1)))) {
        filteredExpenses.add(repairOrderExpenseModel);
      }
    }

    incomeExpenses = filteredExpenses;

    update();
  }
}
