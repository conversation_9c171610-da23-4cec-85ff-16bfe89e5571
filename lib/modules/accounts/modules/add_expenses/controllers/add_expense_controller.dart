import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/selected_images_model.dart';
import 'package:speed_force_franchise/models/vendor_model.dart';
import 'package:speed_force_franchise/modules/accounts/modules/add_expenses/models/expense_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/repositories/repair_orders_repository.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';

class AddExpenseController extends GetxController {
  AddExpenseController(
      {required this.homeScreenController, required this.isPartPurchase});

  bool isPartPurchase;

  HomeScreenController homeScreenController;

  TextEditingController expenseLabelTextEditingController =
      TextEditingController();
  TextEditingController vendorTextEditingController = TextEditingController();
  TextEditingController repairOrderTextEditingController =
      TextEditingController();
  TextEditingController totalAmountTextEditingController =
      TextEditingController();

  TextEditingController commentTextEditingController = TextEditingController();
  TextEditingController referenceNumberTextEditingController =
      TextEditingController();
  TextEditingController advancePaymentTextEditingController =
      TextEditingController();

  DateTime expenseDate = DateTime.now();

  TextEditingController expenseDateTextEditingController =
      TextEditingController()
        ..text = DateFormat("MMM d y").format(DateTime.now());

  DateTime paymentDate = DateTime.now();

  TextEditingController paymentDateTextEditingController =
      TextEditingController()
        ..text = DateFormat("MMM d y").format(DateTime.now());

  TextEditingController paymentModeTextEditingController =
      TextEditingController()..text = PaymentMode.cash.name;

  final OverlayPortalController expensesLabelOverlayPortalController =
      OverlayPortalController();

  final OverlayPortalController repairOrdersOverlayPortalController =
      OverlayPortalController();

  List<String> expenseLabels = [];
  String? selectedExpenseLabel;

  VendorModel? selectedVendor;

  List<RepairOrderModel> allRepairOrders = [];
  RepairOrderModel? selectedRepairOrder;

  ExpenseMode expenseMode = ExpenseMode.paid;
  PaymentMode paymentMode = PaymentMode.cash;

  List<SelectedImageModel> images = [];

  Future<void> fetchExpensesLabels() async {
    DocumentSnapshot<Map<String, dynamic>> expenseLabelsSnapshot =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.expenseLabels.name)
            .doc(FirebaseAuth.instance.currentUser!.uid)
            .get();

    Map<String, dynamic>? expenseLabelsMap = expenseLabelsSnapshot.data();

    if (expenseLabelsMap != null) {
      expenseLabels = List.from(expenseLabelsMap["expenseLabels"]);
    }

    update();
  }

  Future<void> fetchRepairOrders() async {
    allRepairOrders = await RepairOrdersRepository.fetchAllRepairOrders();
    update();
  }

  Future<void> addNewExpenseLabelInDatabase(
      {required String newExpenseLabel}) async {
    String? alreadyExistedLabel =
        expenseLabels.firstWhereOrNull((expenseLabel) {
      return expenseLabel.toLowerCase() == newExpenseLabel.toLowerCase();
    });

    if (alreadyExistedLabel != null) {
      Utils.showSnackBar(title: "Label already existed");
      return;
    } else {
      expenseLabels.add(newExpenseLabel ?? "");
    }

    await FirebaseFirestore.instance
        .collection(FirebaseCollections.expenseLabels.name)
        .doc(FirebaseAuth.instance.currentUser?.uid ?? "")
        .set({"expenseLabels": expenseLabels});
  }

  void changeSelectedExpenseLabel({required String label}) {
    selectedExpenseLabel = expenseLabelTextEditingController.text = label;
    update();
  }

  void changeSelectedRepairOrder({required String orderId}) {
    RepairOrderModel? repairOrderModel = allRepairOrders.firstWhereOrNull((el) {
      return el.orderId == orderId;
    });

    if (repairOrderModel != null) {
      selectedRepairOrder = repairOrderModel;
    }

    repairOrderTextEditingController.text = repairOrderModel?.orderId ?? "";

    update();
  }

  void toggleExpenseMode({required ExpenseMode expenseMode}) {
    this.expenseMode = expenseMode;
    update();
  }

  Future<List<String>> uploadExpenseImages({required String expenseId}) async {
    List<String> imagesLink = await homeScreenController.uploadImages(
        folderPath:
            '${FirebaseAuth.instance.currentUser?.uid}/${selectedRepairOrder?.customerDetailsModel?.username?.replaceAll(' ', '_')}/${selectedRepairOrder?.vehicleDetailsModel?.make?.replaceAll(' ', '_')}_${selectedRepairOrder?.vehicleDetailsModel?.model?.replaceAll(' ', '_')}/repair_orders/${selectedRepairOrder?.orderId}/${isPartPurchase ? "partsPurchases" : "expenses"}/$expenseId',
        imagesToUpload: images ?? []);

    return imagesLink;
  }

  Future<void> uploadExpenseInDatabase() async {
    ExpenseModel expenseModel = ExpenseModel();
    expenseModel.franchiseId = FirebaseAuth.instance.currentUser?.uid;
    expenseModel.expenseLabel = expenseLabelTextEditingController.text;
    expenseModel.vendorId = selectedVendor?.vendorId;
    expenseModel.repairOrderId = selectedRepairOrder?.orderId;
    expenseModel.totalAmount =
        double.tryParse(totalAmountTextEditingController.text);
    expenseModel.comment = commentTextEditingController.text;
    expenseModel.expenseDate = expenseDate.toIso8601String();
    expenseModel.referenceNumber = referenceNumberTextEditingController.text;
    expenseModel.expenseMode = expenseMode;
    if (expenseMode == ExpenseMode.credit) {
      expenseModel.advancePaid =
          double.tryParse(advancePaymentTextEditingController.text);
    }
    expenseModel.paymentMode = paymentMode;
    expenseModel.expenseDate = expenseDate.toString();

    CollectionReference<Map<String, dynamic>> expensesCollection =
        FirebaseFirestore.instance.collection(isPartPurchase
            ? FirebaseCollections.partsPurchases.name
            : FirebaseCollections.expenses.name);

    DocumentReference<Map<String, dynamic>> newExpense =
        await expensesCollection.add(expenseModel.toMap());
    if (isPartPurchase) {
      expenseModel.partPurchaseId = newExpense.id;
    } else {
      expenseModel.expenseId = newExpense.id;
    }

    if (images.isNotEmpty) {
      expenseModel.imagesLinks =
          await uploadExpenseImages(expenseId: expenseModel.expenseId ?? "");

      await expensesCollection
          .doc(expenseModel.expenseId)
          .set(expenseModel.toMap());
    }
  }
}
