import 'dart:convert';

import 'package:speed_force_franchise/enums/enums.dart';

class ExpenseModel {
  String? title;
  String? partPurchaseId;
  String? expenseId;
  String? franchiseId;
  String? expenseLabel;
  String? vendorId;
  String? repairOrderId;
  double? totalAmount;
  String? comment;
  String? expenseDate;
  String? referenceNumber;
  ExpenseMode? expenseMode;
  double? advancePaid;
  PaymentMode? paymentMode;
  String? paymentDate;
  List<String>? imagesLinks;
  ExpenseModel({
    this.title,
    this.expenseId,
    this.franchiseId,
    this.expenseLabel,
    this.vendorId,
    this.repairOrderId,
    this.totalAmount,
    this.comment,
    this.expenseDate,
    this.referenceNumber,
    this.expenseMode,
    this.advancePaid,
    this.paymentMode,
    this.paymentDate,
    this.imagesLinks,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (title != null) {
      result.addAll({'title': title});
    }
    if (expenseId != null) {
      result.addAll({'expenseId': expenseId});
    }
    if (franchiseId != null) {
      result.addAll({'franchiseId': franchiseId});
    }
    if (expenseLabel != null) {
      result.addAll({'expenseLabel': expenseLabel});
    }
    if (vendorId != null) {
      result.addAll({'vendorId': vendorId});
    }
    if (repairOrderId != null) {
      result.addAll({'repairOrderId': repairOrderId});
    }
    if (totalAmount != null) {
      result.addAll({'totalAmount': totalAmount});
    }
    if (comment != null) {
      result.addAll({'comment': comment});
    }
    if (expenseDate != null) {
      result.addAll({'expenseDate': expenseDate});
    }
    if (referenceNumber != null) {
      result.addAll({'referenceNumber': referenceNumber});
    }
    if (expenseMode != null) {
      result.addAll({'expenseMode': expenseMode?.name});
    }
    if (advancePaid != null) {
      result.addAll({'advancePaid': advancePaid});
    }
    if (paymentMode != null) {
      result.addAll({'paymentMode': paymentMode?.name});
    }
    if (paymentDate != null) {
      result.addAll({'paymentDate': paymentDate});
    }
    if (imagesLinks != null) {
      result.addAll({'imagesLinks': imagesLinks});
    }

    return result;
  }

  factory ExpenseModel.fromMap(Map<String, dynamic> map) {
    return ExpenseModel(
      title: map['title'],
      expenseId: map['expenseId'],
      franchiseId: map['franchiseId'],
      expenseLabel: map['expenseLabel'],
      vendorId: map['vendorId'],
      repairOrderId: map['repairOrderId'],
      totalAmount: map['totalAmount']?.toDouble(),
      comment: map['comment'],
      expenseDate: map['expenseDate'],
      referenceNumber: map['referenceNumber'],
      expenseMode: map['expenseMode'] != null
          ? ExpenseMode.paid.getFromName(map['expenseMode'])
          : null,
      advancePaid: map['advancePaid']?.toDouble(),
      paymentMode: map['paymentMode'] != null
          ? PaymentMode.cash.getFromName(map['paymentMode'])
          : null,
      paymentDate: map['paymentDate'],
      imagesLinks: map['imagesLinks'] != null
          ? List<String>.from(map['imagesLinks'])
          : [],
    );
  }

  String toJson() => json.encode(toMap());

  factory ExpenseModel.fromJson(String source) =>
      ExpenseModel.fromMap(json.decode(source));
}
