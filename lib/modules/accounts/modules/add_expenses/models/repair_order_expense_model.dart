import 'dart:convert';

import 'package:speed_force_franchise/modules/accounts/modules/add_expenses/models/expense_model.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';

class RepairOrderExpenseModel {
  RepairOrderModel? repairOrderModel;
  ExpenseModel? expenseModel;
  RepairOrderExpenseModel({
    required this.repairOrderModel,
    this.expenseModel,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'repairOrderModel': repairOrderModel?.toMap()});
    result.addAll({'expenseModel': expenseModel?.toMap()});

    return result;
  }

  factory RepairOrderExpenseModel.fromMap(Map<String, dynamic> map) {
    return RepairOrderExpenseModel(
      repairOrderModel: RepairOrderModel.fromMap(null, map['repairOrderModel']),
      expenseModel: ExpenseModel.fromMap(map['expenseModel']),
    );
  }

  String toJson() => json.encode(toMap());

  factory RepairOrderExpenseModel.fromJson(String source) =>
      RepairOrderExpenseModel.fromMap(json.decode(source));
}
