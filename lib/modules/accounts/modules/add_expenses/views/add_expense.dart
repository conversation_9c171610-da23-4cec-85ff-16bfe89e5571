import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/selected_images_model.dart';
import 'package:speed_force_franchise/models/vendor_model.dart';
import 'package:speed_force_franchise/modules/accounts/controllers/accounts_controller.dart';
import 'package:speed_force_franchise/modules/accounts/modules/add_expenses/controllers/add_expense_controller.dart';
import 'package:speed_force_franchise/modules/accounts/modules/add_expenses/models/expense_model.dart';
import 'package:speed_force_franchise/modules/accounts/modules/add_expenses/widgets/paid_credit_button.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/orders/widgets/custom_overlay.dart';
import 'package:speed_force_franchise/modules/orders/widgets/load_media_container.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

class AddExpense extends StatefulWidget {
  const AddExpense({super.key, required this.isPartPurchase});

  final bool isPartPurchase;

  @override
  State<AddExpense> createState() => _AddExpenseState();
}

class _AddExpenseState extends State<AddExpense> {
  DateTime expenseDate = DateTime.now();
  DateTime paymentDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    Get.put(AddExpenseController(
        isPartPurchase: widget.isPartPurchase,
        homeScreenController: Get.find<HomeScreenController>())
      ..fetchExpensesLabels()
      ..fetchRepairOrders());
  }

  @override
  void dispose() {
    Get.delete<AddExpenseController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AddExpenseController>(builder: (addExpenseController) {
      return GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Scaffold(
          appBar: AppBar(
            title: Text(
              addExpenseController.isPartPurchase
                  ? "Part Expense"
                  : "Add Expense",
            ),
            elevation: 0.5,
            backgroundColor: colorsConstants.whiteColor,
            shadowColor: colorsConstants.whiteColor,
            surfaceTintColor: colorsConstants.whiteColor,
          ),
          body: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        HeightBox(20.h),
                        if (addExpenseController.isPartPurchase) ...[
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                "PART PURCHASE",
                                style: TextStyle(fontSize: 24.sp),
                              ),
                            ],
                          ),
                          HeightBox(10.h),
                        ],
                        if (!addExpenseController.isPartPurchase) ...[
                          const Text("Expense Label* :"),
                          HeightBox(5.h),
                          CustomTextField(
                            hintText: "Enter Label",
                            // keyboardType: TextInputType.number,
                            controller: addExpenseController
                                .expenseLabelTextEditingController,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 10.w, vertical: 10.h),
                            filled: true,
                            fillColor: colorsConstants.whiteColor,
                          ),
                          // const Text("Expense Label* :"),
                          // HeightBox(5.h),
                          // CustomTextField(
                          //   hintText: "Select expense label",
                          //   controller: addExpenseController
                          //       .expenseLabelTextEditingController,
                          //   readOnly: true,
                          //   contentPadding: EdgeInsets.symmetric(
                          //       horizontal: 10.w, vertical: 10.h),
                          //   suffix: const Icon(CupertinoIcons.chevron_down),
                          //   filled: true,
                          //   fillColor: colorsConstants.whiteColor,
                          //   onTap: () {
                          //     addExpenseController
                          //         .expensesLabelOverlayPortalController
                          //         .show();
                          //   },
                          // ),
                          HeightBox(10.h),
                        ],
                        HeightBox(5.h),
                        const Text("Total Amount* :"),
                        HeightBox(5.h),
                        CustomTextField(
                          hintText: "Total expense amount...",
                          keyboardType: TextInputType.number,
                          controller: addExpenseController
                              .totalAmountTextEditingController,
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 10.w, vertical: 10.h),
                          filled: true,
                          fillColor: colorsConstants.whiteColor,
                        ),
                        HeightBox(10.h),
                        const Text("Description* :"),
                        HeightBox(5.h),
                        CustomTextField(
                          hintText: "Description here...",
                          controller:
                              addExpenseController.commentTextEditingController,
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 10.w, vertical: 10.h),
                          filled: true,
                          fillColor: colorsConstants.whiteColor,
                        ),
                        HeightBox(10.h),
                        const Text("Expense Date :"),
                        HeightBox(5.h),
                        CustomTextField(
                          hintText: "Expense date...",
                          readOnly: true,
                          controller: addExpenseController
                              .expenseDateTextEditingController,
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 10.w, vertical: 10.h),
                          filled: true,
                          fillColor: colorsConstants.whiteColor,
                          suffix: const Icon(CupertinoIcons.chevron_down),
                          onTap: () {
                            showCupertinoModalPopup(
                              context: context,
                              builder: (context) {
                                return Container(
                                  height: 300.h,
                                  color: Colors.white,
                                  child: Column(
                                    children: [
                                      Expanded(
                                        child: CupertinoDatePicker(
                                          initialDateTime: expenseDate,
                                          mode: CupertinoDatePickerMode.date,
                                          onDateTimeChanged: (DateTime value) {
                                            expenseDate = value;
                                          },
                                        ),
                                      ),
                                      HeightBox(10.h),
                                      Container(
                                        margin: EdgeInsets.symmetric(
                                            horizontal: 20.w),
                                        width: double.maxFinite,
                                        child: PrimaryButton(
                                            onPress: () {
                                              addExpenseController.expenseDate =
                                                  expenseDate;

                                              addExpenseController
                                                  .expenseDateTextEditingController
                                                  .text = DateFormat(
                                                      "MMM d y")
                                                  .format(addExpenseController
                                                      .expenseDate);

                                              Navigator.pop(context);
                                            },
                                            title: "Add"),
                                      ),
                                      HeightBox(20.h),
                                    ],
                                  ),
                                );
                              },
                            );
                          },
                        ),
                        // HeightBox(10.h),
                        // const Text("Payment Channel :"),
                        // HeightBox(5.h),
                        // CustomTextField(
                        //   controller: addExpenseController
                        //       .paymentModeTextEditingController,
                        //   readOnly: true,
                        //   contentPadding: EdgeInsets.symmetric(
                        //       horizontal: 10.w, vertical: 10.h),
                        //   suffix: const Icon(CupertinoIcons.chevron_down),
                        //   filled: true,
                        //   fillColor: colorsConstants.whiteColor,
                        //   onTap: () {
                        //     showDialog(
                        //       context: context,
                        //       builder: (context) {
                        //         return selectPaymentModeDialog(context);
                        //       },
                        //     );
                        //   },
                        // ),
                        HeightBox(10.h),
                        const Text("Payment Date :"),
                        HeightBox(5.h),
                        CustomTextField(
                          readOnly: true,
                          controller: addExpenseController
                              .paymentDateTextEditingController,
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 10.w, vertical: 10.h),
                          filled: true,
                          fillColor: colorsConstants.whiteColor,
                          suffix: const Icon(CupertinoIcons.chevron_down),
                          onTap: () {
                            showCupertinoModalPopup(
                              context: context,
                              builder: (context) {
                                return Container(
                                  height: 300.h,
                                  color: Colors.white,
                                  child: Column(
                                    children: [
                                      Expanded(
                                        child: CupertinoDatePicker(
                                          initialDateTime: paymentDate,
                                          mode: CupertinoDatePickerMode.date,
                                          onDateTimeChanged: (DateTime value) {
                                            paymentDate = value;
                                          },
                                        ),
                                      ),
                                      HeightBox(10.h),
                                      Container(
                                        margin: EdgeInsets.symmetric(
                                            horizontal: 20.w),
                                        width: double.maxFinite,
                                        child: PrimaryButton(
                                            onPress: () {
                                              addExpenseController.paymentDate =
                                                  paymentDate;

                                              addExpenseController
                                                  .paymentDateTextEditingController
                                                  .text = DateFormat(
                                                      "MMM d y")
                                                  .format(addExpenseController
                                                      .paymentDate);

                                              Navigator.pop(context);
                                            },
                                            title: "Add"),
                                      ),
                                      HeightBox(20.h),
                                    ],
                                  ),
                                );
                              },
                            );
                          },
                        ),
                        HeightBox(20.h),
                      ],
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(vertical: 20.h),
                  width: double.maxFinite,
                  child: PrimaryButton(
                    onPress: () async {
                      try {
                        if ((!addExpenseController.isPartPurchase &&
                                addExpenseController
                                    .expenseLabelTextEditingController
                                    .text
                                    .isEmpty) ||
                            addExpenseController
                                .totalAmountTextEditingController
                                .text
                                .isEmpty ||
                            addExpenseController
                                .commentTextEditingController.text.isEmpty) {
                          Utils.showSnackBar(
                            title: "Fields with * are mandatory...",
                          );
                          return;
                        }
                        context.loaderOverlay.show(
                            progress: addExpenseController.isPartPurchase
                                ? 'Adding Part Purchase'
                                : "Adding New Expense...");

                        ExpenseModel expenseModel = ExpenseModel(
                            repairOrderId: null,
                            title: addExpenseController
                                .expenseLabelTextEditingController.text,
                            comment: addExpenseController
                                .commentTextEditingController.text,
                            paymentDate:
                                addExpenseController.paymentDate.toString(),
                            franchiseId: FirebaseAuth.instance.currentUser?.uid,
                            expenseDate:
                                addExpenseController.expenseDate.toString(),
                            // expenseMode: ExpenseMode.paid,
                            totalAmount: double.tryParse(addExpenseController
                                    .totalAmountTextEditingController.text) ??
                                0);
                        await FirebaseFirestore.instance
                            .collection(FirebaseCollections.expenses.name)
                            .add(expenseModel.toMap());
                        Get.find<AccountsController>().fetchIncomeExpenses();
                        if (context.mounted) {
                          context.loaderOverlay.hide();
                          Navigator.pop(context);
                        }
                      } catch (e) {
                        debugPrint(e.toString());
                        if (context.mounted) {
                          context.loaderOverlay.hide();
                        }
                      }
                    },
                    title: addExpenseController.isPartPurchase
                        ? "Save"
                        : "Save Expense",
                  ),
                ),
                OverlayPortal(
                  controller:
                      addExpenseController.expensesLabelOverlayPortalController,
                  overlayChildBuilder: (BuildContext context) {
                    return CustomOverlay(
                      title: "Select Expense Label",
                      onBackPress: () {
                        addExpenseController
                            .expensesLabelOverlayPortalController
                            .hide();
                      },
                      onSelected: (selectedExpenseLabel) {
                        addExpenseController
                            .expensesLabelOverlayPortalController
                            .hide();
                        addExpenseController.changeSelectedExpenseLabel(
                            label: selectedExpenseLabel);
                      },
                      dataSource: addExpenseController.expenseLabels,
                      addNewOnPress: () {
                        TextEditingController newLabelTextEditingController =
                            TextEditingController();
                        showDialog(
                          context: context,
                          builder: (context) {
                            return AlertDialog(
                              content: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "Add new label",
                                    style: TextStyle(fontSize: 20.sp),
                                  ),
                                  HeightBox(20.h),
                                  CustomTextField(
                                    hintText: "Enter new label here...",
                                    controller: newLabelTextEditingController,
                                  ),
                                  HeightBox(20.h),
                                  Row(
                                    children: [
                                      const Spacer(),
                                      PrimaryButton(
                                        onPress: () async {
                                          if (newLabelTextEditingController
                                              .text.isEmpty) {
                                            Utils.showSnackBar(
                                                title:
                                                    "Empty labels are not allowed");
                                            return;
                                          }

                                          try {
                                            context.loaderOverlay.show(
                                                progress: "Adding new label");

                                            await addExpenseController
                                                .addNewExpenseLabelInDatabase(
                                                    newExpenseLabel:
                                                        newLabelTextEditingController
                                                            .text);

                                            if (context.mounted) {
                                              Navigator.pop(context);
                                            }

                                            await addExpenseController
                                                .fetchExpensesLabels();

                                            if (context.mounted) {
                                              context.loaderOverlay.hide();
                                            }
                                          } catch (e) {
                                            if (context.mounted) {
                                              context.loaderOverlay.hide();
                                            }
                                          }
                                        },
                                        title: "Add",
                                      ),
                                    ],
                                  )
                                ],
                              ),
                            );
                          },
                        );
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget selectPaymentModeDialog(BuildContext context) {
    AddExpenseController addExpenseController =
        Get.find<AddExpenseController>();
    return AlertDialog(
      contentPadding: EdgeInsets.zero,
      content: ConstrainedBox(
        constraints: BoxConstraints(maxHeight: 300.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            HeightBox(10.h),
            Text(
              "Payment Mode",
              style: TextStyle(fontSize: 24.sp),
            ),
            const Divider(),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: PaymentMode.values.map(
                    (PaymentMode mode) {
                      return Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10.w),
                        child: InkWell(
                          onTap: () {
                            addExpenseController.paymentMode = mode;
                            addExpenseController
                                .paymentModeTextEditingController
                                .text = mode.name;
                            addExpenseController.update();
                            Navigator.pop(context);
                          },
                          child: Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(8.sp),
                                margin: EdgeInsets.symmetric(vertical: 10.sp),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: colorsConstants.primaryRed,
                                    width: 2.sp,
                                  ),
                                ),
                              ),
                              WidthBox(10.w),
                              Text(mode.name),
                            ],
                          ),
                        ),
                      );
                    },
                  ).toList(),
                ),
              ),
            ),
            // const Divider(),
            // PrimaryButton(
            //   onPress: () {
            //     moveToAddVendorScreen(
            //         context: context,
            //         revertCallback: (dynamic vendor) {
            //           if (vendor != null) {
            //             addExpenseController.selectedVendor =
            //                 (vendor as VendorModel);
            //             addExpenseController.vendorTextEditingController.text =
            //                 addExpenseController.selectedVendor?.name ?? "";
            //             Navigator.pop(context);
            //             addExpenseController.update();
            //             Get.find<HomeScreenController>().fetchVendors();
            //           }
            //         });
            //   },
            //   title: "Add New Vendor",
            // ),
            // HeightBox(10.h),
          ],
        ),
      ),
    );
  }
}
 

// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:loader_overlay/loader_overlay.dart';
// import 'package:speed_force_franchise/enums/enums.dart';
// import 'package:speed_force_franchise/models/selected_images_model.dart';
// import 'package:speed_force_franchise/models/vendor_model.dart';
// import 'package:speed_force_franchise/modules/accounts/controllers/accounts_controller.dart';
// import 'package:speed_force_franchise/modules/accounts/modules/add_expenses/controllers/add_expense_controller.dart';
// import 'package:speed_force_franchise/modules/accounts/modules/add_expenses/widgets/paid_credit_button.dart';
// import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
// import 'package:speed_force_franchise/modules/orders/widgets/custom_overlay.dart';
// import 'package:speed_force_franchise/modules/orders/widgets/load_media_container.dart';
// import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
// import 'package:speed_force_franchise/utils/common_exports.dart';
// import 'package:speed_force_franchise/utils/utils.dart';
// import 'package:speed_force_franchise/widgets/primary_button.dart';

// class AddExpense extends StatefulWidget {
//   const AddExpense({super.key, required this.isPartPurchase});

//   final bool isPartPurchase;

//   @override
//   State<AddExpense> createState() => _AddExpenseState();
// }

// class _AddExpenseState extends State<AddExpense> {
//   DateTime expenseDate = DateTime.now();
//   DateTime paymentDate = DateTime.now();

//   @override
//   void initState() {
//     super.initState();
//     Get.put(AddExpenseController(
//         isPartPurchase: widget.isPartPurchase,
//         homeScreenController: Get.find<HomeScreenController>())
//       ..fetchExpensesLabels()
//       ..fetchRepairOrders());
//   }

//   @override
//   void dispose() {
//     Get.delete<AddExpenseController>();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<AddExpenseController>(builder: (addExpenseController) {
//       return GestureDetector(
//         onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
//         child: Scaffold(
//           appBar: AppBar(
//             title: Text(
//               addExpenseController.isPartPurchase
//                   ? "Part Expense"
//                   : "Add Expense",
//             ),
//             elevation: 0.5,
//             backgroundColor: colorsConstants.whiteColor,
//             shadowColor: colorsConstants.whiteColor,
//             surfaceTintColor: colorsConstants.whiteColor,
//           ),
//           body: Container(
//             padding: EdgeInsets.symmetric(horizontal: 16.w),
//             child: Column(
//               children: [
//                 Expanded(
//                   child: SingleChildScrollView(
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         HeightBox(20.h),
//                         if (addExpenseController.isPartPurchase) ...[
//                           Row(
//                             mainAxisAlignment: MainAxisAlignment.center,
//                             children: [
//                               Text(
//                                 "PART PURCHASE",
//                                 style: TextStyle(fontSize: 24.sp),
//                               ),
//                             ],
//                           ),
//                           HeightBox(10.h),
//                         ],
//                         if (!addExpenseController.isPartPurchase) ...[
//                           const Text("Expense Label* :"),
//                           HeightBox(5.h),
//                           CustomTextField(
//                             hintText: "Select expense label",
//                             controller: addExpenseController
//                                 .expenseLabelTextEditingController,
//                             readOnly: true,
//                             contentPadding: EdgeInsets.symmetric(
//                                 horizontal: 10.w, vertical: 10.h),
//                             suffix: const Icon(CupertinoIcons.chevron_down),
//                             filled: true,
//                             fillColor: colorsConstants.whiteColor,
//                             onTap: () {
//                               addExpenseController
//                                   .expensesLabelOverlayPortalController
//                                   .show();
//                             },
//                           ),
//                           HeightBox(10.h),
//                         ],
//                         const Text("Vendor :"),
//                         HeightBox(5.h),
//                         CustomTextField(
//                           hintText: "Select a vendor...",
//                           controller:
//                               addExpenseController.vendorTextEditingController,
//                           readOnly: true,
//                           contentPadding: EdgeInsets.symmetric(
//                               horizontal: 10.w, vertical: 10.h),
//                           suffix: const Icon(CupertinoIcons.chevron_down),
//                           filled: true,
//                           fillColor: colorsConstants.whiteColor,
//                           onTap: () {
//                             showDialog(
//                               context: context,
//                               builder: (context) {
//                                 return selectVendorDialog(context);
//                               },
//                             );
//                           },
//                         ),
//                         HeightBox(10.h),
//                         const Text("Repair Order* :"),
//                         HeightBox(5.h),
//                         CustomTextField(
//                           hintText: "Select a repair order...",
//                           controller: addExpenseController
//                               .repairOrderTextEditingController,
//                           readOnly: true,
//                           contentPadding: EdgeInsets.symmetric(
//                               horizontal: 10.w, vertical: 10.h),
//                           suffix: const Icon(CupertinoIcons.chevron_down),
//                           filled: true,
//                           fillColor: colorsConstants.whiteColor,
//                           onTap: () {
//                             addExpenseController
//                                 .repairOrdersOverlayPortalController
//                                 .show();
//                           },
//                         ),
//                         HeightBox(10.h),
//                         const Text("Total Amount* :"),
//                         HeightBox(5.h),
//                         CustomTextField(
//                           hintText: "Total expense amount...",
//                           keyboardType: TextInputType.number,
//                           controller: addExpenseController
//                               .totalAmountTextEditingController,
//                           contentPadding: EdgeInsets.symmetric(
//                               horizontal: 10.w, vertical: 10.h),
//                           filled: true,
//                           fillColor: colorsConstants.whiteColor,
//                         ),
//                         HeightBox(10.h),
//                         const Text("Comment :"),
//                         HeightBox(5.h),
//                         CustomTextField(
//                           hintText: "Comment here...",
//                           controller:
//                               addExpenseController.commentTextEditingController,
//                           contentPadding: EdgeInsets.symmetric(
//                               horizontal: 10.w, vertical: 10.h),
//                           filled: true,
//                           fillColor: colorsConstants.whiteColor,
//                         ),
//                         HeightBox(10.h),
//                         const Text("Expense Date :"),
//                         HeightBox(5.h),
//                         CustomTextField(
//                           hintText: "Expense date...",
//                           readOnly: true,
//                           controller: addExpenseController
//                               .expenseDateTextEditingController,
//                           contentPadding: EdgeInsets.symmetric(
//                               horizontal: 10.w, vertical: 10.h),
//                           filled: true,
//                           fillColor: colorsConstants.whiteColor,
//                           suffix: const Icon(CupertinoIcons.chevron_down),
//                           onTap: () {
//                             showCupertinoModalPopup(
//                               context: context,
//                               builder: (context) {
//                                 return Container(
//                                   height: 300.h,
//                                   color: Colors.white,
//                                   child: Column(
//                                     children: [
//                                       Expanded(
//                                         child: CupertinoDatePicker(
//                                           initialDateTime: expenseDate,
//                                           mode: CupertinoDatePickerMode.date,
//                                           onDateTimeChanged: (DateTime value) {
//                                             expenseDate = value;
//                                           },
//                                         ),
//                                       ),
//                                       HeightBox(10.h),
//                                       Container(
//                                         margin: EdgeInsets.symmetric(
//                                             horizontal: 20.w),
//                                         width: double.maxFinite,
//                                         child: PrimaryButton(
//                                             onPress: () {
//                                               addExpenseController.expenseDate =
//                                                   expenseDate;

//                                               addExpenseController
//                                                   .expenseDateTextEditingController
//                                                   .text = DateFormat(
//                                                       "MMM d y")
//                                                   .format(addExpenseController
//                                                       .expenseDate);

//                                               Navigator.pop(context);
//                                             },
//                                             title: "Add"),
//                                       ),
//                                       HeightBox(20.h),
//                                     ],
//                                   ),
//                                 );
//                               },
//                             );
//                           },
//                         ),
//                         if (!addExpenseController.isPartPurchase) ...[
//                           HeightBox(10.h),
//                           const Text("Reference Number :"),
//                           HeightBox(5.h),
//                           CustomTextField(
//                             hintText: "Add reference number...",
//                             controller: addExpenseController
//                                 .referenceNumberTextEditingController,
//                             contentPadding: EdgeInsets.symmetric(
//                                 horizontal: 10.w, vertical: 10.h),
//                             filled: true,
//                             fillColor: colorsConstants.whiteColor,
//                           ),
//                         ],
//                         HeightBox(10.h),
//                         Row(
//                           children: [
//                             Expanded(
//                               child: PairCreditButton(
//                                 title: "Paid",
//                                 isSelected: addExpenseController.expenseMode ==
//                                     ExpenseMode.paid,
//                                 onTap: () {
//                                   addExpenseController.toggleExpenseMode(
//                                       expenseMode: ExpenseMode.paid);
//                                 },
//                               ),
//                             ),
//                             WidthBox(10.w),
//                             Expanded(
//                               child: PairCreditButton(
//                                 title: "Credit",
//                                 isSelected: addExpenseController.expenseMode ==
//                                     ExpenseMode.credit,
//                                 onTap: () {
//                                   addExpenseController.toggleExpenseMode(
//                                       expenseMode: ExpenseMode.credit);
//                                 },
//                               ),
//                             ),
//                           ],
//                         ),
//                         if (addExpenseController.expenseMode ==
//                             ExpenseMode.credit) ...[
//                           HeightBox(10.h),
//                           const Text("Advance Paid :"),
//                           HeightBox(5.h),
//                           CustomTextField(
//                             hintText: "Any advance payment...",
//                             keyboardType: TextInputType.number,
//                             controller: addExpenseController
//                                 .advancePaymentTextEditingController,
//                             contentPadding: EdgeInsets.symmetric(
//                                 horizontal: 10.w, vertical: 10.h),
//                             filled: true,
//                             fillColor: colorsConstants.whiteColor,
//                           ),
//                         ],
//                         HeightBox(10.h),
//                         const Text("Payment Channel :"),
//                         HeightBox(5.h),
//                         CustomTextField(
//                           controller: addExpenseController
//                               .paymentModeTextEditingController,
//                           readOnly: true,
//                           contentPadding: EdgeInsets.symmetric(
//                               horizontal: 10.w, vertical: 10.h),
//                           suffix: const Icon(CupertinoIcons.chevron_down),
//                           filled: true,
//                           fillColor: colorsConstants.whiteColor,
//                           onTap: () {
//                             showDialog(
//                               context: context,
//                               builder: (context) {
//                                 return selectPaymentModeDialog(context);
//                               },
//                             );
//                           },
//                         ),
//                         HeightBox(10.h),
//                         const Text("Payment Date :"),
//                         HeightBox(5.h),
//                         CustomTextField(
//                           readOnly: true,
//                           controller: addExpenseController
//                               .paymentDateTextEditingController,
//                           contentPadding: EdgeInsets.symmetric(
//                               horizontal: 10.w, vertical: 10.h),
//                           filled: true,
//                           fillColor: colorsConstants.whiteColor,
//                           suffix: const Icon(CupertinoIcons.chevron_down),
//                           onTap: () {
//                             showCupertinoModalPopup(
//                               context: context,
//                               builder: (context) {
//                                 return Container(
//                                   height: 300.h,
//                                   color: Colors.white,
//                                   child: Column(
//                                     children: [
//                                       Expanded(
//                                         child: CupertinoDatePicker(
//                                           initialDateTime: paymentDate,
//                                           mode: CupertinoDatePickerMode.date,
//                                           onDateTimeChanged: (DateTime value) {
//                                             paymentDate = value;
//                                           },
//                                         ),
//                                       ),
//                                       HeightBox(10.h),
//                                       Container(
//                                         margin: EdgeInsets.symmetric(
//                                             horizontal: 20.w),
//                                         width: double.maxFinite,
//                                         child: PrimaryButton(
//                                             onPress: () {
//                                               addExpenseController.paymentDate =
//                                                   paymentDate;

//                                               addExpenseController
//                                                   .paymentDateTextEditingController
//                                                   .text = DateFormat(
//                                                       "MMM d y")
//                                                   .format(addExpenseController
//                                                       .paymentDate);

//                                               Navigator.pop(context);
//                                             },
//                                             title: "Add"),
//                                       ),
//                                       HeightBox(20.h),
//                                     ],
//                                   ),
//                                 );
//                               },
//                             );
//                           },
//                         ),
//                         HeightBox(20.h),
//                         LoadMediaContainer(
//                           title: "Images",
//                           onMediaLoaded: (SelectedImageModel pickedMedia) {
//                             addExpenseController.images.add(pickedMedia);
//                             addExpenseController.update();
//                           },
//                           onRemoveMedia: (SelectedImageModel removedMedia) {
//                             addExpenseController.images.remove(removedMedia);
//                             addExpenseController.update();
//                           },
//                         ),
//                         HeightBox(20.h),
//                       ],
//                     ),
//                   ),
//                 ),
//                 Container(
//                   padding: EdgeInsets.symmetric(vertical: 20.h),
//                   width: double.maxFinite,
//                   child: PrimaryButton(
//                     onPress: () async {
//                       try {
//                         if ((!addExpenseController.isPartPurchase &&
//                                 addExpenseController
//                                     .expenseLabelTextEditingController
//                                     .text
//                                     .isEmpty) ||
//                             addExpenseController.selectedRepairOrder == null ||
//                             addExpenseController
//                                 .totalAmountTextEditingController
//                                 .text
//                                 .isEmpty) {
//                           Utils.showSnackBar(
//                             title: "Fields with * are mandatory...",
//                           );
//                           return;
//                         }
//                         context.loaderOverlay.show(
//                             progress: addExpenseController.isPartPurchase
//                                 ? 'Adding Part Purchase'
//                                 : "Adding New Expense...");
//                         await addExpenseController.uploadExpenseInDatabase();
//                         Get.find<AccountsController>().update();
//                         if (context.mounted) {
//                           context.loaderOverlay.hide();
//                           Navigator.pop(context);
//                         }
//                       } catch (e) {
//                         debugPrint(e.toString());
//                         if (context.mounted) {
//                           context.loaderOverlay.hide();
//                         }
//                       }
//                     },
//                     title: addExpenseController.isPartPurchase
//                         ? "Save"
//                         : "Save Expense",
//                   ),
//                 ),
//                 OverlayPortal(
//                   controller:
//                       addExpenseController.expensesLabelOverlayPortalController,
//                   overlayChildBuilder: (BuildContext context) {
//                     return CustomOverlay(
//                       title: "Select Expense Label",
//                       onBackPress: () {
//                         addExpenseController
//                             .expensesLabelOverlayPortalController
//                             .hide();
//                       },
//                       onSelected: (selectedExpenseLabel) {
//                         addExpenseController
//                             .expensesLabelOverlayPortalController
//                             .hide();
//                         addExpenseController.changeSelectedExpenseLabel(
//                             label: selectedExpenseLabel);
//                       },
//                       dataSource: addExpenseController.expenseLabels,
//                       addNewOnPress: () {
//                         TextEditingController newLabelTextEditingController =
//                             TextEditingController();
//                         showDialog(
//                           context: context,
//                           builder: (context) {
//                             return AlertDialog(
//                               content: Column(
//                                 mainAxisSize: MainAxisSize.min,
//                                 crossAxisAlignment: CrossAxisAlignment.start,
//                                 children: [
//                                   Text(
//                                     "Add new label",
//                                     style: TextStyle(fontSize: 20.sp),
//                                   ),
//                                   HeightBox(20.h),
//                                   CustomTextField(
//                                     hintText: "Enter new label here...",
//                                     controller: newLabelTextEditingController,
//                                   ),
//                                   HeightBox(20.h),
//                                   Row(
//                                     children: [
//                                       const Spacer(),
//                                       PrimaryButton(
//                                         onPress: () async {
//                                           if (newLabelTextEditingController
//                                               .text.isEmpty) {
//                                             Utils.showSnackBar(
//                                                 title:
//                                                     "Empty labels are not allowed");
//                                             return;
//                                           }
//                                           try {
//                                             context.loaderOverlay.show(
//                                                 progress: "Adding new label");

//                                             await addExpenseController
//                                                 .addNewExpenseLabelInDatabase(
//                                                     newExpenseLabel:
//                                                         newLabelTextEditingController
//                                                             .text);

//                                             if (context.mounted) {
//                                               Navigator.pop(context);
//                                             }

//                                             await addExpenseController
//                                                 .fetchExpensesLabels();

//                                             if (context.mounted) {
//                                               context.loaderOverlay.hide();
//                                             }
//                                           } catch (e) {
//                                             if (context.mounted) {
//                                               context.loaderOverlay.hide();
//                                             }
//                                           }
//                                         },
//                                         title: "Add",
//                                       ),
//                                     ],
//                                   )
//                                 ],
//                               ),
//                             );
//                           },
//                         );
//                       },
//                     );
//                   },
//                 ),
//                 OverlayPortal(
//                   controller:
//                       addExpenseController.repairOrdersOverlayPortalController,
//                   overlayChildBuilder: (BuildContext context) {
//                     return CustomOverlay(
//                       title: "Select Repair Order",
//                       onBackPress: () {
//                         addExpenseController.repairOrdersOverlayPortalController
//                             .hide();
//                       },
//                       onSelected: (selectedExpenseLabel) {
//                         addExpenseController.repairOrdersOverlayPortalController
//                             .hide();

//                         String orderId = selectedExpenseLabel
//                             .split("\n")
//                             .first
//                             .replaceAll("(", "")
//                             .replaceAll(")", "");

//                         addExpenseController.changeSelectedRepairOrder(
//                             orderId: orderId);
//                       },
//                       dataSource: addExpenseController.allRepairOrders
//                           .fold(<String>[], (previousValue, element) {
//                         String data = """
// (${element.orderId})
// ${element.customerDetailsModel?.username}
// ${element.vehicleDetailsModel?.make} ${element.vehicleDetailsModel?.model}""";
//                         previousValue.add(data);
//                         return previousValue;
//                       }),
//                     );
//                   },
//                 ),
//               ],
//             ),
//           ),
//         ),
//       );
//     });
//   }

//   Widget selectPaymentModeDialog(BuildContext context) {
//     AddExpenseController addExpenseController =
//         Get.find<AddExpenseController>();
//     return AlertDialog(
//       contentPadding: EdgeInsets.zero,
//       content: ConstrainedBox(
//         constraints: BoxConstraints(maxHeight: 300.h),
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             HeightBox(10.h),
//             Text(
//               "Payment Mode",
//               style: TextStyle(fontSize: 24.sp),
//             ),
//             const Divider(),
//             Expanded(
//               child: SingleChildScrollView(
//                 child: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: PaymentMode.values.map(
//                     (PaymentMode mode) {
//                       return Padding(
//                         padding: EdgeInsets.symmetric(horizontal: 10.w),
//                         child: InkWell(
//                           onTap: () {
//                             addExpenseController.paymentMode = mode;
//                             addExpenseController
//                                 .paymentModeTextEditingController
//                                 .text = mode.name;
//                             addExpenseController.update();
//                             Navigator.pop(context);
//                           },
//                           child: Row(
//                             children: [
//                               Container(
//                                 padding: EdgeInsets.all(8.sp),
//                                 margin: EdgeInsets.symmetric(vertical: 10.sp),
//                                 decoration: BoxDecoration(
//                                   shape: BoxShape.circle,
//                                   border: Border.all(
//                                     color: colorsConstants.primaryRed,
//                                     width: 2.sp,
//                                   ),
//                                 ),
//                               ),
//                               WidthBox(10.w),
//                               Text(mode.name),
//                             ],
//                           ),
//                         ),
//                       );
//                     },
//                   ).toList(),
//                 ),
//               ),
//             ),
//             // const Divider(),
//             // PrimaryButton(
//             //   onPress: () {
//             //     moveToAddVendorScreen(
//             //         context: context,
//             //         revertCallback: (dynamic vendor) {
//             //           if (vendor != null) {
//             //             addExpenseController.selectedVendor =
//             //                 (vendor as VendorModel);
//             //             addExpenseController.vendorTextEditingController.text =
//             //                 addExpenseController.selectedVendor?.name ?? "";
//             //             Navigator.pop(context);
//             //             addExpenseController.update();
//             //             Get.find<HomeScreenController>().fetchVendors();
//             //           }
//             //         });
//             //   },
//             //   title: "Add New Vendor",
//             // ),
//             // HeightBox(10.h),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget selectVendorDialog(BuildContext context) {
//     AddExpenseController addExpenseController =
//         Get.find<AddExpenseController>();
//     return AlertDialog(
//       contentPadding: EdgeInsets.zero,
//       content: ConstrainedBox(
//         constraints: BoxConstraints(maxHeight: 300.h),
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             HeightBox(10.h),
//             Text(
//               "Preferred Vendor",
//               style: TextStyle(fontSize: 24.sp),
//             ),
//             const Divider(),
//             Expanded(
//               child: SingleChildScrollView(
//                 child: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children:
//                       addExpenseController.homeScreenController.vendors.map(
//                     (VendorModel vendorModel) {
//                       return Padding(
//                         padding: EdgeInsets.symmetric(horizontal: 10.w),
//                         child: InkWell(
//                           onTap: () {
//                             addExpenseController.selectedVendor = vendorModel;
//                             addExpenseController.vendorTextEditingController
//                                 .text = vendorModel.name ?? "";
//                             addExpenseController.update();
//                             Navigator.pop(context);
//                           },
//                           child: Row(
//                             children: [
//                               Container(
//                                 padding: EdgeInsets.all(8.sp),
//                                 margin: EdgeInsets.symmetric(vertical: 10.sp),
//                                 decoration: BoxDecoration(
//                                   shape: BoxShape.circle,
//                                   border: Border.all(
//                                     color: colorsConstants.primaryRed,
//                                     width: 2.sp,
//                                   ),
//                                 ),
//                               ),
//                               WidthBox(10.w),
//                               Text(vendorModel.name ?? ""),
//                             ],
//                           ),
//                         ),
//                       );
//                     },
//                   ).toList(),
//                 ),
//               ),
//             ),
//             const Divider(),
//             PrimaryButton(
//               onPress: () {
//                 moveToAddVendorScreen(
//                     context: context,
//                     revertCallback: (dynamic vendor) {
//                       if (vendor != null) {
//                         addExpenseController.selectedVendor =
//                             (vendor as VendorModel);
//                         addExpenseController.vendorTextEditingController.text =
//                             addExpenseController.selectedVendor?.name ?? "";
//                         Navigator.pop(context);
//                         addExpenseController.update();
//                         Get.find<HomeScreenController>().fetchVendors();
//                       }
//                     });
//               },
//               title: "Add New Vendor",
//             ),
//             HeightBox(10.h),
//           ],
//         ),
//       ),
//     );
//   }
// }
