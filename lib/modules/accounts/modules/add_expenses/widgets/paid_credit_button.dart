import 'package:flutter/material.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class PairCreditButton extends StatelessWidget {
  const PairCreditButton({
    super.key,
    required this.title,
    required this.isSelected,
    required this.onTap,
  });

  final String title;
  final bool isSelected;
  final void Function() onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 5.h),
        decoration: BoxDecoration(
          color: isSelected
              ? colorsConstants.primaryRed
              : colorsConstants.whiteColor,
          boxShadow: Constants.boxShadow,
        ),
        child: Text(
          title,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16.sp,
            color: isSelected
                ? colorsConstants.whiteColor
                : colorsConstants.hintGrey,
          ),
        ),
      ),
    );
  }
}
