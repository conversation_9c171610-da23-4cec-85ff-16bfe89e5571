import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/accounts/controllers/accounts_controller.dart';
import 'package:speed_force_franchise/modules/accounts/modules/add_expenses/models/repair_order_expense_model.dart';
import 'package:speed_force_franchise/modules/accounts/views/expense.dart';
import 'package:speed_force_franchise/modules/accounts/views/income.dart';
import 'package:speed_force_franchise/modules/orders/controllers/view_orders_controller.dart';
import 'package:speed_force_franchise/modules/parts/views/parts.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

class Accounts extends StatefulWidget {
  const Accounts({super.key});

  @override
  State<Accounts> createState() => _AccountsState();
}

class _AccountsState extends State<Accounts> {
  @override
  void initState() {
    super.initState();
    Get.put(
      AccountsController()
        ..fetchIncomeExpenses()
        ..fetchInvoices(),
    );
  }

  @override
  void dispose() {
    // Get.delete<AccountsController>();
    super.dispose();
  }

  double income = 0.0;
  double expense = 0.0;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<AccountsController>(builder: (accountsController) {
      income = (accountsController.totalIncome.fold<double>(0.0,
          (previousValue, element) {
        if (element.repairOrderModel?.orderStatus == OrderStatus.completed) {
          return previousValue +
                  (element.repairOrderModel?.repairDetailsModel!.total ?? 0) ??
              0.0;
        }
        return previousValue;
      }));
      expense = accountsController.totalIncomeExpenses.fold<double>(
        0.0,
        (previousValue, element) {
          return previousValue + (element.expenseModel?.totalAmount ?? 0.0);
        },
      );
      print(expense);
      return Column(
        children: [
          HeightBox(20.h),
          Center(
            child: InkWell(
              onTap: () async {
                showDialog(
                  context: context,
                  builder: (context) {
                    return filterDialog(context);
                  },
                );
                accountsController.filterExpensesByDates(
                    startDate: accountsController.startDate,
                    endDate: accountsController.endDate);
                accountsController.filterExpensesByDates(
                    startDate: accountsController.startDate,
                    endDate: accountsController.endDate);
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 2.w),
                decoration: BoxDecoration(
                  color: colorsConstants.hintGrey,
                  borderRadius: BorderRadius.circular(5.r),
                ),
                child: Text(
                  accountsController.selectedDateRange.name,
                  style: TextStyle(
                    color: colorsConstants.whiteColor,
                    fontSize: 12.sp,
                  ),
                ),
              ),
            ),
          ),
          HeightBox(5.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                DateFormat("d MMM y").format(accountsController.startDate),
                style: TextStyle(
                  color: colorsConstants.hintGrey,
                  fontSize: 12.sp,
                ),
              ),
              Text(
                " - ",
                style: TextStyle(
                  color: colorsConstants.hintGrey,
                  fontSize: 12.sp,
                ),
              ),
              Text(
                DateFormat("d MMM y").format(accountsController.endDate),
                style: TextStyle(
                  color: colorsConstants.hintGrey,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
          HeightBox(10.h),
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      accountsController.changeSelectedTab(changedTab: 0);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      padding: EdgeInsets.only(bottom: 5.h),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            width: 1.sp,
                            color: accountsController.selectedTab == 0
                                ? colorsConstants.primaryRed
                                : colorsConstants.slateGrey,
                          ),
                        ),
                      ),
                      child: Text(
                        "Expense",
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: accountsController.selectedTab == 0
                              ? colorsConstants.blackColor
                              : colorsConstants.hintGrey,
                        ),
                      ),
                    ),
                  ),
                ),
                // Expanded(
                //   child: GestureDetector(
                //     onTap: () {
                //       accountsController.changeSelectedTab(changedTab: 1);
                //     },
                //     child: Container(
                //       alignment: Alignment.center,
                //       padding: EdgeInsets.only(bottom: 5.h),
                //       decoration: BoxDecoration(
                //         border: Border(
                //           bottom: BorderSide(
                //             width: 1.sp,
                //             color: accountsController.selectedTab == 1
                //                 ? colorsConstants.primaryRed
                //                 : colorsConstants.slateGrey,
                //           ),
                //         ),
                //       ),
                //       child: Text(
                //         "Part Purchase",
                //         style: TextStyle(
                //           fontSize: 12.sp,
                //           color: accountsController.selectedTab == 1
                //               ? colorsConstants.blackColor
                //               : colorsConstants.hintGrey,
                //         ),
                //       ),
                //     ),
                //   ),
                // ),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      accountsController.changeSelectedTab(changedTab: 2);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      padding: EdgeInsets.only(bottom: 5.h),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            width: 1.sp,
                            color: accountsController.selectedTab == 2
                                ? colorsConstants.primaryRed
                                : colorsConstants.slateGrey,
                          ),
                        ),
                      ),
                      child: Text(
                        "Income",
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: accountsController.selectedTab == 2
                              ? colorsConstants.blackColor
                              : colorsConstants.hintGrey,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 25,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    if (accountsController.selectedTab == 0) ...[
                      HeightBox(20.h),
                      Row(
                        children: [
                          ButtonWithIcon(
                            onPress: () {
                              moveToAddExpenseScreen(
                                context: context,
                                isPartPurchase: false,
                                revertCallback: (_) {
                                  accountsController.update();
                                },
                              );
                            },
                            title: "Add Expense",
                            icon: Icons.add,
                          ),
                        ],
                      ),
                      HeightBox(20.h),
                      Expense(),
                    ],
                    if (accountsController.selectedTab == 1) ...[
                      Row(
                        children: [
                          ButtonWithIcon(
                            onPress: () {
                              moveToAddExpenseScreen(
                                context: context,
                                isPartPurchase: true,
                              );
                              accountsController.update();
                            },
                            title: "Add Part Purchase",
                            icon: Icons.add,
                          ),
                          // const Spacer(),
                          // SmallPrimaryButton(
                          //   onPress: () {
                          //     Utils.showSnackBar(title: "Coming soon!");
                          //   },
                          //   title: "View All Vendors Due",
                          // ),
                        ],
                      ),
                      HeightBox(20.h),

                      // const TotalPaidCreditContainer(),
                    ],
                    if (accountsController.selectedTab == 0 &&
                        accountsController.incomeExpenses.isEmpty) ...[
                      HeightBox(20.h),
                      const Text(
                        "No expense found for the given time limit",
                        style: TextStyle(color: Colors.black),
                      ),
                    ],
                    if (accountsController.selectedTab == 2 &&
                        accountsController.incomeExpenses.fold<bool>(
                          true,
                          (previousValue, element) {
                            if (previousValue &&
                                element.repairOrderModel?.orderStatus ==
                                    OrderStatus.completed) {
                              return true;
                            }

                            return previousValue;
                          },
                        )) ...[
                      HeightBox(20.h),
                      Income(),
                    ],
                  ],
                ),
              ),
            ),
          ),
          Expanded(
              child: SingleChildScrollView(
            child: Column(children: [
              if (accountsController.selectedTab == 0) ...[
                ...accountsController.incomeExpenses
                    .mapIndexed((index, expense) {
                  if (index == 0) {
                    // print(expense.repairOrderModel.invoiceId);
                    return Column(
                      children: [
                        // Padding(
                        //     padding: EdgeInsets.symmetric(horizontal: 16.w),
                        //     child: const TotalPaidCreditContainer()),
                        getExpenseCard(expense)
                      ],
                    );
                  }

                  return getExpenseCard(expense);
                }),
              ],
              if (accountsController.selectedTab == 2) ...[
                HeightBox(10.h),
                ...accountsController.incomeExpenses
                    .mapIndexed((index, expense) {
                  if (expense.repairOrderModel?.orderStatus ==
                      OrderStatus.completed) {
                    return getExpenseCard(expense);
                  }
                  return const SizedBox();
                }),
              ],
            ]),
          )),
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 5.h),
            decoration: BoxDecoration(
              boxShadow: Constants.boxShadow,
              color: colorsConstants.whiteColor,
            ),
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "NET",
                      style: TextStyle(fontSize: 20.sp),
                    ),
                    HeightBox(2.h),
                    accountsController.fetchexpense
                        ? Center(
                            child: SizedBox(
                            height: 25,
                            width: 25,
                          ))
                        : Text(
                            "Profit ${Constants.rupeeSign} ${(income - expense).toStringAsFixed(2)}",
                            style: TextStyle(fontSize: 12.sp),
                          ),
                  ],
                ),
                Expanded(
                  child: accountsController.fetchexpense
                      ? Center(
                          child: SizedBox(
                              height: 25,
                              width: 25,
                              child: CircularProgressIndicator()))
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              "Income ${Constants.rupeeSign} ${income.toStringAsFixed(2)}",
                              style: TextStyle(fontSize: 12.sp),
                            ),
                            // Text(
                            //   "Income ${Constants.rupeeSign} ${accountsController.incomeExpenses.fold<double>(
                            //     0.0,
                            //     (previousValue, element) {
                            //       if (element.repairOrderModel.orderStatus ==
                            //           OrderStatus.completed) {
                            //         return previousValue +
                            //                 element.repairOrderModel
                            //                     .repairDetailsModel!.total! ??
                            //             0.0;
                            //       }
                            //       return previousValue;
                            //     },
                            //   )}",
                            //   style: TextStyle(fontSize: 12.sp),
                            // ),
                            HeightBox(2.h),
                            Text(
                              "Expense ${Constants.rupeeSign} ${expense.toStringAsFixed(2)}",
                              style: TextStyle(fontSize: 12.sp),
                            ),
                          ],
                        ),
                ),
                // WidthBox(20.w),
                // Text(
                //   "${Constants.rupeeSign}0.00",
                //   style: TextStyle(fontSize: 20.sp),
                // ),
              ],
            ),
          ),
          HeightBox(10.h),
        ],
      );
    });
  }

  Widget getExpenseCard(RepairOrderExpenseModel expense) {
    print("ee");
    // print(expense.expenseModel?.totalAmount);
    return InkWell(
      onTap: () {
        showModalBottomSheet(
          context: context,
          scrollControlDisabledMaxHeightRatio: 0.9,
          builder: (context) {
            return Container(
              color: colorsConstants.whiteColor,
              width: double.maxFinite,
              child: ExpenseDetails(
                expense: expense,
              ),
            );
          },
        );
      },
      child: Container(
        margin: EdgeInsets.symmetric(vertical: 5.h, horizontal: 16.w),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: Constants.boxShadow,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Row(
            //   children: [
            //     Text(
            //       "Label: ${expense.expenseModel?.expenseLabel}",
            //     ),
            //     const Spacer(),
            //     Text(
            //       "Mode: ${expense.expenseModel?.paymentMode!.name}",
            //     ),
            //   ],
            // ),
            // HeightBox(5.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text("Advance Payment: "),
                Text(
                  "${Constants.rupeeSign} ${expense.expenseModel?.advancePaid ?? 0.0}",
                  style: TextStyle(
                    color: colorsConstants.hintGrey,
                  ),
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text("Total Payment:"),
                Text(
                  "${Constants.rupeeSign} ${expense.expenseModel?.totalAmount ?? 0.0}",
                  style: TextStyle(
                    color: colorsConstants.hintGrey,
                  ),
                ),
              ],
            ),
            if (expense.expenseModel?.comment?.isNotEmpty ?? false) ...[
              HeightBox(5.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Comment:",
                    style: TextStyle(fontSize: 12.sp),
                  ),
                  Text(
                    "${expense.expenseModel?.comment}",
                    style: TextStyle(
                        color: colorsConstants.hintGrey, fontSize: 12.sp),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Expense Date:",
                    style: TextStyle(fontSize: 12.sp),
                  ),
                  Text(
                    DateFormat("d MMM y").format(DateTime.parse(
                        expense.expenseModel?.expenseDate ?? "")),
                    style: TextStyle(
                        color: colorsConstants.hintGrey, fontSize: 12.sp),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  AlertDialog filterDialog(BuildContext context) {
    AccountsController accountsController = Get.find<AccountsController>();

    return AlertDialog(
      content: ConstrainedBox(
          constraints: BoxConstraints(maxHeight: 250.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "By Date",
                style: TextStyle(fontSize: 22.sp, fontWeight: FontWeight.bold),
              ),
              const Divider(),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ...SearchDateRange.values.map(
                        (rangeType) {
                          return InkWell(
                            onTap: () async {
                              DateTime? startDate;
                              DateTime? endDate;

                              if (rangeType == SearchDateRange.dateRange) {
                                DateTime minDateTime =
                                    DateTime.utc(1985, 04, 20);
                                DateTime maxDateTime =
                                    DateTime.utc(275760, 09, 13);
                                DateTimeRange? selectedDateRange =
                                    await showDateRangePicker(
                                  context: context,
                                  firstDate: minDateTime,
                                  lastDate: DateTime.now(),
                                );

                                if (selectedDateRange != null) {
                                  startDate = selectedDateRange.start;
                                  endDate = selectedDateRange.end;
                                }
                              }

                              accountsController.changeSearchDateRange(
                                  dateRange: rangeType,
                                  startDate: startDate,
                                  endDate: endDate);
                              accountsController.filterInvoiceByDates(
                                  startDate: accountsController.startDate,
                                  endDate: accountsController.endDate);
                              accountsController.filterExpensesByDates(
                                  startDate: accountsController.startDate,
                                  endDate: accountsController.endDate);
                              if (context.mounted) Navigator.pop(context);
                            },
                            child: Row(
                              children: [
                                Container(
                                  padding: EdgeInsets.all(8.sp),
                                  margin: EdgeInsets.symmetric(vertical: 10.sp),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: colorsConstants.primaryRed,
                                      width: 2.sp,
                                    ),
                                  ),
                                ),
                                WidthBox(10.w),
                                Text(rangeType.name),
                              ],
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
              HeightBox(10.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  PrimaryButton(
                    onPress: () {
                      Navigator.pop(context);
                    },
                    title: "Ok",
                  ),
                ],
              )
            ],
          )),
    );
  }
}

class ExpenseDetails extends StatelessWidget {
  const ExpenseDetails({super.key, required this.expense});

  final RepairOrderExpenseModel expense;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            color: colorsConstants.lightBlue,
            padding: EdgeInsets.symmetric(vertical: 10.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "Expense Details",
                  style: TextStyle(fontSize: 16.sp),
                ),
              ],
            ),
          ),
          HeightBox(5.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Total Expense",
                      style: TextStyle(fontSize: 14.sp),
                    ),
                    Text(
                      "${Constants.rupeeSign} ${expense.expenseModel?.totalAmount.toString() ?? "--"}",
                      style: TextStyle(fontSize: 14.sp),
                    ),
                  ],
                ),
                //       Row(
                //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //         children: [
                //           Text(
                //             "Advance Paid",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //           Text(
                //             "${Constants.rupeeSign} ${expense.expenseModel?.advancePaid?.toString() ?? "--"}",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //         ],
                //       ),
                //       Row(
                //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //         children: [
                //           Text(
                //             "Expense Mode",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //           Text(
                //             expense.expenseModel?.expenseMode?.name.toUpperCase() ??
                //                 "--",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //         ],
                //       ),
                //       Row(
                //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //         children: [
                //           Text(
                //             "Payment Mode",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //           Text(
                //             expense.expenseModel?.paymentMode?.name ?? "--",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //         ],
                //       ),
                //       Row(
                //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //         children: [
                //           Text(
                //             "Expense Date",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //           Text(
                //             DateFormat("d MMM y").format(DateTime.parse(
                //                 expense.expenseModel?.expenseDate ?? "")),
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //         ],
                //       ),
                //     ],
                //   ),
                // ),
                // HeightBox(10.h),
                // Container(
                //   color: colorsConstants.lightBlue,
                //   padding: EdgeInsets.symmetric(vertical: 10.h),
                //   child: Row(
                //     mainAxisAlignment: MainAxisAlignment.center,
                //     children: [
                //       Text(
                //         "Customer Details",
                //         style: TextStyle(fontSize: 16.sp),
                //       ),
                //     ],
                //   ),
                // ),
                // HeightBox(5.h),
                // Padding(
                //   padding: EdgeInsets.symmetric(horizontal: 16.w),
                //   child: Column(
                //     children: [
                //       Row(
                //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //         children: [
                //           Text(
                //             "Username",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //           Text(
                //             expense.repairOrderModel.customerDetailsModel?.username ??
                //                 "--",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //         ],
                //       ),
                //       Row(
                //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //         children: [
                //           Text(
                //             "Phone",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //           Text(
                //             expense.repairOrderModel.customerDetailsModel?.phone ??
                //                 "--",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //         ],
                //       ),
                //       Row(
                //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //         children: [
                //           Text(
                //             "Email",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //           Text(
                //             expense.repairOrderModel.customerDetailsModel?.email ??
                //                 "--",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //         ],
                //       ),
                //       Row(
                //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //         children: [
                //           Text(
                //             "Address",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //           Text(
                //             expense.repairOrderModel.customerDetailsModel?.address ??
                //                 "--",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //         ],
                //       ),
                //     ],
                //   ),
                // ),
                // HeightBox(10.h),
                // Container(
                //   color: colorsConstants.lightBlue,
                //   padding: EdgeInsets.symmetric(vertical: 10.h),
                //   child: Row(
                //     mainAxisAlignment: MainAxisAlignment.center,
                //     children: [
                //       Text(
                //         "Vehicle Details",
                //         style: TextStyle(fontSize: 16.sp),
                //       ),
                //     ],
                //   ),
                // ),
                // HeightBox(5.h),
                // Padding(
                //   padding: EdgeInsets.symmetric(horizontal: 16.w),
                //   child: Column(
                //     children: [
                //       Row(
                //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //         children: [
                //           Text(
                //             "Model",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //           Text(
                //             "${expense.repairOrderModel.vehicleDetailsModel?.make} ${expense.repairOrderModel.vehicleDetailsModel?.model ?? "--"}",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //         ],
                //       ),
                //       Row(
                //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //         children: [
                //           Text(
                //             "Registration Number",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //           Text(
                //             expense.repairOrderModel.vehicleDetailsModel
                //                     ?.registrationNumber ??
                //                 "--",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //         ],
                //       ),
                //     ],
                //   ),
                // ),
                // HeightBox(10.h),
                // Container(
                //   color: colorsConstants.lightBlue,
                //   padding: EdgeInsets.symmetric(vertical: 10.h),
                //   child: Row(
                //     mainAxisAlignment: MainAxisAlignment.center,
                //     children: [
                //       Text(
                //         "Order Details",
                //         style: TextStyle(fontSize: 16.sp),
                //       ),
                //     ],
                //   ),
                // ),
                // HeightBox(10.h),
                // Padding(
                //   padding: EdgeInsets.symmetric(horizontal: 16.w),
                //   child: Column(
                //     children: [
                //       Row(
                //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //         children: [
                //           Text(
                //             "Created At",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //           Text(
                //             DateFormat("d MMM y").format(DateTime.parse(
                //                 expense.repairOrderModel.createdAt ?? "")),
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //         ],
                //       ),
                //       Row(
                //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //         children: [
                //           Text(
                //             "Payment Due",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //           Text(
                //             "${Constants.rupeeSign} ${expense.repairOrderModel.repairDetailsModel?.paymentDue.toString() ?? "--"}",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //         ],
                //       ),
                //       Row(
                //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //         children: [
                //           Text(
                //             "Discount",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //           Text(
                //             "${Constants.rupeeSign} ${expense.repairOrderModel.repairDetailsModel?.discount.toString() ?? "--"}",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //         ],
                //       ),
                //       Row(
                //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //         children: [
                //           Text(
                //             "Payment Received",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //           Text(
                //             "${Constants.rupeeSign} ${expense.repairOrderModel.repairDetailsModel?.paymentReceived.toString() ?? "--"}",
                //             style: TextStyle(fontSize: 14.sp),
                //           ),
                //         ],
                //       ),
                //       HeightBox(10.h),
                //       Container(
                //         color: colorsConstants.lightBlue,
                //         child: Row(
                //           mainAxisAlignment: MainAxisAlignment.center,
                //           children: [
                //             Text(
                //               "Parts",
                //               style: TextStyle(fontSize: 16.sp),
                //             ),
                //           ],
                //         ),
                //       ),
                //       Container(
                //         color: colorsConstants.slateGrey,
                //         padding: EdgeInsets.symmetric(horizontal: 16.w),
                //         child: Column(
                //           children: [
                //             HeightBox(5.h),
                //             ...expense.repairOrderModel.repairDetailsModel!.parts!
                //                 .map((part) {
                //               return Row(
                //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //                 children: [
                //                   Text(part.title ?? "--"),
                //                   Text(
                //                       "${Constants.rupeeSign} ${part.amount.toString()}"),
                //                 ],
                //               );
                //             }),
                //             HeightBox(5.h),
                //             Divider(
                //               color: colorsConstants.blackColor.withOpacity(0.1),
                //             ),
                //             Row(
                //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //               children: [
                //                 Text(
                //                   "Parts Total",
                //                   style: TextStyle(fontSize: 14.sp),
                //                 ),
                //                 Text(
                //                   "${Constants.rupeeSign} ${expense.repairOrderModel.repairDetailsModel?.partsTotal.toString() ?? "--"}",
                //                   style: TextStyle(fontSize: 14.sp),
                //                 ),
                //               ],
                //             ),
                //             HeightBox(5.h),
                //           ],
                //         ),
                //       ),
                //       HeightBox(10.h),
                //       Container(
                //         color: colorsConstants.lightBlue,
                //         child: Row(
                //           mainAxisAlignment: MainAxisAlignment.center,
                //           children: [
                //             Text(
                //               "Services",
                //               style: TextStyle(fontSize: 16.sp),
                //             ),
                //           ],
                //         ),
                //       ),
                //       Container(
                //         color: colorsConstants.slateGrey,
                //         padding: EdgeInsets.symmetric(horizontal: 16.w),
                //         child: Column(
                //           children: [
                //             HeightBox(5.h),
                //             ...expense.repairOrderModel.repairDetailsModel!.services!
                //                 .map((part) {
                //               return Row(
                //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //                 children: [
                //                   Text(part.title ?? "--"),
                //                   Text(
                //                       "${Constants.rupeeSign} ${part.amount.toString()}"),
                //                 ],
                //               );
                //             }),
                //             HeightBox(5.h),
                //             Divider(
                //               color: colorsConstants.blackColor.withOpacity(0.1),
                //             ),
                //             Row(
                //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //               children: [
                //                 Text(
                //                   "Services Total",
                //                   style: TextStyle(fontSize: 14.sp),
                //                 ),
                //                 Text(
                //                   "${Constants.rupeeSign} ${expense.repairOrderModel.repairDetailsModel?.servicesTotal.toString() ?? "--"}",
                //                   style: TextStyle(fontSize: 14.sp),
                //                 ),
                //               ],
                //             ),
                //             HeightBox(5.h),
                //           ],
                //         ),
                //       ),
                // HeightBox(5.h),
                // ...expense.repairOrderModel.repairDetailsModel!.services!
                //     .map((part) {
                //   return Row(
                //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //     children: [
                //       Text(part.title ?? "--"),
                //       Text("${Constants.rupeeSign} ${part.amount.toString()}"),
                //     ],
                //   );
                // }),
              ],
            ),
          ),
          HeightBox(80.h),
        ],
      ),
    );
  }
}

class TotalPaidCreditContainer extends StatelessWidget {
  const TotalPaidCreditContainer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AccountsController>(builder: (accountsController) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: colorsConstants.slateGrey,
            width: 2.sp,
          ),
        ),
        child: Column(
          children: [
            HeightBox(10.h),
            Row(
              children: [
                const Spacer(),
                Row(
                  children: [
                    Container(
                      height: 8.h,
                      width: 8.w,
                      color: Colors.green,
                    ),
                    WidthBox(2.w),
                    Text(
                      "Paid",
                      style: TextStyle(fontSize: 12.sp),
                    )
                  ],
                ),
                WidthBox(10.w),
                Row(
                  children: [
                    Container(
                      height: 8.h,
                      width: 8.w,
                      color: Colors.orange,
                    ),
                    WidthBox(2.w),
                    Text(
                      "Partial Paid",
                      style: TextStyle(fontSize: 12.sp),
                    )
                  ],
                ),
                WidthBox(10.w),
                Row(
                  children: [
                    Container(
                      height: 8.h,
                      width: 8.w,
                      color: Colors.red,
                    ),
                    WidthBox(2.w),
                    Text(
                      "Credit",
                      style: TextStyle(fontSize: 12.sp),
                    )
                  ],
                ),
                const Spacer(),
              ],
            ),
            HeightBox(10.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Column(
                  children: [
                    const Text(
                      "TOTAL",
                    ),
                    HeightBox(10.h),
                    Text(
                      "${Constants.rupeeSign} ${accountsController.incomeExpenses.fold<double>(
                        0.0,
                        (previousValue, element) {
                          return previousValue +
                              (element.expenseModel?.totalAmount ??
                                  0.0 + element.expenseModel!.advancePaid!);
                        },
                      )}",
                      style: TextStyle(
                        color: colorsConstants.hintGrey,
                        fontSize: 14.sp,
                      ),
                    )
                  ],
                ),
                Column(
                  children: [
                    const Text(
                      "PAID",
                      style: TextStyle(
                        color: Colors.green,
                      ),
                    ),
                    HeightBox(10.h),
                    Text(
                      "${Constants.rupeeSign} ${accountsController.incomeExpenses.fold<double>(
                        0.0,
                        (previousValue, element) {
                          if (element.expenseModel?.expenseMode ==
                              ExpenseMode.paid) {
                            return previousValue +
                                (element.expenseModel?.totalAmount ?? 0.0);
                          }
                          return previousValue;
                        },
                      )}",
                      style: TextStyle(
                        color: colorsConstants.hintGrey,
                        fontSize: 14.sp,
                      ),
                    )
                  ],
                ),
                Column(
                  children: [
                    const Text(
                      "CREDIT",
                      style: TextStyle(
                        color: Colors.red,
                      ),
                    ),
                    HeightBox(10.h),
                    Text(
                      "${Constants.rupeeSign} ${accountsController.incomeExpenses.fold<double>(
                        0.0,
                        (previousValue, element) {
                          if (element.expenseModel?.expenseMode ==
                              ExpenseMode.credit) {
                            return previousValue +
                                (element.expenseModel?.totalAmount ?? 0.0);
                          }
                          return previousValue;
                        },
                      )}",
                      style: TextStyle(
                        color: colorsConstants.hintGrey,
                        fontSize: 14.sp,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            HeightBox(10.h),
          ],
        ),
      );
    });
  }
}
