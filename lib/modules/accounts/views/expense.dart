import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/franchise_details.dart';
import 'package:speed_force_franchise/modules/accounts/controllers/accounts_controller.dart';
import 'package:speed_force_franchise/modules/accounts/modules/add_expenses/models/repair_order_expense_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/orders/controllers/view_orders_controller.dart';
import 'package:speed_force_franchise/modules/orders/views/repair_order_details.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class Expense extends StatefulWidget {
  const Expense({super.key, this.orderStatus, this.vehicleId});
  final OrderStatus? orderStatus;
  final String? vehicleId;
  @override
  State<Expense> createState() => _ExpenseState();
}

class _ExpenseState extends State<Expense> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<ViewOrdersController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AccountsController>(
      builder: (viewOrdersController) {
        // AccountsController viewOrdersController =
        //     Get.find<AccountsController>();
        print("===---${viewOrdersController.incomeExpenses.length}");
        return viewOrdersController.isLoading
            ? Padding(
                padding: const EdgeInsets.only(top: 50.0),
                child: Center(child: CircularProgressIndicator()),
              )
            : Column(
                children: [
                  ...List.generate(
                    viewOrdersController.isFiltered
                        ? viewOrdersController.filteredExpenses.length
                        : viewOrdersController.incomeExpenses.length,
                    (index) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 10.0),
                        child: IncomeCard2(
                          repairOrder: viewOrdersController.isFiltered
                              ? viewOrdersController.filteredExpenses[index]
                              : viewOrdersController.incomeExpenses[index],
                          franchiseDetails: Get.find<HomeScreenController>()
                                  .franchiseDetails ??
                              FranchiseDetails(),
                        ),
                      );
                    },
                  )
                ],
              );
      },
    );
  }
}

class IncomeCard2 extends StatefulWidget {
  const IncomeCard2({
    super.key,
    required this.repairOrder,
    required this.franchiseDetails,
    this.onRevertCallBack,
  });

  final RepairOrderExpenseModel repairOrder;
  final FranchiseDetails franchiseDetails;
  final void Function()? onRevertCallBack;

  @override
  State<IncomeCard2> createState() => _IncomeCard2State();
}

class _IncomeCard2State extends State<IncomeCard2> {
  final _globalKey = GlobalKey<FormState>();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Get.put(RepairOrderDetails(
      orderId: widget.repairOrder.expenseModel?.repairOrderId ?? "",
    ));
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        // showModalBottomSheet(
        //   context: context,
        //   scrollControlDisabledMaxHeightRatio: 0.9,
        //   builder: (context) {
        //     return RepairOrderInvoiceData(
        //       globalKey: _globalKey,
        //       repairOrderModel: widget.repairOrder,
        //       invoiceModel: widget.repairOrder,
        //     );
        //   },
        // );
      },
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 11.w, vertical: 5.w),
        decoration: BoxDecoration(
          boxShadow: Constants.boxShadow,
          color: colorsConstants.whiteColor,
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      HeightBox(5.h),

                      Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Expanded(
                              child: Center(
                                child: Text(
                                  "${Constants.rupeeSign} ${widget.repairOrder.expenseModel?.totalAmount?.floor().toString()}",
                                  style: TextStyle(
                                    fontSize: 18.sp,
                                    color:
                                        const Color.fromARGB(159, 0, 68, 255),
                                  ),
                                ),
                              ),
                            ),
                            Expanded(
                              child: Column(
                                children: [
                                  Text(
                                    overflow: TextOverflow.ellipsis,
                                    " ${widget.repairOrder.expenseModel?.title ?? "-"}",
                                    style: TextStyle(
                                        fontSize: 12.sp,
                                        color: colorsConstants.hintGrey),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                      HeightBox(5.h),
                      // if (widget.repairOrder.orderStatus ==
                      //     OrderStatus.completed) ...[
                      //   Text(
                      //     "Paid: ${Constants.rupeeSign} ${widget.repairOrder.repairDetailsModel?.paymentReceived?.toString()}",
                      //     style:
                      //         TextStyle(fontSize: 12.sp, color: Colors.green),
                      //   ),
                      //   HeightBox(10.h),
                      // ],
                      // Container(
                      //   padding: EdgeInsets.symmetric(
                      //     horizontal: 5.w,
                      //     vertical: 10.h,
                      //   ),
                      //   decoration: BoxDecoration(
                      //     color: widget.repairOrder.orderStatus?.getColor(),
                      //     boxShadow: Constants.boxShadow,
                      //   ),
                      //   child: Container(
                      //     width: double.maxFinite,
                      //     alignment: Alignment.center,
                      //     child: Text(
                      //       widget.repairOrder.orderStatus?.name ?? "",
                      //       style: TextStyle(
                      //         fontSize: 10.sp,
                      //         color: colorsConstants.whiteColor,
                      //         fontWeight: FontWeight.bold,
                      //       ),
                      //     ),
                      //   ),
                      // ),
                    ],
                  ),
                ),
                WidthBox(10.w),
              ],
            ),
            Container(
              padding: EdgeInsets.symmetric(vertical: 10.h),
              decoration: BoxDecoration(
                  color: colorsConstants.successGreen.withOpacity(0.1)),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          "Description",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: colorsConstants.hintGrey,
                          ),
                        ),
                        Text(
                          widget.repairOrder.expenseModel?.comment ?? "-",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: colorsConstants.hintGrey,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Expanded(
                  //   child: Column(
                  //     children: [
                  //       Text(
                  //         "Created By",
                  //         style: TextStyle(
                  //           fontSize: 10.sp,
                  //           color: colorsConstants.hintGrey,
                  //         ),
                  //       ),
                  //       Text(
                  //         widget.franchiseDetails.ownerName ?? "",
                  //         style: TextStyle(
                  //           fontSize: 10.sp,
                  //           color: colorsConstants.hintGrey,
                  //         ),
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          "Expense Date",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: colorsConstants.hintGrey,
                          ),
                        ),
                        Text(
                          widget.repairOrder.expenseModel?.expenseDate
                                  ?.substring(0, 10) ??
                              "",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: colorsConstants.hintGrey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
