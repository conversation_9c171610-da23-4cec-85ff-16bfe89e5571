import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/franchise_details.dart';
import 'package:speed_force_franchise/modules/accounts/controllers/accounts_controller.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/orders/controllers/view_invoice_controller.dart';
import 'package:speed_force_franchise/modules/orders/controllers/view_orders_controller.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/modules/orders/widgets/repair_order_invoice_data.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class Income extends StatefulWidget {
  const Income({super.key, this.orderStatus, this.vehicleId});
  final OrderStatus? orderStatus;
  final String? vehicleId;
  @override
  State<Income> createState() => _IncomeState();
}

class _IncomeState extends State<Income> {
  @override
  void initState() {
    super.initState();
    // Get.find<AccountsController>().fetchInvoices(
    //     orderStatus: widget.orderStatus?.name, vehicleId: widget.vehicleId);
  }

  // @override
  // void dispose() {
  //   Get.delete<ViewOrdersController>();
  //   super.dispose();
  // }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AccountsController>(
      builder: (viewOrdersController) {
        AccountsController viewOrdersController =
            Get.find<AccountsController>();

        return viewOrdersController.isLoading
            ? Padding(
                padding: const EdgeInsets.only(top: 50.0),
                child: Center(child: CircularProgressIndicator()),
              )
            : Column(
                children: [
                  ...List.generate(
                    viewOrdersController.isFiltered
                        ? viewOrdersController.filteredInvvoices.length
                        : viewOrdersController.repairOrderModels.length,
                    (index) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 10.0),
                        child: IncomeCard(
                          repairOrder: viewOrdersController.isFiltered
                              ? viewOrdersController.filteredInvvoices[index]
                              : viewOrdersController.repairOrderModels[index],
                          franchiseDetails: Get.find<HomeScreenController>()
                                  .franchiseDetails ??
                              FranchiseDetails(),
                        ),
                      );
                    },
                  )
                ],
              );
      },
    );
  }
}

class IncomeCard extends StatefulWidget {
  const IncomeCard({
    super.key,
    required this.repairOrder,
    required this.franchiseDetails,
    this.onRevertCallBack,
  });

  final RepairOrderModel repairOrder;
  final FranchiseDetails franchiseDetails;
  final void Function()? onRevertCallBack;

  @override
  State<IncomeCard> createState() => _IncomeCardState();
}

class _IncomeCardState extends State<IncomeCard> {
  final _globalKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        showModalBottomSheet(
          context: context,
          scrollControlDisabledMaxHeightRatio: 0.9,
          builder: (context) {
            return RepairOrderInvoiceData(
              globalKey: _globalKey,
              repairOrderModel: widget.repairOrder,
              invoiceModel: widget.repairOrder,
            );
          },
        );
      },
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 11.w, vertical: 5.w),
        decoration: BoxDecoration(
          boxShadow: Constants.boxShadow,
          color: colorsConstants.whiteColor,
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      HeightBox(5.h),
                      if (widget.repairOrder.orderStatus !=
                          OrderStatus.completed) ...[
                        Text(
                          "Due: ${Constants.rupeeSign} ${widget.repairOrder.repairDetailsModel?.paymentDue?.toString()}",
                          style: TextStyle(
                              fontSize: 12.sp,
                              color: colorsConstants.primaryRed),
                        ),
                        HeightBox(5.h),
                      ],
                      Text(
                        "${Constants.rupeeSign} ${widget.repairOrder.repairDetailsModel?.total?.floor().toString()}",
                        style: TextStyle(
                          fontSize: 18.sp,
                          color: const Color.fromARGB(159, 0, 68, 255),
                        ),
                      ),

                      HeightBox(5.h),
                      // if (widget.repairOrder.orderStatus ==
                      //     OrderStatus.completed) ...[
                      //   Text(
                      //     "Paid: ${Constants.rupeeSign} ${widget.repairOrder.repairDetailsModel?.paymentReceived?.toString()}",
                      //     style:
                      //         TextStyle(fontSize: 12.sp, color: Colors.green),
                      //   ),
                      //   HeightBox(10.h),
                      // ],
                      // Container(
                      //   padding: EdgeInsets.symmetric(
                      //     horizontal: 5.w,
                      //     vertical: 10.h,
                      //   ),
                      //   decoration: BoxDecoration(
                      //     color: widget.repairOrder.orderStatus?.getColor(),
                      //     boxShadow: Constants.boxShadow,
                      //   ),
                      //   child: Container(
                      //     width: double.maxFinite,
                      //     alignment: Alignment.center,
                      //     child: Text(
                      //       widget.repairOrder.orderStatus?.name ?? "",
                      //       style: TextStyle(
                      //         fontSize: 10.sp,
                      //         color: colorsConstants.whiteColor,
                      //         fontWeight: FontWeight.bold,
                      //       ),
                      //     ),
                      //   ),
                      // ),
                    ],
                  ),
                ),
                WidthBox(10.w),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        widget.repairOrder.customerDetailsModel?.username ?? "",
                      ),
                      HeightBox(5.h),
                      Text(
                        widget.repairOrder.customerDetailsModel?.phone ?? "",
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: colorsConstants.hintGrey,
                        ),
                      ),
                      HeightBox(5.h),
                    ],
                  ),
                ),
              ],
            ),
            Container(
              padding: EdgeInsets.symmetric(vertical: 10.h),
              decoration: BoxDecoration(
                  color: colorsConstants.successGreen.withOpacity(0.1)),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          "Invoice",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: colorsConstants.hintGrey,
                          ),
                        ),
                        Text(
                          widget.repairOrder.jobCardId ?? "",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: colorsConstants.hintGrey,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          "${widget.repairOrder.vehicleDetailsModel?.make ?? ""} ${widget.repairOrder.vehicleDetailsModel?.model ?? ""}",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: colorsConstants.hintGrey,
                          ),
                        ),
                        // HeightBox(5.h),
                        Text(
                          widget.repairOrder.vehicleDetailsModel
                                  ?.registrationNumber ??
                              "",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: colorsConstants.hintGrey,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Expanded(
                  //   child: Column(
                  //     children: [
                  //       Text(
                  //         "Created By",
                  //         style: TextStyle(
                  //           fontSize: 10.sp,
                  //           color: colorsConstants.hintGrey,
                  //         ),
                  //       ),
                  //       Text(
                  //         widget.franchiseDetails.ownerName ?? "",
                  //         style: TextStyle(
                  //           fontSize: 10.sp,
                  //           color: colorsConstants.hintGrey,
                  //         ),
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          "Completion",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: colorsConstants.hintGrey,
                          ),
                        ),
                        Text(
                          widget.repairOrder.completionDate != null
                              ? DateFormat("d MMM y").format(
                                  widget.repairOrder.completionDate?.toDate() ??
                                      DateTime.now(),
                                )
                              : "-",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: colorsConstants.hintGrey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
