import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/add_garage/widgets/personal_details.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class AddGarageController extends GetxController {
  PageController pageController = PageController();

  VehicleType selectedVehicleType = VehicleType.twoWheeler;

  changeVehicleType({required VehicleType vehicleType}) {
    selectedVehicleType = vehicleType;
    update();
  }
}
