import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/add_garage/controller/add_garage_controller.dart';
import 'package:speed_force_franchise/modules/add_garage/widgets/personal_details.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class AddGarage extends StatefulWidget {
  const AddGarage({super.key});

  @override
  State<AddGarage> createState() => _AddGarageState();
}

class _AddGarageState extends State<AddGarage> {
  @override
  void initState() {
    super.initState();
    Get.put(AddGarageController());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Workshop Details",
          style: TextStyle(color: colorsConstants.primaryRed),
        ),
        elevation: 0.5,
        backgroundColor: colorsConstants.whiteColor,
        shadowColor: colorsConstants.whiteColor,
        surfaceTintColor: colorsConstants.whiteColor,
      ),
      body: SingleChildScrollView(
        child: GetBuilder<AddGarageController>(
          builder: (_) {
            AddGarageController addGarageController =
                Get.find<AddGarageController>();
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                HeightBox(40.h),
                SizedBox(
                  height: 500.h,
                  child: PageView.builder(
                    itemCount: 3,
                    controller: addGarageController.pageController,
                    itemBuilder: (BuildContext context, int index) {
                      return const PersonalDetails();
                    },
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
