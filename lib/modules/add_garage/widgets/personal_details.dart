import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/add_garage/controller/add_garage_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

enum VehicleType { twoWheeler, fourWheeler }

class PersonalDetails extends StatefulWidget {
  const PersonalDetails({super.key});

  @override
  State<PersonalDetails> createState() => _PersonalDetailsState();
}

class _PersonalDetailsState extends State<PersonalDetails> {
  @override
  Widget build(BuildContext context) {
    AddGarageController addGarageController = Get.find<AddGarageController>();
    return GetBuilder<AddGarageController>(
      builder: (_) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 14.w),
              child: Text(
                "Personal Details",
                style: TextStyle(
                  color: colorsConstants.primaryRed,
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
            HeightBox(10.h),
            Container(
              decoration: BoxDecoration(color: colorsConstants.whiteColor),
              width: double.maxFinite,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 14.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    HeightBox(20.h),
                    Text(
                      "Garage Type *",
                      style: TextStyle(
                        color: colorsConstants.hintGrey,
                      ),
                    ),
                    HeightBox(20.h),
                    Row(
                      children: [
                        Expanded(
                          child: RadioListTile<VehicleType>(
                            title: Row(
                              children: [
                                const Icon(Icons.two_wheeler_rounded),
                                WidthBox(5.w),
                                Text(
                                  'Two wheeler',
                                  style: TextStyle(fontSize: 12.sp),
                                ),
                              ],
                            ),
                            value: VehicleType.twoWheeler,
                            groupValue: addGarageController.selectedVehicleType,
                            onChanged: (VehicleType? vehicleType) {
                              addGarageController.changeVehicleType(
                                vehicleType:
                                    vehicleType ?? VehicleType.twoWheeler,
                              );
                            },
                          ),
                        ),
                        Expanded(
                          child: RadioListTile<VehicleType>(
                            title: Row(
                              children: [
                                const Icon(CupertinoIcons.car_detailed),
                                WidthBox(5.w),
                                Text(
                                  'Two wheeler',
                                  style: TextStyle(fontSize: 12.sp),
                                ),
                              ],
                            ),
                            value: VehicleType.fourWheeler,
                            groupValue: addGarageController.selectedVehicleType,
                            onChanged: (VehicleType? vehicleType) {
                              addGarageController.changeVehicleType(
                                vehicleType:
                                    vehicleType ?? VehicleType.fourWheeler,
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
