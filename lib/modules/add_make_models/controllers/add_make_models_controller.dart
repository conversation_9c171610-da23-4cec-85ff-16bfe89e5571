import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/add_make_models/models/make_models_model.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class AddMakeModelController extends GetxController {
  AddMakeModelController({this.makeModelsModel}) {
    if (makeModelsModel != null) {
      makeTextEditingController.text = makeModelsModel?.company ?? "";
      models = makeModelsModel?.models ?? [];
    }
  }

  TextEditingController makeTextEditingController = TextEditingController();

  MakeModelsModel? makeModelsModel;

  List<String> models = [""];

  //  repairDetailsModel.customerRemarks =
  //     customerRemarks.where((remark) => remark.isNotEmpty).toList();

  void addModel() {
    models.add("");
    update(["models"]);
  }

  void updateModelAt({required String remark, required int index}) {
    models[index] = remark;
  }

  void removeModel({required int index}) {
    models.removeAt(index);
    update(["models"]);
  }

  Future<void> addUpdateMakeModesInDatabase() async {
    CollectionReference<Map<String, dynamic>> vehiclesCompaniesCollection =
        FirebaseFirestore.instance
            .collection(FirebaseCollections.vehiclesCompanies.name);
    if (makeModelsModel == null) {
      makeModelsModel = MakeModelsModel(
        franchiseId: FirebaseAuth.instance.currentUser?.uid,
        company: makeTextEditingController.text,
        models: models.where((model) => model.isNotEmpty).toList(),
      );

      DocumentReference<Map<String, dynamic>> newMakeModels =
          await vehiclesCompaniesCollection.add(makeModelsModel!.toMap());
      makeModelsModel?.makeModelId = newMakeModels.id;

      await vehiclesCompaniesCollection
          .doc(makeModelsModel?.makeModelId)
          .set(makeModelsModel!.toMap());
    } else {
      await vehiclesCompaniesCollection
          .doc(makeModelsModel!.makeModelId)
          .set(makeModelsModel!.toMap());
    }

    update();
  }
}
