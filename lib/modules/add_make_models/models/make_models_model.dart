import 'dart:convert';

class MakeModelsModel {
  String? franchiseId;
  String? makeModelId;
  String? company;
  List<String>? models;
  MakeModelsModel({
    this.franchiseId,
    this.makeModelId,
    this.company,
    this.models,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (franchiseId != null) {
      result.addAll({'franchiseId': franchiseId});
    }
    if (makeModelId != null) {
      result.addAll({'makeModelId': makeModelId});
    }
    if (company != null) {
      result.addAll({'company': company});
    }
    if (models != null) {
      result.addAll({'models': models});
    }

    return result;
  }

  factory MakeModelsModel.fromMap(Map<String, dynamic> map) {
    return MakeModelsModel(
      franchiseId: map['franchiseId'],
      makeModelId: map['makeModelId'],
      company: map['company'],
      models: List<String>.from(map['models']),
    );
  }

  String toJson() => json.encode(toMap());

  factory MakeModelsModel.fromJson(String source) =>
      MakeModelsModel.fromMap(json.decode(source));
}
