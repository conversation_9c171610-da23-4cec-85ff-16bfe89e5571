import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/add_make_models/controllers/add_make_models_controller.dart';
import 'package:speed_force_franchise/modules/add_make_models/models/make_models_model.dart';
import 'package:speed_force_franchise/modules/orders/controllers/create_repair_order_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';
import 'package:speed_force_franchise/widgets/small_primary_button.dart';

class AddMakeModels extends StatefulWidget {
  const AddMakeModels({super.key, this.makeModelsModel});

  final MakeModelsModel? makeModelsModel;

  @override
  State<AddMakeModels> createState() => _AddMakeModelsState();
}

class _AddMakeModelsState extends State<AddMakeModels> {
  @override
  void initState() {
    super.initState();
    Get.put(AddMakeModelController(makeModelsModel: widget.makeModelsModel));
  }

  @override
  void dispose() {
    Get.delete<AddMakeModelController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AddMakeModelController>(
        builder: (addMakeModelController) {
      return GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Scaffold(
          appBar: AppBar(
            title: Text(
              widget.makeModelsModel != null
                  ? "Add/Edit Models"
                  : "Add Make and Models",
              overflow: TextOverflow.ellipsis,
            ),
            elevation: 0.5,
            backgroundColor: colorsConstants.whiteColor,
            shadowColor: colorsConstants.whiteColor,
            surfaceTintColor: colorsConstants.whiteColor,
          ),
          body: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      HeightBox(20.h),
                      CustomTextField(
                        enabled: widget.makeModelsModel == null,
                        label: "Make Name*",
                        hintText: "Make Name",
                        controller:
                            addMakeModelController.makeTextEditingController,
                      ),
                      HeightBox(20.h),
                      Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 16.w, vertical: 10.h),
                        child: Row(
                          children: [
                            Text(
                              "Models",
                              style: TextStyle(
                                color:
                                    colorsConstants.blackColor.withOpacity(0.8),
                              ),
                            ),
                            const Spacer(),
                            SmallPrimaryButton(
                              title: "ADD",
                              icon: CupertinoIcons.add_circled_solid,
                              onPress: () {
                                addMakeModelController.addModel();
                              },
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: SingleChildScrollView(
                          child: GetBuilder<AddMakeModelController>(
                              id: "models",
                              builder: (addMakeModelController) {
                                return Container(
                                  color: colorsConstants.slateGrey,
                                  padding:
                                      addMakeModelController.models.isNotEmpty
                                          ? EdgeInsets.symmetric(
                                              horizontal: 16.w,
                                              vertical: 10.h,
                                            )
                                          : null,
                                  child: SingleChildScrollView(
                                      child: Column(
                                    children: addMakeModelController.models
                                        .mapIndexed((index, customerRemark) {
                                      return Container(
                                        margin: EdgeInsets.only(bottom: 10.h),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: TextField(
                                                controller:
                                                    TextEditingController(
                                                        text: customerRemark),
                                                onChanged: (value) {
                                                  addMakeModelController
                                                      .updateModelAt(
                                                          remark: value,
                                                          index: index);
                                                },
                                                decoration: InputDecoration(
                                                  filled: true,
                                                  fillColor: colorsConstants
                                                      .whiteColor,
                                                  hintText: "Model Name",
                                                  contentPadding:
                                                      EdgeInsets.only(
                                                          left: 5.w),
                                                  enabledBorder:
                                                      const OutlineInputBorder(
                                                    borderSide: BorderSide.none,
                                                  ),
                                                ),
                                              ),
                                            ),
                                            IconButton(
                                              onPressed: () {
                                                addMakeModelController
                                                    .removeModel(index: index);
                                              },
                                              icon: const Icon(Icons.close),
                                            )
                                          ],
                                        ),
                                      );
                                    }).toList(),
                                  )),
                                );
                              }),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(vertical: 20.h),
                  width: double.maxFinite,
                  child: PrimaryButton(
                    onPress: () async {
                      if (Get.find<CreateRepairOrderController>()
                              .vehiclesDataSource
                              .firstWhereOrNull((element) =>
                                  (element.company ?? "") ==
                                  addMakeModelController
                                      .makeTextEditingController.text
                                      .trim()) !=
                          null) {
                        Utils.showSnackBar(
                            title:
                                "Make already exist if you want to add model inside than select the make and go in models");
                        return;
                      }
                      // if (isAddingService) {
                      //   if (addMakeModelController
                      //       .serviceNameEditingController.text.isEmpty) {
                      //     Utils.showSnackBar(
                      //         title: "Fields with \"*\" are mandatory");
                      //     return;
                      //   }
                      // } else {
                      //   if (addMakeModelController
                      //       .partNameEditingController.text.isEmpty) {
                      //     Utils.showSnackBar(
                      //         title: "Fields with \"*\" are mandatory");
                      //     return;
                      //   }
                      // }
                      // await addMakeModelController.addServicePartInDatabase(
                      //     isAddingService: isAddingService);
                      if (addMakeModelController.makeTextEditingController.text
                          .trim()
                          .isEmpty) {
                        Utils.showSnackBar(title: 'Make name cannot be Empty');
                        return;
                      }
                      if (addMakeModelController.models.isEmpty) {
                        Utils.showSnackBar(title: 'Models cannot be empty');
                        return;
                      }
                      await addMakeModelController
                          .addUpdateMakeModesInDatabase();

                      if (context.mounted) Navigator.pop(context);
                    },
                    title: widget.makeModelsModel != null
                        ? "Add/Edit Models"
                        : "Add Make and Models",
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
