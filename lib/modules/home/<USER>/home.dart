import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';

class Home extends StatefulWidget {
  const Home({super.key});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> {
  @override
  void initState() {
    super.initState();
    Get.put(HomeScreenController());
  }

  @override
  Widget build(BuildContext context) {
    HomeScreenController homeScreenController =
        Get.find<HomeScreenController>();
    return GetBuilder<HomeScreenController>(
      builder: (_) {
        return Scaffold(
          appBar: AppBar(
            automaticallyImplyLeading: false,
            title: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  homeScreenController.franchiseDetails?.garageName ?? "",
                  style: TextStyle(
                    color: colorsConstants.primaryRed,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),

                const Spacer(),
                InkWell(
                  onLongPress: () async {
                    final packageInfo = await PackageInfo.fromPlatform();

                    Utils.showSnackBar(
                        title:
                            "Build: ${packageInfo.buildNumber} / Version: ${packageInfo.version}");
                  },
                  child: Image.asset(
                    assetsConstants.logo,
                    // height: 80.h,
                    width: 80.w,
                    fit: BoxFit.fill,
                  ),
                ),

                // Column(
                //   children: [
                //     Text(
                //       homeScreenController.franchiseDetails?.garageName ?? "",
                //       style: TextStyle(
                //         color: colorsConstants.primaryRed,
                //         fontSize: 14.sp,
                //         fontWeight: FontWeight.bold,
                //       ),
                //     ),
                //     HeightBox(2.h),
                //     Text(
                //       homeScreenController.franchiseDetails?.ownerName ?? "",
                //       style: TextStyle(
                //         color: colorsConstants.hintGrey,
                //         fontSize: 12.sp,
                //       ),
                //     ),
                //   ],
                // ),
              ],
            ),
            elevation: 0.5,
            backgroundColor: colorsConstants.whiteColor,
            shadowColor: colorsConstants.whiteColor,
            surfaceTintColor: colorsConstants.whiteColor,
          ),
          body: Builder(
            builder: (context) {
              return homeScreenController.getSelectedScreen();
            },
          ),
          bottomNavigationBar: BottomNavigationBar(
            type: BottomNavigationBarType.fixed,
            currentIndex: homeScreenController.activeScreen.index,
            onTap: (int selectedIndex) {
              homeScreenController.changeActiveScreen(
                  BottomNavigationBarScreens.values[selectedIndex]);
            },
            items: const [
              BottomNavigationBarItem(
                label: "Services",
                icon: Icon(Icons.handyman_rounded),
              ),
              BottomNavigationBarItem(
                label: "Parts",
                icon: Icon(Icons.perm_data_setting_rounded),
              ),
              BottomNavigationBarItem(
                label: "Accounts",
                icon: Icon(Icons.account_tree_rounded),
              ),
              BottomNavigationBarItem(
                label: "More",
                icon: Icon(Icons.menu_rounded),
              ),
            ],
          ),
        );
      },
    );
  }
}
