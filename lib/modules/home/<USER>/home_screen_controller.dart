import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/advertise_model.dart';
import 'package:speed_force_franchise/models/franchise_details.dart';
import 'package:speed_force_franchise/models/selected_images_model.dart';
import 'package:speed_force_franchise/models/vendor_model.dart';
import 'package:speed_force_franchise/modules/accounts/views/accounts.dart';
import 'package:speed_force_franchise/modules/more/views/more.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_franchise/modules/parts/views/parts.dart';
import 'package:speed_force_franchise/modules/services/views/services.dart';

class HomeScreenController extends GetxController {
  BottomNavigationBarScreens activeScreen = BottomNavigationBarScreens.services;

  FranchiseDetails? franchiseDetails;
  Map<String, CustomerDetailsModel> customers = {};
  List<VendorModel> vendors = [];

  List<AdvertiseModel> advertises = [];

  HomeScreenController() {
    fetchFrenciseDetails();
    fetchCustomers();
    fetchVendors();
    fetchAdvertises();
  }

  Future<void> fetchAdvertises() async {
    QuerySnapshot<Map<String, dynamic>> adQuerySnapshot =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.advertises.name)
            .get();

    for (var adDocSnapshot in adQuerySnapshot.docs) {
      advertises.add(AdvertiseModel.fromMap(adDocSnapshot.data()));
    }

    update();
  }

  Future<void> fetchFrenciseDetails() async {
    String userUID = FirebaseAuth.instance.currentUser?.uid ?? "";
    if (userUID.isNotEmpty) {
      DocumentSnapshot<Map<String, dynamic>> franchiseDocument =
          await FirebaseFirestore.instance
              .collection(FirebaseCollections.franchises.name)
              .doc(userUID)
              .get();

      Map<String, dynamic>? franchiseData = franchiseDocument.data();

      if (franchiseData != null) {
        franchiseDetails = FranchiseDetails.fromMap(franchiseData);
      }
    }
  }

  Future<void> fetchCustomers() async {
    QuerySnapshot<Map<String, dynamic>> customersCollection =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.customers.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .get();
    for (QueryDocumentSnapshot<Map<String, dynamic>> customerSnapshot
        in customersCollection.docs) {
      Map<String, dynamic> customerData = customerSnapshot.data();
      CustomerDetailsModel customerModel =
          CustomerDetailsModel.fromMap(customerData);
      customers[customerSnapshot.id] = customerModel
        ..customerId = customerSnapshot.id;
    }

    update();
  }

  Future<void> fetchVendors() async {
    vendors.clear();
    QuerySnapshot<Map<String, dynamic>> vendorsCollection =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.vendors.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .get();

    for (QueryDocumentSnapshot<Map<String, dynamic>> vendorSnapshot
        in vendorsCollection.docs) {
      Map<String, dynamic> vendorData = vendorSnapshot.data();
      VendorModel vendorModel = VendorModel.fromMap(vendorData);
      vendors.add(vendorModel);
    }

    update();
  }

  void changeActiveScreen(BottomNavigationBarScreens selectedScreen) {
    activeScreen = selectedScreen;
    update();
  }

  Future<List<String>> uploadImages(
      {required String folderPath,
      required List<SelectedImageModel> imagesToUpload}) async {
    List<String> uploadedFilesLinks = [];
    try {
      for (var image in imagesToUpload) {
        await FirebaseStorage.instance
            .ref(folderPath)
            .child(image.name)
            .putData(
                image.uInt8List, SettableMetadata(contentType: 'image/jpeg'))
            .then((TaskSnapshot taskSnapshot) async {
          uploadedFilesLinks.add(await taskSnapshot.ref.getDownloadURL());
        });
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return uploadedFilesLinks;
  }

  Widget getSelectedScreen() {
    switch (activeScreen) {
      case BottomNavigationBarScreens.services:
        return const Services();
      case BottomNavigationBarScreens.parts:
        return const Parts();
      case BottomNavigationBarScreens.accounts:
        return const Accounts();
      case BottomNavigationBarScreens.more:
        return const More();
      default:
        return const Services();
    }
  }
}
