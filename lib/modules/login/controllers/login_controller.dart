import 'dart:async';
import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';

const dummyEmail = '<EMAIL>';
const dummyOtp = '000000';

class LoginController extends GetxController {
  TextEditingController mobileNumberTextEditingController =
      TextEditingController();

  TextEditingController otpTextEditingController = TextEditingController();

  bool otpSent = false;
  String? verificationId;

  String retryOtpId = "RetryOtpId";
  int otpRetryTimeInSeconds = 40;
  int? newUserEmailOtp;
  DateTime? newUserEmailOtpSentOn;

  Timer? timer;

  Stopwatch retryOtpStopWatch = Stopwatch();

  requestOTPEmail({required BuildContext context}) async {
    try {
      final otp = Random().nextInt(999999);

      QuerySnapshot<Map<String, dynamic>> franchisesCollection =
          await FirebaseFirestore.instance
              .collection(FirebaseCollections.franchises.name)
              .where('email',
                  isEqualTo: mobileNumberTextEditingController.text.trim())
              .get();
      if (franchisesCollection.docs.isNotEmpty) {
        // // Document Exist
        debugPrint("Old User");
        // Save Otp
        newUserEmailOtp = otp;
        newUserEmailOtpSentOn = DateTime.now();
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.franchises.name)
            .doc(franchisesCollection.docs.first.id)
            .update({
          'otp': otp,
          'otpTime': FieldValue.serverTimestamp(),
        });
        // Send OTP Email
        await FBFunctions.ff.httpsCallable('sendOtpEmail').call(
          {
            'email': mobileNumberTextEditingController.text.trim(),
            'otp': otp,
          },
        );
      }

      // await FirebaseFunctions.instance.httpsCallable('sendOtpEmail').call(
      //   {
      //     'email': mobileNumberTextEditingController.text.trim(),
      //     'otp': otp,
      //   },
      // );

      // UserCredential userCredential = await FirebaseAuth.instance
      //     .signInWithEmailAndPassword(
      //         email: mobileNumberTextEditingController.text,
      //         password: "default");

      otpSent = true;
      retryOtpStopWatch.start();

      timer = Timer.periodic(const Duration(seconds: 1), (_) {
        if (timer!.tick > otpRetryTimeInSeconds) {
          timer?.cancel();
          update();
        } else {
          update([retryOtpId]);
        }
      });
      if (context.mounted) {
        context.loaderOverlay.hide();
      }
      // print(userCredential);
      update();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  requestOTPContactNumber({required BuildContext context}) async {
    try {
      await FirebaseAuth.instance.verifyPhoneNumber(
        phoneNumber: '+91${mobileNumberTextEditingController.text}',
        verificationCompleted: (PhoneAuthCredential credential) {
          debugPrint("Verification Done");
          if (context.mounted) {
            context.loaderOverlay.hide();
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          Utils.showSnackBar(
            title: "Verification Failed",
            message: e.toString(),
            snackBarType: SnackBarType.error,
          );
          if (context.mounted) {
            context.loaderOverlay.hide();
          }
        },
        codeSent: (String verificationId, int? resendToken) {
          // Utils.showSnackBar(title: "OTP has been send");
          otpSent = true;
          this.verificationId = verificationId;
          retryOtpStopWatch.start();

          timer = Timer.periodic(const Duration(seconds: 1), (_) {
            if (timer!.tick > otpRetryTimeInSeconds) {
              timer?.cancel();
              update();
            } else {
              update([retryOtpId]);
            }
          });
          if (context.mounted) {
            context.loaderOverlay.hide();
          }
          update();
        },
        codeAutoRetrievalTimeout: (String verificationId) {},
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<String?> validateEmail({required String email}) async {
    QuerySnapshot<Map<String, dynamic>> franchisesSnapshot =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.franchises.name)
            .where("email", isEqualTo: email)
            .get();
    if (franchisesSnapshot.docs.isNotEmpty) {
      debugPrint("user exist");

      Map<String, dynamic> mapData = franchisesSnapshot.docs[0].data();

      if (mapData["notavailable"] != null && mapData["notavailable"] == false) {
        return null;
      }

      return "Your account is disabled by admin.";
    }
    debugPrint("user not exist");

    return "Workshop doesn't exist";
  }

  Future<String?> validatePhoneNumber({required String phoneNumber}) async {
    QuerySnapshot<Map<String, dynamic>> franchisesSnapshot =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.franchises.name)
            .where("contactNumber", isEqualTo: "+91$phoneNumber")
            .get();

    if (franchisesSnapshot.docs.isNotEmpty) {
      debugPrint("user exist");

      Map<String, dynamic> mapData = franchisesSnapshot.docs[0].data();

      if (mapData["notavailable"] != null && mapData["notavailable"] == false) {
        return null;
      }

      return "Your account is disabled by admin.";
    }
    debugPrint("user not exist");

    return "Workshop doesn't exist";
  }

  Future<bool> validateUser() async {
    String userUID = FirebaseAuth.instance.currentUser?.uid ?? "";
    DocumentSnapshot<Map<String, dynamic>> userDocument =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.franchises.name)
            .doc(userUID)
            .get();

    if (userDocument.exists) {
      debugPrint("exist");
      return true;
    } else {
      debugPrint("not exist");
      return false;
    }
  }

  Future<void> signInWithSmsCode() async {
    if (verificationId != null) {
      final PhoneAuthCredential authCredential = PhoneAuthProvider.credential(
        smsCode: otpTextEditingController.text,
        verificationId: verificationId!,
      );
      try {
        UserCredential userCredential =
            await FirebaseAuth.instance.signInWithCredential(authCredential);
        // userCredential.user.uid
        if (!await validateUser()) {
          await logout();
          Utils.showSnackBar(
            title: "Workshop doesn't exist",
            message: "Please contact admin",
            snackBarType: SnackBarType.error,
          );
        }
        debugPrint(userCredential.user?.phoneNumber ?? "");
        update();
      } on PlatformException catch (e) {
        debugPrint("${e.code}: ${e.message}");
        // throw _firebaseErrorFactory.getException(e.code);
      } on FirebaseAuthException catch (e) {
        debugPrint(e.message);
        // throw _firebaseErrorFactory
        //     .getException('ERROR_INVALID_VERIFICATION_CODE');
      }
    }
  }

  confirmEmailOTP() async {
    try {
      final res = await FirebaseFirestore.instance
          .collection(FirebaseCollections.franchises.name)
          .where('email',
              isEqualTo: mobileNumberTextEditingController.text.trim())
          .get();

      if (res.docs.isNotEmpty) {
        // // Doc Found
        debugPrint("Old User");
        final userDocData = res.docs.first;
        if (mobileNumberTextEditingController.text.trim() == dummyEmail) {
          if (dummyOtp == otpTextEditingController.text.trim()) {
            await FirebaseAuth.instance.signInWithEmailAndPassword(
                email: mobileNumberTextEditingController.text,
                password: res.docs.first['password'].toString());
          }
        }
        if (userDocData['otp'].toString() == otpTextEditingController.text &&
            (userDocData['otpTime']
                    ?.toDate()
                    .add(const Duration(minutes: 5))
                    .isAfter(DateTime.now()) ??
                false)) {
          await FirebaseAuth.instance.signInWithEmailAndPassword(
            email: mobileNumberTextEditingController.text.trim(),
            password: res.docs.first['password'].toString(),
          );
        }
      }

      update();
    } on FirebaseAuthException catch (e) {
      debugPrint(e.toString());
      debugPrint(e.code.toString());
      if (e.code == 'invalid-verification-code') {
        Utils.showSnackBar(title: "Invalid OTP!");
      } else {
        Utils.showSnackBar(
            title: e.message?.toString() ?? "Something went wrong!");
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void clearDataOnLogin() {
    otpSent = false;
    mobileNumberTextEditingController.clear();
    otpTextEditingController.clear();
    timer?.cancel();
    timer = null;
  }

  void resetScreen() {
    otpSent = false;
    timer = null;
    update();
  }

  Future<void> logout({BuildContext? context}) async {
    /// Method to Logout the `FirebaseUser` (`_firebaseUser`)
    try {
      print("object");
      // signout code
      await FirebaseAuth.instance.signOut();
      if (context != null) {
        moveToLoginScreen(context: context);
      }
    } catch (e) {
      // print(e.toString());
    }
  }
}
