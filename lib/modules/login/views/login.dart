import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/login/controllers/login_controller.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';

class Login extends StatefulWidget {
  const Login({super.key});

  @override
  State<Login> createState() => _LoginState();
}

class _LoginState extends State<Login> {
  final _numberFormKey = GlobalKey<FormState>();
  final _otpFormKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    // Get.put(LoginController());
  }

  @override
  void didChangeDependencies() async {
    super.didChangeDependencies();
    if (!await Utils.checkVersionSupported()) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return const AlertDialog(
            content: Text("Please update the app to continue"),
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
        backgroundColor: colorsConstants.whiteColor,
        appBar: AppBar(
          title: Text(
            "Lets get started!",
            style: TextStyle(color: colorsConstants.primaryRed),
          ),
          elevation: 0.5,
          backgroundColor: colorsConstants.whiteColor,
          shadowColor: colorsConstants.whiteColor,
          surfaceTintColor: colorsConstants.whiteColor,
        ),
        body: GetBuilder<LoginController>(
          builder: (_) {
            LoginController loginController = Get.find<LoginController>();
            loginController.update();

            if (FirebaseAuth.instance.currentUser != null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                // loginController.resetScreen();
                loginController.clearDataOnLogin();
                moveToHomeScreen(context: context);
                // loginController.logout();
                // moveToAddGarageScreen(context: context);
              });
            }

            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          HeightBox(16.h),
                          Image.asset(assetsConstants.logo,
                              height: 120.h,
                              width: double.maxFinite,
                              fit: BoxFit.fill),
                          HeightBox(24.h),
                          Form(
                            key: _numberFormKey,
                            child: CustomTextField(
                              hintText: "Enter Email Address",
                              isRequired: true,
                              controller: loginController
                                  .mobileNumberTextEditingController,
                              // keyboardType: TextInputType.phone,
                              maxLine: 1,
                              // prefixText: "+91 ",
                              // inputFormatters: [
                              //   LengthLimitingTextInputFormatter(10)
                              // ],
                              // validatorFunctions: [
                              //   (value) {
                              //     return value.length == 10
                              //         ? null
                              //         : "Not valid mobile number";
                              //   }
                              // ],
                            ),
                          ),
                          if (loginController.otpSent) ...[
                            HeightBox(16.h),
                            Form(
                              key: _otpFormKey,
                              child: CustomTextField(
                                hintText: "Type the OTP from email",
                                isRequired: true,
                                keyboardType: TextInputType.number,
                                controller:
                                    loginController.otpTextEditingController,
                                maxLine: 1,
                                inputFormatters: [
                                  LengthLimitingTextInputFormatter(6)
                                ],
                              ),
                            ),
                            // HeightBox(10.h),
                            // Align(
                            //     alignment: Alignment.centerRight,
                            //     child: TextButton(
                            //         onPressed: () {},
                            //         child: Text(
                            //           "Resend",
                            //           style: TextStyle(
                            //               color: colorsConstants.primaryRed),
                            //         ))),
                            HeightBox(24.h),
                            Text.rich(
                              textAlign: TextAlign.center,
                              TextSpan(
                                text:
                                    'By clicking on submit, You agree to our\n',
                                style: TextStyle(
                                    color: colorsConstants.hintGrey,
                                    fontSize: 14.sp),
                                children: <InlineSpan>[
                                  TextSpan(
                                    text: 'terms and conditions',
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      color: colorsConstants.linkBlue,
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ],
                          HeightBox(24.h),
                          if (loginController.timer != null) ...[
                            HeightBox(16.h),
                            GetBuilder<LoginController>(
                              id: loginController.retryOtpId,
                              builder: (_) {
                                int remainingSeconds =
                                    loginController.otpRetryTimeInSeconds -
                                        loginController.timer!.tick;

                                return GestureDetector(
                                  behavior: HitTestBehavior.opaque,
                                  onTap: () async {
                                    if (remainingSeconds <= 0) {
                                      // loginController.resetScreen();
                                      FocusManager.instance.primaryFocus
                                          ?.unfocus();

                                      String? message =
                                          await loginController.validateEmail(
                                              email: loginController
                                                  .mobileNumberTextEditingController
                                                  .text
                                                  .trim());

                                      if (message != null) {
                                        Utils.showSnackBar(
                                          title: message,
                                          message: "Please contact admin",
                                          snackBarType: SnackBarType.error,
                                        );
                                        return;
                                      }
                                      if (context.mounted) {
                                        context.loaderOverlay.show(
                                            progress: "Requesting OTP...");
                                        await loginController.requestOTPEmail(
                                            context: context);
                                        // await loginController.requestOTPContactNumber(
                                        //     context: context);

                                        if (context.mounted) {
                                          context.loaderOverlay.hide();
                                        }
                                      }
                                    }
                                  },
                                  child: Text(
                                    remainingSeconds <= 0
                                        ? "Resend OTP"
                                        : "Retry in $remainingSeconds",
                                    style: remainingSeconds <= 0
                                        ? TextStyle(
                                            color: colorsConstants.primaryRed,
                                            fontWeight: FontWeight.bold,
                                          )
                                        : null,
                                  ),
                                );
                              },
                            ),
                            HeightBox(16.h),
                          ],
                        ],
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () async {
                      try {
                        if (!loginController.otpSent &&
                            _numberFormKey.currentState != null &&
                            _numberFormKey.currentState!.validate()) {
                          FocusManager.instance.primaryFocus?.unfocus();

                          String? message = await loginController.validateEmail(
                              email: loginController
                                  .mobileNumberTextEditingController.text
                                  .trim());

                          if (message != null) {
                            Utils.showSnackBar(
                              title: message,
                              message: "Please contact admin",
                              snackBarType: SnackBarType.error,
                            );
                            return;
                          }
                          if (context.mounted) {
                            context.loaderOverlay
                                .show(progress: "Requesting OTP...");
                            await loginController.requestOTPEmail(
                                context: context);
                            // await loginController.requestOTPContactNumber(
                            //     context: context);

                            if (context.mounted) {
                              context.loaderOverlay.hide();
                            }
                          }

                          // if (context.mounted) {
                          //   context.loaderOverlay.hide();
                          // }
                        } else if (loginController.otpSent &&
                            _otpFormKey.currentState != null &&
                            _otpFormKey.currentState!.validate()) {
                          FocusManager.instance.primaryFocus?.unfocus();

                          context.loaderOverlay
                              .show(progress: "Validating OTp...");
                          // await loginController.signInWithSmsCode();
                          await loginController.confirmEmailOTP();

                          if (context.mounted) {
                            context.loaderOverlay.hide();
                          }
                        }
                      } catch (e) {
                        debugPrint(e.toString());
                        if (context.mounted) {
                          context.loaderOverlay.hide();
                        }
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorsConstants.primaryRed,
                      foregroundColor: colorsConstants.whiteColor,
                      shape: ContinuousRectangleBorder(
                        borderRadius: BorderRadius.circular(5.r),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                            loginController.otpSent ? "Submit" : "Request Otp"),
                      ],
                    ),
                  ),
                  HeightBox(20.h)
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
