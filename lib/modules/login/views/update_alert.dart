import 'package:flutter/material.dart';

class UpdateAlert extends StatelessWidget {
  const UpdateAlert({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        padding: const EdgeInsets.all(40),
        height: MediaQuery.sizeOf(context).height,
        decoration: const BoxDecoration(
            image: DecorationImage(
                image: AssetImage("assets/images/Image.png"),
                opacity: .75,
                fit: BoxFit.cover)),
        child: Center(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/images/version_upgrade.png',
              height: 100,
            ),
            const SizedBox(height: 20),
            const Text(
              "Version not supported. Please upgrade app version from Play Store/App Store.",
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
            ),
          ],
        )),
      ),
    );
  }
}
