import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/vehicle_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';

class CustomerDetailsController extends GetxController {
  CustomerDetailsModel? customerDetailsModel;

  TextEditingController userNameTextEditingController = TextEditingController();
  TextEditingController phoneNumberTextEditingController =
      TextEditingController();
  TextEditingController emailTextEditingController = TextEditingController();
  TextEditingController addressTextEditingController = TextEditingController();
  TextEditingController gstinTextEditingController = TextEditingController();

  List<RepairOrderModel> repairOrders = [];

  List<String> gstTypes = ['Without GST', 'With GST'];
  String? selectedGsttype;
  bool igst = false;

  Future<void> fetchCustomerDetails({required String customerId}) async {
    QuerySnapshot<Map<String, dynamic>> customersCollection =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.customers.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .where("customerId", isEqualTo: customerId)
            .get();

    for (QueryDocumentSnapshot<Map<String, dynamic>> customerSnapshot
        in customersCollection.docs) {
      Map<String, dynamic> customerData = customerSnapshot.data();
      customerDetailsModel = CustomerDetailsModel.fromMap(customerData);
    }

    await fetchCustomerRepairOrders(customerId: customerId);

    update();
  }

  Future<void> fetchCustomerRepairOrders({required String customerId}) async {
    QuerySnapshot<Map<String, dynamic>> ordersCollection =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.orders.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .where("customerId", isEqualTo: customerId)
            .get();

    for (QueryDocumentSnapshot<Map<String, dynamic>> ordersSnapshot
        in ordersCollection.docs) {
      Map<String, dynamic> orderData = ordersSnapshot.data();
      RepairOrderModel repairOrderModel =
          RepairOrderModel.fromMap(ordersSnapshot.id, orderData);

      repairOrderModel.customerDetailsModel = customerDetailsModel;

      repairOrderModel.vehicleDetailsModel = await fetchCustomerVehicleDetails(
          vehicleId: repairOrderModel.vehicleId ?? "");

      repairOrders.add(repairOrderModel);
    }

    update();
  }

  Future<VehicleDetailsModel?> fetchCustomerVehicleDetails(
      {required String vehicleId}) async {
    VehicleDetailsModel? vehicleDetailsModel;

    DocumentSnapshot<Map<String, dynamic>> vehicleDocument =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.customerVehicles.name)
            .doc(vehicleId)
            .get();

    Map<String, dynamic>? vehicleMap = vehicleDocument.data();
    if (vehicleMap != null) {
      vehicleDetailsModel = VehicleDetailsModel.fromMap(vehicleMap);
    }

    return vehicleDetailsModel;
  }

  Future<void> updateCustomerDetails() async {
    if (userNameTextEditingController.text.isEmpty ||
        phoneNumberTextEditingController.text.isEmpty) {
      Utils.showSnackBar(
        title:
            "Fields marked * are mandatory, Please fill all mandatory fields.",
      );
      return;
    }

    customerDetailsModel?.username = userNameTextEditingController.text;
    customerDetailsModel?.phone = phoneNumberTextEditingController.text;
    customerDetailsModel?.email = emailTextEditingController.text;
    customerDetailsModel?.address = addressTextEditingController.text;
    customerDetailsModel?.gstin = gstinTextEditingController.text;

    CollectionReference<Map<String, dynamic>> customerCollection =
        FirebaseFirestore.instance
            .collection(FirebaseCollections.customers.name);

    await customerCollection
        .doc(customerDetailsModel?.customerId)
        .set(customerDetailsModel!.toMap());
    update();
  }
}
