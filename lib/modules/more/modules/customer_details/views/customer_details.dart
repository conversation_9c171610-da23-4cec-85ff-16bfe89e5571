import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/more/modules/customer_details/controllers/customer_details_controller.dart';
import 'package:speed_force_franchise/modules/more/modules/customer_details/widgets/repair_order_card.dart';
import 'package:speed_force_franchise/modules/orders/models/details_card_model.dart';
import 'package:speed_force_franchise/modules/orders/widgets/details_card.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';
import 'package:speed_force_franchise/widgets/small_primary_button.dart';

class CustomerDetails extends StatefulWidget {
  const CustomerDetails({super.key, required this.customerId});
  final String customerId;

  @override
  State<CustomerDetails> createState() => _CustomerDetailsState();
}

class _CustomerDetailsState extends State<CustomerDetails> {
  @override
  void initState() {
    Get.put(CustomerDetailsController()
      ..fetchCustomerDetails(customerId: widget.customerId));
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<CustomerDetailsController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CustomerDetailsController>(
        builder: (customerDetailsController) {
      return Scaffold(
        appBar: AppBar(
          title: const Text("Details"),
          elevation: 0.5,
          backgroundColor: colorsConstants.whiteColor,
          shadowColor: colorsConstants.whiteColor,
          surfaceTintColor: colorsConstants.whiteColor,
        ),
        body: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    HeightBox(40.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Column(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(500.r),
                              child: Image.asset(
                                height: 100.h,
                                assetsConstants.profilePlaceholder,
                              ),
                            ),
                            HeightBox(10.h),
                            Row(
                              children: [
                                Text(customerDetailsController
                                        .customerDetailsModel?.username ??
                                    ""),
                                WidthBox(10.w),
                                InkWell(
                                  onTap: () {
                                    showDialog(
                                      context: context,
                                      builder: (context) {
                                        return editCustomerDetailsDialog(
                                            context);
                                      },
                                    );
                                  },
                                  child: Icon(
                                    Icons.edit,
                                    color: colorsConstants.hintGrey,
                                  ),
                                )
                              ],
                            ),
                            HeightBox(10.h),
                            Text(customerDetailsController
                                    .customerDetailsModel?.phone ??
                                ""),
                            HeightBox(10.h),
                            SmallPrimaryButton(
                              onPress: () {
                                moveToAddCreateRepairOrderAddCustomer(
                                  context: context,
                                  vehicleOnly: true,
                                  customerId: customerDetailsController
                                      .customerDetailsModel?.customerId,
                                );
                              },
                              title: "Vehicles",
                            ),
                          ],
                        ),
                      ],
                    ),
                    HeightBox(16.h),
                    InkWell(
                      onTap: () {},
                      child: Container(
                        margin: EdgeInsets.symmetric(
                            horizontal: 16.w, vertical: 10.h),
                        decoration: BoxDecoration(
                          boxShadow: Constants.boxShadow,
                          color: colorsConstants.whiteColor,
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                children: [
                                  Text(
                                    "OUTSTANDING",
                                    style: TextStyle(
                                      color: colorsConstants.primaryRed,
                                      fontSize: 16.sp,
                                    ),
                                  ),
                                  Text(
                                    "${Constants.rupeeSign}${customerDetailsController.repairOrders.fold(
                                      0.0,
                                      (previousValue, element) {
                                        return previousValue +
                                            (element.repairDetailsModel
                                                    ?.paymentDue ??
                                                0.0);
                                      },
                                    )}",
                                    style: TextStyle(
                                      color: colorsConstants.primaryRed,
                                      fontSize: 16.sp,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Expanded(
                              child: Column(
                                children: [
                                  Text(
                                    "PAID",
                                    style: TextStyle(
                                      color: Colors.green,
                                      fontSize: 16.sp,
                                    ),
                                  ),
                                  Text(
                                    "${Constants.rupeeSign}${customerDetailsController.repairOrders.fold(
                                      0.0,
                                      (previousValue, element) {
                                        return previousValue +
                                            (element.repairDetailsModel
                                                    ?.paymentReceived ??
                                                0.0);
                                      },
                                    )}",
                                    style: TextStyle(
                                      color: Colors.green,
                                      fontSize: 16.sp,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    HeightBox(20.h),
                    Container(
                      decoration:
                          BoxDecoration(color: colorsConstants.slateGrey),
                      padding: EdgeInsets.symmetric(vertical: 10.h),
                      width: double.maxFinite,
                      alignment: Alignment.center,
                      child: Text(
                        "ORDERS",
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: colorsConstants.hintGrey,
                        ),
                      ),
                    ),
                    ...customerDetailsController.repairOrders
                        .map((repairOrder) {
                      return InkWell(
                        onTap: () {
                          moveToRepairOrderDetailsScreen(
                              context: context,
                              orderId: repairOrder.orderId ?? "");
                        },
                        child: RepairOrderCard(repairOrderModel: repairOrder),
                      );
                    }),
                    HeightBox(20.h),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  AlertDialog editCustomerDetailsDialog(BuildContext context) {
    CustomerDetailsController customerDetailsController =
        Get.find<CustomerDetailsController>();
    return AlertDialog(
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          DetailsCard(
            cardTitle: "Personal Details",
            /* extraFields: [
              Divider(
                height: 2.h,
                color: colorsConstants.slateGrey,
              ),
              Row(
                children: [
                  ...List.generate(
                    customerDetailsController.gstTypes.length,
                    (index) {
                      return Row(
                        children: [
                          Radio<String>(
                            value: customerDetailsController.gstTypes[index],
                            groupValue:
                                customerDetailsController.selectedGsttype,
                            onChanged: (value) {
                              customerDetailsController.selectedGsttype = value;
                              setState(() {});
                            },
                          ),
                          // SizedBox(width: 10),
                          Text(customerDetailsController.gstTypes[index])
                        ],
                      );
                    },
                  ),
                ],
              ),
              if (customerDetailsController.selectedGsttype ==
                  customerDetailsController.gstTypes[1]) ...[
                const SizedBox(width: 5),
                Row(
                  children: [
                    Checkbox(
                      value: customerDetailsController.igst,
                      onChanged: (value) {
                        if (value != null) {
                          customerDetailsController.igst = value;
                          setState(() {});
                        }
                      },
                    ),
                    const Text('IGST')
                  ],
                )
              ]
              // ElevatedButton(
              //     onPressed: () {}, child: Text("data"))
            ],
             */
            detailsCardTextsFieldData: [
              DetailsCardModel(
                hintText: "User Name*",
                textEditingController:
                    customerDetailsController.userNameTextEditingController
                      ..text = customerDetailsController
                              .customerDetailsModel?.username ??
                          "",
                prefixIcon: Icon(
                  CupertinoIcons.person_alt,
                  size: 24.sp,
                ),
              ),
              DetailsCardModel(
                hintText: "Phone Number*",
                keyboardType: TextInputType.phone,
                inputFormatters: [
                  LengthLimitingTextInputFormatter(10),
                ],
                textEditingController: customerDetailsController
                    .phoneNumberTextEditingController
                  ..text =
                      customerDetailsController.customerDetailsModel?.phone ??
                          "",
                prefixIcon: Icon(
                  CupertinoIcons.phone_fill,
                  size: 24.sp,
                ),
                suffixIcon: IconButton(
                  icon: Icon(
                    Icons.contact_phone_outlined,
                    size: 24.sp,
                  ),
                  onPressed: () async {
                    if (await FlutterContacts.requestPermission()) {
                      Contact? contact =
                          await FlutterContacts.openExternalPick();

                      if (contact != null) {
                        customerDetailsController
                            .phoneNumberTextEditingController
                            .text = contact.phones[0].number;
                      }
                    }
                  },
                ),
              ),
              DetailsCardModel(
                hintText: "Email Address",
                textEditingController: customerDetailsController
                    .emailTextEditingController
                  ..text =
                      customerDetailsController.customerDetailsModel?.email ??
                          "",
                prefixIcon: Icon(
                  CupertinoIcons.mail_solid,
                  size: 24.sp,
                ),
              ),
              DetailsCardModel(
                hintText: "Address",
                textEditingController: customerDetailsController
                    .addressTextEditingController
                  ..text =
                      customerDetailsController.customerDetailsModel?.address ??
                          "",
                prefixIcon: Icon(
                  Icons.location_pin,
                  size: 24.sp,
                ),
              ),
              DetailsCardModel(
                hintText: "GSTIN",
                textEditingController: customerDetailsController
                    .gstinTextEditingController
                  ..text =
                      customerDetailsController.customerDetailsModel?.gstin ??
                          "",
                prefixIcon: Icon(
                  CupertinoIcons.doc_text_fill,
                  size: 24.sp,
                ),
              ),
            ],
          ),
          HeightBox(16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: PrimaryButton(
                  title: "Close",
                  backgroundColor: colorsConstants.hintGrey,
                  onPress: () {
                    Navigator.pop(context);
                  },
                ),
              ),
              WidthBox(10.w),
              Expanded(
                child: PrimaryButton(
                  title: "Save",
                  onPress: () async {
                    await customerDetailsController.updateCustomerDetails();

                    await Get.find<HomeScreenController>().fetchCustomers();
                    if (context.mounted) {
                      Navigator.pop(context);
                    }

                    setState(() {});
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
