import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/enums/enums.dart';

class RepairOrderCard extends StatefulWidget {
  const RepairOrderCard({super.key, required this.repairOrderModel});

  final RepairOrderModel repairOrderModel;

  @override
  State<RepairOrderCard> createState() => _RepairOrderCardState();
}

class _RepairOrderCardState extends State<RepairOrderCard> {
  bool isExpnaded = false;

  @override
  Widget build(BuildContext context) {
    RepairOrderModel repairOrderModel = widget.repairOrderModel;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
      decoration: BoxDecoration(
        boxShadow: Constants.boxShadow,
        color: colorsConstants.whiteColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 5.w,
                  vertical: 5.h,
                ),
                decoration: BoxDecoration(
                  color: repairOrderModel.orderStatus?.getColor(),
                  // boxShadow: Constants.boxShadow,
                ),
                child: Container(
                  alignment: Alignment.center,
                  child: Text(
                    repairOrderModel.orderStatus?.name ?? "",
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: colorsConstants.whiteColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              WidthBox(10.w),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "${Constants.rupeeSign}${repairOrderModel.repairDetailsModel?.total.toString()}",
                    style: TextStyle(color: colorsConstants.hintGrey),
                  ),
                  WidthBox(2.w),
                  Text(
                    "(Due: ${Constants.rupeeSign}${repairOrderModel.repairDetailsModel?.paymentDue.toString()})",
                    style: TextStyle(
                        color: colorsConstants.primaryRed, fontSize: 10.sp),
                  ),
                ],
              ),
              const Spacer(),
              if (repairOrderModel.createdAt != null &&
                  repairOrderModel.createdAt!.isNotEmpty) ...[
                Expanded(
                  child: Text(
                    DateFormat("dd MMM yyyy hh:m").format(
                      DateTime.parse(
                        repairOrderModel.createdAt ?? "",
                      ),
                    ),
                    style: TextStyle(
                        color: colorsConstants.hintGrey, fontSize: 10.sp),
                  ),
                ),
              ],
            ],
          ),
          HeightBox(5.h),
          Text(
            repairOrderModel.customerDetailsModel?.username ?? "",
            style: TextStyle(fontSize: 16.sp),
          ),
          HeightBox(5.h),
          Text(
            "(${repairOrderModel.customerDetailsModel?.phone ?? ""})",
            style: TextStyle(fontSize: 12.sp, color: colorsConstants.hintGrey),
          ),
          HeightBox(5.h),
          Text(
            "${repairOrderModel.vehicleDetailsModel?.make} ${repairOrderModel.vehicleDetailsModel?.model}",
            style: TextStyle(fontSize: 16.sp),
          ),
          Row(
            children: [
              Text(
                "(${repairOrderModel.vehicleDetailsModel?.registrationNumber})",
                style:
                    TextStyle(fontSize: 12.sp, color: colorsConstants.hintGrey),
              ),
              const Spacer(),
              InkWell(
                onTap: () {
                  setState(() {
                    isExpnaded = !isExpnaded;
                  });
                },
                child: Text(
                  isExpnaded ? "Hide Details" : "View Details",
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: colorsConstants.primaryRed,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          if (isExpnaded) ...[
            HeightBox(10.h),
            Container(
              decoration: BoxDecoration(color: colorsConstants.slateGrey),
              padding: EdgeInsets.symmetric(vertical: 10.h),
              width: double.maxFinite,
              alignment: Alignment.center,
              child: Text(
                "Parts/Services",
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: colorsConstants.hintGrey,
                ),
              ),
            ),
            HeightBox(5.h),
            Row(
              children: [
                Expanded(
                  child: Text(
                    "Name",
                    style: TextStyle(
                      color: colorsConstants.hintGrey,
                      fontWeight: FontWeight.bold,
                      fontSize: 12.sp,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    "Qty",
                    style: TextStyle(
                      color: colorsConstants.hintGrey,
                      fontWeight: FontWeight.bold,
                      fontSize: 12.sp,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    "Price",
                    style: TextStyle(
                      color: colorsConstants.hintGrey,
                      fontWeight: FontWeight.bold,
                      fontSize: 12.sp,
                    ),
                  ),
                ),
              ],
            ),
            HeightBox(5.h),
            ...[
              ...repairOrderModel.repairDetailsModel?.services ?? [],
              ...repairOrderModel.repairDetailsModel?.parts ?? []
            ].map((servicePart) {
              return Row(
                children: [
                  Expanded(
                    child: Text(
                      servicePart.title ?? "-",
                      style: TextStyle(
                        color: colorsConstants.hintGrey,
                        fontSize: 12.sp,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      servicePart.quantity == null
                          ? "-"
                          : servicePart.quantity.toString(),
                      style: TextStyle(
                        color: colorsConstants.hintGrey,
                        fontSize: 12.sp,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      servicePart.rate == null
                          ? "-"
                          : servicePart.rate.toString(),
                      style: TextStyle(
                        color: colorsConstants.hintGrey,
                        fontSize: 12.sp,
                      ),
                    ),
                  ),
                ],
              );
            }),
          ]
        ],
      ),
    );
  }
}
