import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/more/modules/my_customers/controllers/my_customers_controller.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

class MyCustomers extends StatefulWidget {
  const MyCustomers({super.key});

  @override
  State<MyCustomers> createState() => _MyCustomersState();
}

class _MyCustomersState extends State<MyCustomers> {
  @override
  void initState() {
    Get.put(
      MyCustomersController(
        homeScreenController: Get.find<HomeScreenController>()
          ..fetchCustomers(),
      ),
    );
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<MyCustomersController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<MyCustomersController>(builder: (myCustomersController) {
      return Scaffold(
        appBar: AppBar(
          title: Text(
              "My Customers (${myCustomersController.homeScreenController.customers.length})"),
          elevation: 0.5,
          backgroundColor: colorsConstants.whiteColor,
          shadowColor: colorsConstants.whiteColor,
          surfaceTintColor: colorsConstants.whiteColor,
        ),
        body: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    ...myCustomersController
                        .homeScreenController.customers.values
                        .map((customer) {
                      return InkWell(
                        onTap: () {
                          moveToCustomerDetailsScreen(
                              context: context,
                              customerId: customer.customerId ?? "");
                        },
                        child: Container(
                          margin: EdgeInsets.symmetric(
                              vertical: 5.h, horizontal: 16.w),
                          padding: EdgeInsets.symmetric(
                              horizontal: 16.w, vertical: 10.h),
                          decoration: BoxDecoration(
                            boxShadow: Constants.boxShadow,
                            color: colorsConstants.whiteColor,
                          ),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Text(
                                    "User Name:",
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      color: colorsConstants.hintGrey,
                                    ),
                                  ),
                                  WidthBox(10.w),
                                  Text(
                                    customer.username ?? "",
                                    style: TextStyle(fontSize: 16.sp),
                                  ),
                                ],
                              ),
                              HeightBox(10.h),
                              Row(
                                children: [
                                  Text(
                                    "Phone:",
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      color: colorsConstants.hintGrey,
                                    ),
                                  ),
                                  WidthBox(10.w),
                                  Text(
                                    customer.phone ?? "",
                                    style: TextStyle(fontSize: 16.sp),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      );
                    }),
                  ],
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 16.w),
              width: double.maxFinite,
              child: PrimaryButton(
                onPress: () async {
                  moveToAddCreateRepairOrderAddCustomer(
                    context: context,
                    addCustomerOnly: true,
                    revertCallback: (_) async {
                      await myCustomersController.homeScreenController
                          .fetchCustomers();

                      myCustomersController.update();
                    },
                  );
                },
                title: "Add Customer",
              ),
            ),
          ],
        ),
      );
    });
  }
}
