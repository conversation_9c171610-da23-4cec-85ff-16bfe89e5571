import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/more/modules/my_vendors/controllers/my_vendors_controller.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

class MyVendors extends StatefulWidget {
  const MyVendors({super.key});

  @override
  State<MyVendors> createState() => _MyVendorsState();
}

class _MyVendorsState extends State<MyVendors> {
  @override
  void initState() {
    Get.put(
      MyVendorsController(
        homeScreenController: Get.find<HomeScreenController>(),
      ),
    );
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<MyVendorsController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<MyVendorsController>(
      builder: (myVendorsController) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
                "My Vendors (${myVendorsController.homeScreenController.vendors.length})"),
            elevation: 0.5,
            backgroundColor: colorsConstants.whiteColor,
            shadowColor: colorsConstants.whiteColor,
            surfaceTintColor: colorsConstants.whiteColor,
          ),
          body: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      ...myVendorsController.homeScreenController.vendors
                          .map((vendor) {
                        return InkWell(
                          onTap: () {
                            // moveToCustomerDetailsScreen(
                            //     context: context,
                            //     customerId: vendor.vendorId ?? "");
                          },
                          child: Container(
                            margin: EdgeInsets.symmetric(
                                vertical: 5.h, horizontal: 16.w),
                            padding: EdgeInsets.symmetric(
                                horizontal: 16.w, vertical: 10.h),
                            decoration: BoxDecoration(
                              boxShadow: Constants.boxShadow,
                              color: colorsConstants.whiteColor,
                            ),
                            child: Column(
                              children: [
                                if (vendor.name != null &&
                                    vendor.name!.isNotEmpty) ...[
                                  Row(
                                    children: [
                                      Text(
                                        "User Name:",
                                        style: TextStyle(
                                          fontSize: 16.sp,
                                          color: colorsConstants.hintGrey,
                                        ),
                                      ),
                                      WidthBox(10.w),
                                      Text(
                                        vendor.name ?? "",
                                        style: TextStyle(fontSize: 16.sp),
                                      ),
                                    ],
                                  ),
                                ],
                                if (vendor.phone != null &&
                                    vendor.phone!.isNotEmpty) ...[
                                  HeightBox(10.h),
                                  Row(
                                    children: [
                                      Text(
                                        "Phone:",
                                        style: TextStyle(
                                          fontSize: 16.sp,
                                          color: colorsConstants.hintGrey,
                                        ),
                                      ),
                                      WidthBox(10.w),
                                      Text(
                                        vendor.phone ?? "",
                                        style: TextStyle(fontSize: 16.sp),
                                      ),
                                    ],
                                  ),
                                ],
                                if (vendor.email != null &&
                                    vendor.email!.isNotEmpty) ...[
                                  HeightBox(10.h),
                                  Row(
                                    children: [
                                      Text(
                                        "Email:",
                                        style: TextStyle(
                                          fontSize: 16.sp,
                                          color: colorsConstants.hintGrey,
                                        ),
                                      ),
                                      WidthBox(10.w),
                                      Text(
                                        vendor.email ?? "",
                                        style: TextStyle(fontSize: 16.sp),
                                      ),
                                    ],
                                  ),
                                ],
                              ],
                            ),
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 16.w),
                width: double.maxFinite,
                child: PrimaryButton(
                  onPress: () async {
                    moveToAddVendorScreen(
                        context: context,
                        revertCallback: (dynamic vendor) async {
                          if (vendor != null) {
                            await Get.find<HomeScreenController>()
                                .fetchVendors();
                            myVendorsController.update();
                          }
                        });
                    // moveToAddCreateRepairOrderAddCustomer(
                    //   context: context,
                    //   addCustomerOnly: true,
                    //   revertCallback: (_) async {
                    //     await myVendorsController.homeScreenController
                    //         .fetchCustomers();

                    //     myVendorsController.update();
                    //   },
                    // );
                  },
                  title: "Add Vendor",
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
