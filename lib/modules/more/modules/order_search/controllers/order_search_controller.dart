import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/vehicle_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class OrderSearchController extends GetxController {
  SearchOrdersBy searchOrdersBy = SearchOrdersBy.jobcards;

  TextEditingController searchTextEditingController = TextEditingController();

  List<RepairOrderModel> repairOrders = [];

  void updateSearchBy({required SearchOrdersBy searchBy}) {
    searchOrdersBy = searchBy;
    update();
  }

  String getHintText() {
    if (searchOrdersBy == SearchOrdersBy.jobcards) {
      return "Enter jobcard number";
    } else if (searchOrdersBy == SearchOrdersBy.invoiceNumber) {
      return "Enter invoice number";
    } else {
      return "By date";
    }
  }

  Future<void> fetchOrders() async {
    QuerySnapshot<Map<String, dynamic>> ordersCollection =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.orders.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .get();

    for (QueryDocumentSnapshot<Map<String, dynamic>> ordersSnapshot
        in ordersCollection.docs) {
      Map<String, dynamic> orderData = ordersSnapshot.data();
      RepairOrderModel repairOrderModel =
          RepairOrderModel.fromMap(ordersSnapshot.id, orderData);

      repairOrderModel.customerDetailsModel = await fetchCustomerDetails(
          customerId: repairOrderModel.customerId ?? "");

      repairOrderModel.vehicleDetailsModel = await fetchCustomerVehicleDetails(
          vehicleId: repairOrderModel.vehicleId ?? "");

      repairOrders.add(repairOrderModel);
    }

    update();
  }

  Future<CustomerDetailsModel?> fetchCustomerDetails(
      {required String customerId}) async {
    CustomerDetailsModel? customerDetailsModel;

    DocumentSnapshot<Map<String, dynamic>> vehicleDocument =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.customers.name)
            .doc(customerId)
            .get();

    Map<String, dynamic>? vehicleMap = vehicleDocument.data();
    if (vehicleMap != null) {
      customerDetailsModel = CustomerDetailsModel.fromMap(vehicleMap);
    }

    return customerDetailsModel;
  }

  Future<VehicleDetailsModel?> fetchCustomerVehicleDetails(
      {required String vehicleId}) async {
    VehicleDetailsModel? vehicleDetailsModel;

    DocumentSnapshot<Map<String, dynamic>> vehicleDocument =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.customerVehicles.name)
            .doc(vehicleId)
            .get();

    Map<String, dynamic>? vehicleMap = vehicleDocument.data();
    if (vehicleMap != null) {
      vehicleDetailsModel = VehicleDetailsModel.fromMap(vehicleMap);
    }

    return vehicleDetailsModel;
  }
}
