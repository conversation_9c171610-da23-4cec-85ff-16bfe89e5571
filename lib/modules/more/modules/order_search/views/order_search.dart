import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/more/modules/customer_details/widgets/repair_order_card.dart';
import 'package:speed_force_franchise/modules/more/modules/order_search/controllers/order_search_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

class OrderSearch extends StatefulWidget {
  const OrderSearch({super.key});

  @override
  State<OrderSearch> createState() => _OrderSearchState();
}

class _OrderSearchState extends State<OrderSearch> {
  @override
  void initState() {
    Get.put(
      OrderSearchController()..fetchOrders(),
    );
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<OrderSearchController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrderSearchController>(
      builder: (orderSearchController) {
        return Scaffold(
          appBar: AppBar(
            title: const Text("Search orders by"),
            elevation: 0.5,
            backgroundColor: colorsConstants.whiteColor,
            shadowColor: colorsConstants.whiteColor,
            surfaceTintColor: colorsConstants.whiteColor,
          ),
          body: GestureDetector(
            onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        HeightBox(20.h),
                        Container(
                          margin: EdgeInsets.symmetric(horizontal: 16.sp),
                          child: InkWell(
                            onTap: () {
                              orderSearchController.updateSearchBy(
                                  searchBy: SearchOrdersBy.jobcards);
                            },
                            child: Row(
                              children: [
                                Container(
                                  padding:
                                      orderSearchController.searchOrdersBy ==
                                              SearchOrdersBy.jobcards
                                          ? EdgeInsets.all(2.sp)
                                          : EdgeInsets.all(7.sp),
                                  margin: EdgeInsets.symmetric(vertical: 10.sp),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: colorsConstants.primaryRed,
                                      width: 2.sp,
                                    ),
                                  ),
                                  child: orderSearchController.searchOrdersBy ==
                                          SearchOrdersBy.jobcards
                                      ? Container(
                                          padding: EdgeInsets.all(5.sp),
                                          decoration: BoxDecoration(
                                              color: colorsConstants.primaryRed,
                                              shape: BoxShape.circle),
                                        )
                                      : null,
                                ),
                                WidthBox(10.w),
                                const Text("By Jobcards/Repair Order number"),
                              ],
                            ),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.symmetric(horizontal: 16.sp),
                          child: InkWell(
                            onTap: () {
                              orderSearchController.updateSearchBy(
                                  searchBy: SearchOrdersBy.invoiceNumber);
                            },
                            child: Row(
                              children: [
                                Container(
                                  padding:
                                      orderSearchController.searchOrdersBy ==
                                              SearchOrdersBy.invoiceNumber
                                          ? EdgeInsets.all(2.sp)
                                          : EdgeInsets.all(7.sp),
                                  margin: EdgeInsets.symmetric(vertical: 10.sp),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: colorsConstants.primaryRed,
                                      width: 2.sp,
                                    ),
                                  ),
                                  child: orderSearchController.searchOrdersBy ==
                                          SearchOrdersBy.invoiceNumber
                                      ? Container(
                                          padding: EdgeInsets.all(5.sp),
                                          decoration: BoxDecoration(
                                              color: colorsConstants.primaryRed,
                                              shape: BoxShape.circle),
                                        )
                                      : null,
                                ),
                                WidthBox(10.w),
                                const Text("By invoice number"),
                              ],
                            ),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.symmetric(horizontal: 16.sp),
                          child: InkWell(
                            onTap: () {
                              orderSearchController.updateSearchBy(
                                  searchBy: SearchOrdersBy.dateRange);
                            },
                            child: Row(
                              children: [
                                Container(
                                  padding:
                                      orderSearchController.searchOrdersBy ==
                                              SearchOrdersBy.dateRange
                                          ? EdgeInsets.all(2.sp)
                                          : EdgeInsets.all(7.sp),
                                  margin: EdgeInsets.symmetric(vertical: 10.sp),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: colorsConstants.primaryRed,
                                      width: 2.sp,
                                    ),
                                  ),
                                  child: orderSearchController.searchOrdersBy ==
                                          SearchOrdersBy.dateRange
                                      ? Container(
                                          padding: EdgeInsets.all(5.sp),
                                          decoration: BoxDecoration(
                                              color: colorsConstants.primaryRed,
                                              shape: BoxShape.circle),
                                        )
                                      : null,
                                ),
                                WidthBox(10.w),
                                const Text("By date range"),
                              ],
                            ),
                          ),
                        ),
                        HeightBox(20.sp),
                        Container(
                          margin: EdgeInsets.symmetric(horizontal: 16.w),
                          child: CustomTextField(
                            hintText: orderSearchController.getHintText(),
                            controller: orderSearchController
                                .searchTextEditingController,
                            readOnly: orderSearchController.searchOrdersBy ==
                                    SearchOrdersBy.dateRange
                                ? true
                                : false,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 10.w, vertical: 10.h),
                            suffix: orderSearchController.searchOrdersBy ==
                                    SearchOrdersBy.dateRange
                                ? const Icon(CupertinoIcons.chevron_down)
                                : null,
                            filled: true,
                            fillColor: colorsConstants.whiteColor,
                            onTap: () {
                              if (orderSearchController.searchOrdersBy ==
                                  SearchOrdersBy.dateRange) {
                                showDialog(
                                  context: context,
                                  builder: (context) {
                                    return selectDateRangeDialog(context);
                                  },
                                );
                              }
                            },
                            onChange: (value) {},
                            focusedBorder: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.primaryRed),
                            ),
                          ),
                        ),
                        HeightBox(20.h),
                        PrimaryButton(
                          onPress: () {
                            Utils.showSnackBar(title: "Coming Soon!");
                          },
                          title: "Search",
                        ),
                        HeightBox(10.h),
                        ...orderSearchController.repairOrders.map((order) {
                          return InkWell(
                            onTap: () {
                              Utils.showSnackBar(title: "Coming Soon!");
                              // moveToRepairOrderDetailsScreen(
                              //     context: context,
                              //     orderId: repairOrder.orderId ?? "");
                            },
                            child: RepairOrderCard(repairOrderModel: order),
                          );
                        }),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  AlertDialog selectDateRangeDialog(BuildContext context) {
    OrderSearchController orderSearchController =
        Get.find<OrderSearchController>();

    return AlertDialog(
      content: ConstrainedBox(
          constraints: BoxConstraints(maxHeight: 300.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "By Date",
                style: TextStyle(fontSize: 22.sp, fontWeight: FontWeight.bold),
              ),
              const Divider(),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ...SearchDateRange.values.map(
                        (rangeType) {
                          return InkWell(
                            onTap: () {
                              orderSearchController.searchTextEditingController
                                  .text = rangeType.name;
                              Navigator.pop(context);
                            },
                            child: Row(
                              children: [
                                Container(
                                  padding: EdgeInsets.all(8.sp),
                                  margin: EdgeInsets.symmetric(vertical: 10.sp),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: colorsConstants.primaryRed,
                                      width: 2.sp,
                                    ),
                                  ),
                                ),
                                WidthBox(10.w),
                                Text(rangeType.name),
                              ],
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
              HeightBox(10.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  PrimaryButton(
                    onPress: () {
                      Navigator.pop(context);
                    },
                    title: "Ok",
                  ),
                ],
              )
            ],
          )),
    );
  }
}
