import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/vehicle_details_model.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class VehicleSearchController extends GetxController {
  SearchVehiclesBy searchVehicleBy = SearchVehiclesBy.registrationNumber;

  TextEditingController searchTextEditingController = TextEditingController();

  List<VehicleDetailsModel> vehicles = [];
  Map<String, CustomerDetailsModel> customersData = {};

  List<VehicleDetailsModel> searchedVehicles = [];

  void updateSearchBy({required SearchVehiclesBy searchBy}) {
    searchVehicleBy = searchBy;
    searchTextEditingController.clear();
    searchedVehicles.clear();
    update();
  }

  Future<void> fetchCustomersVehicles() async {
    vehicles.clear();
    customersData.clear();

    QuerySnapshot<Map<String, dynamic>> vehiclesQuerySnapShot =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.customerVehicles.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .get();

    for (QueryDocumentSnapshot<Map<String, dynamic>> vehiclesDocs
        in vehiclesQuerySnapShot.docs) {
      Map<String, dynamic> vehicleData = vehiclesDocs.data();

      VehicleDetailsModel vehicleDetailsModel =
          VehicleDetailsModel.fromMap(vehicleData);

      vehicles.add(vehicleDetailsModel);

      CustomerDetailsModel? customerDetailsModel = await fetchCustomerDetails(
          customerId: vehicleDetailsModel.customerId ?? "");

      if (customerDetailsModel != null) {
        customersData[vehicleDetailsModel.vehicleId ?? ""] =
            customerDetailsModel;
      }
    }

    update();
  }

  void searchVehicles({required String searchTerm}) {
    searchedVehicles = vehicles.where((vehicle) {
      if (searchVehicleBy == SearchVehiclesBy.registrationNumber) {
        if (vehicle.registrationNumber?.toLowerCase().contains(searchTerm) ??
            false) {
          return true;
        }
      } else if (searchVehicleBy == SearchVehiclesBy.customerNumber) {
        if (vehicle.customerId?.toLowerCase().contains(searchTerm) ?? false) {
          return true;
        }
      } else {
        if (vehicle.chasisNumber?.toLowerCase().contains(searchTerm) ?? false) {
          return true;
        }
      }
      return false;
    }).toList();

    update();
  }

  String getQueryField() {
    if (searchVehicleBy == SearchVehiclesBy.registrationNumber) {
      return "registrationNumber";
    } else if (searchVehicleBy == SearchVehiclesBy.customerNumber) {
      return "customerId";
    } else {
      return "chasisNumber";
    }
  }

  Future<CustomerDetailsModel?> fetchCustomerDetails(
      {required String customerId}) async {
    CustomerDetailsModel? customerDetailsModel;

    DocumentSnapshot<Map<String, dynamic>> vehicleDocument =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.customers.name)
            .doc(customerId)
            .get();

    Map<String, dynamic>? vehicleMap = vehicleDocument.data();
    if (vehicleMap != null) {
      customerDetailsModel = CustomerDetailsModel.fromMap(vehicleMap);
    }

    return customerDetailsModel;
  }
}
