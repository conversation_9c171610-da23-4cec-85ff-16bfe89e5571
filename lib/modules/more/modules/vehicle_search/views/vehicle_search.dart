import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/more/modules/vehicle_search/controllers/vehicle_search_controller.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

class VehicleSearch extends StatefulWidget {
  const VehicleSearch({super.key});

  @override
  State<VehicleSearch> createState() => _VehicleSearchState();
}

class _VehicleSearchState extends State<VehicleSearch> {
  @override
  void initState() {
    Get.put(
      VehicleSearchController()..fetchCustomersVehicles(),
    );
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<VehicleSearchController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<VehicleSearchController>(
      builder: (vehicleSearchController) {
        return Scaffold(
          appBar: AppBar(
            title: const Text("Search vehicles by"),
            elevation: 0.5,
            backgroundColor: colorsConstants.whiteColor,
            shadowColor: colorsConstants.whiteColor,
            surfaceTintColor: colorsConstants.whiteColor,
          ),
          body: GestureDetector(
            onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        HeightBox(20.h),
                        Container(
                          margin: EdgeInsets.symmetric(horizontal: 16.sp),
                          child: InkWell(
                            onTap: () {
                              vehicleSearchController.updateSearchBy(
                                  searchBy:
                                      SearchVehiclesBy.registrationNumber);
                            },
                            child: Row(
                              children: [
                                Container(
                                  padding: vehicleSearchController
                                              .searchVehicleBy ==
                                          SearchVehiclesBy.registrationNumber
                                      ? EdgeInsets.all(2.sp)
                                      : EdgeInsets.all(7.sp),
                                  margin: EdgeInsets.symmetric(vertical: 10.sp),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: colorsConstants.primaryRed,
                                      width: 2.sp,
                                    ),
                                  ),
                                  child: vehicleSearchController
                                              .searchVehicleBy ==
                                          SearchVehiclesBy.registrationNumber
                                      ? Container(
                                          padding: EdgeInsets.all(5.sp),
                                          decoration: BoxDecoration(
                                              color: colorsConstants.primaryRed,
                                              shape: BoxShape.circle),
                                        )
                                      : null,
                                ),
                                WidthBox(10.w),
                                const Text("By vehicle registration number"),
                              ],
                            ),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.symmetric(horizontal: 16.sp),
                          child: InkWell(
                            onTap: () {
                              vehicleSearchController.updateSearchBy(
                                  searchBy: SearchVehiclesBy.customerNumber);
                            },
                            child: Row(
                              children: [
                                Container(
                                  padding:
                                      vehicleSearchController.searchVehicleBy ==
                                              SearchVehiclesBy.customerNumber
                                          ? EdgeInsets.all(2.sp)
                                          : EdgeInsets.all(7.sp),
                                  margin: EdgeInsets.symmetric(vertical: 10.sp),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: colorsConstants.primaryRed,
                                      width: 2.sp,
                                    ),
                                  ),
                                  child: vehicleSearchController
                                              .searchVehicleBy ==
                                          SearchVehiclesBy.customerNumber
                                      ? Container(
                                          padding: EdgeInsets.all(5.sp),
                                          decoration: BoxDecoration(
                                              color: colorsConstants.primaryRed,
                                              shape: BoxShape.circle),
                                        )
                                      : null,
                                ),
                                WidthBox(10.w),
                                const Text("By customer number"),
                              ],
                            ),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.symmetric(horizontal: 16.sp),
                          child: InkWell(
                            onTap: () {
                              vehicleSearchController.updateSearchBy(
                                  searchBy: SearchVehiclesBy.vinNumber);
                            },
                            child: Row(
                              children: [
                                Container(
                                  padding:
                                      vehicleSearchController.searchVehicleBy ==
                                              SearchVehiclesBy.vinNumber
                                          ? EdgeInsets.all(2.sp)
                                          : EdgeInsets.all(7.sp),
                                  margin: EdgeInsets.symmetric(vertical: 10.sp),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: colorsConstants.primaryRed,
                                      width: 2.sp,
                                    ),
                                  ),
                                  child: vehicleSearchController
                                              .searchVehicleBy ==
                                          SearchVehiclesBy.vinNumber
                                      ? Container(
                                          padding: EdgeInsets.all(5.sp),
                                          decoration: BoxDecoration(
                                              color: colorsConstants.primaryRed,
                                              shape: BoxShape.circle),
                                        )
                                      : null,
                                ),
                                WidthBox(10.w),
                                const Text("By VIN number"),
                              ],
                            ),
                          ),
                        ),
                        HeightBox(20.sp),
                        Container(
                          margin: EdgeInsets.symmetric(horizontal: 16.w),
                          alignment: Alignment.centerLeft,
                          child: const Text("Search Vehicle"),
                        ),
                        HeightBox(5.h),
                        Container(
                          margin: EdgeInsets.symmetric(horizontal: 16.w),
                          child: CustomTextField(
                            hintText: "Search",
                            controller: vehicleSearchController
                                .searchTextEditingController,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 10.w, vertical: 10.h),
                            filled: true,
                            fillColor: colorsConstants.whiteColor,
                            onChange: (value) {},
                            prefix: Icon(
                              Icons.search,
                              size: 20.sp,
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.primaryRed),
                            ),
                          ),
                        ),
                        HeightBox(20.h),
                        PrimaryButton(
                          onPress: () {
                            vehicleSearchController.searchVehicles(
                                searchTerm: vehicleSearchController
                                    .searchTextEditingController.text);
                          },
                          title: "Search",
                        ),
                        HeightBox(10.h),
                        ...vehicleSearchController.searchedVehicles
                            .map((vehicle) {
                          return InkWell(
                            onTap: () {
                              moveToViewOrdersScreen(
                                context: context,
                                vehicleId: vehicle.vehicleId,
                              );
                            },
                            child: Container(
                              margin: EdgeInsets.symmetric(
                                  vertical: 5.h, horizontal: 16.w),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 10.w, vertical: 10.h),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: Colors.lightBlue.withOpacity(0.5),
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(vehicle.registrationNumber ?? ""),
                                  HeightBox(10.h),
                                  Row(
                                    children: [
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            vehicleSearchController
                                                    .customersData[
                                                        vehicle.vehicleId ?? ""]
                                                    ?.username ??
                                                "",
                                            style: TextStyle(
                                              color: colorsConstants.hintGrey,
                                            ),
                                          ),
                                          Text(
                                            "(${vehicleSearchController.customersData[vehicle.vehicleId ?? ""]?.phone})",
                                            style: TextStyle(
                                              color: colorsConstants.hintGrey,
                                            ),
                                          )
                                        ],
                                      ),
                                      const Spacer(),
                                      Icon(
                                        Icons.arrow_forward_ios_sharp,
                                        color: colorsConstants.primaryRed,
                                      )
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          );
                        }),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
