import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/accounts/controllers/accounts_controller.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/login/controllers/login_controller.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class More extends StatefulWidget {
  const More({super.key});

  @override
  State<More> createState() => _MoreState();
}

class _MoreState extends State<More> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
        child: Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              moveToMyCustomersScreen(context: context);
            },
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Icon(
                  CupertinoIcons.person_2_alt,
                  size: 25.sp,
                ),
                WidthBox(20.w),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      HeightBox(10.h),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Expanded(child: Text("My Customers")),
                          const Spacer(),
                          Icon(
                            Icons.arrow_right_outlined,
                            size: 30.sp,
                          ),
                        ],
                      ),
                      HeightBox(5.h),
                      const Divider(),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // InkWell(
          //   onTap: () {
          //     moveToMyVendorsScreen(context: context);
          //   },
          //   child: Row(
          //     crossAxisAlignment: CrossAxisAlignment.center,
          //     children: [
          //       Icon(
          //         Icons.people_rounded,
          //         size: 25.sp,
          //       ),
          //       WidthBox(20.w),
          //       Expanded(
          //         child: Column(
          //           mainAxisAlignment: MainAxisAlignment.center,
          //           children: [
          //             HeightBox(10.h),
          //             Row(
          //               crossAxisAlignment: CrossAxisAlignment.center,
          //               children: [
          //                 const Expanded(child: Text("My Vendors")),
          //                 const Spacer(),
          //                 Icon(
          //                   Icons.arrow_right_outlined,
          //                   size: 30.sp,
          //                 ),
          //               ],
          //             ),
          //             HeightBox(5.h),
          //             const Divider(),
          //           ],
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
          // InkWell(
          //   onTap: () {
          //     moveToOrderSearchScreen(context: context);
          //   },
          //   child: Row(
          //     crossAxisAlignment: CrossAxisAlignment.center,
          //     children: [
          //       Icon(
          //         Icons.file_copy,
          //         size: 25.sp,
          //       ),
          //       WidthBox(20.w),
          //       Expanded(
          //         child: Column(
          //           mainAxisAlignment: MainAxisAlignment.center,
          //           children: [
          //             HeightBox(10.h),
          //             Row(
          //               crossAxisAlignment: CrossAxisAlignment.center,
          //               children: [
          //                 const Expanded(child: Text("Order Search")),
          //                 const Spacer(),
          //                 Icon(
          //                   Icons.arrow_right_outlined,
          //                   size: 30.sp,
          //                 ),
          //               ],
          //             ),
          //             HeightBox(5.h),
          //             const Divider(),
          //           ],
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
          InkWell(
            onTap: () {
              moveToVehicleSearchScreen(context: context);
            },
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Icon(
                  Icons.motorcycle_rounded,
                  size: 25.sp,
                ),
                WidthBox(20.w),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      HeightBox(10.h),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Expanded(child: Text("Vehicle Search")),
                          const Spacer(),
                          Icon(
                            Icons.arrow_right_outlined,
                            size: 30.sp,
                          ),
                        ],
                      ),
                      HeightBox(5.h),
                      const Divider(),
                    ],
                  ),
                ),
              ],
            ),
          ),
          InkWell(
            onTap: () {
              moveToReportsScreen(context: context);
            },
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Icon(
                  CupertinoIcons.chart_bar_alt_fill,
                  size: 25.sp,
                ),
                WidthBox(20.w),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      HeightBox(10.h),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Expanded(child: Text("Reports")),
                          const Spacer(),
                          Icon(
                            Icons.arrow_right_outlined,
                            size: 30.sp,
                          ),
                        ],
                      ),
                      HeightBox(5.h),
                      const Divider(),
                    ],
                  ),
                ),
              ],
            ),
          ),
          InkWell(
            onTap: () async {
              bool loggingOut = false;
              showDialog(
                context: context,
                builder: (context2) {
                  return StatefulBuilder(builder: (context, setState2) {
                    return AlertDialog(
                      backgroundColor: Colors.white,
                      surfaceTintColor: Colors.white,
                      title: const Text(
                        "Delete",
                        style: TextStyle(fontSize: 25, color: Colors.black),
                      ),
                      content: const Text(
                        'Are you sure you want to delete?',
                        style: TextStyle(fontSize: 15, color: Colors.black),
                      ),
                      // actionsPadding: EdgeInsets.all(0),
                      actionsAlignment: MainAxisAlignment.center,
                      actions: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            loggingOut
                                ? SizedBox(
                                    height: 40,
                                    width: 40,
                                    child: CircularProgressIndicator())
                                : ElevatedButton(
                                    onPressed: () async {
                                      loggingOut = true;
                                      setState2(() {});
                                      Get.find<HomeScreenController>()
                                          .changeActiveScreen(
                                              BottomNavigationBarScreens
                                                  .services);
                                      await Get.find<LoginController>()
                                          .logout(context: context2);
                                      Get.find<AccountsController>().update();

                                      Navigator.of(context).pop();
                                      loggingOut = false;
                                      setState2(() {});
                                      setState(() {});
                                    },
                                    style: ElevatedButton.styleFrom(
                                        elevation: 0,
                                        foregroundColor: Colors.white,
                                        backgroundColor: Colors.redAccent),
                                    child: const Text(
                                      "Yes",
                                      style: TextStyle(fontSize: 16),
                                    )),
                            const SizedBox(width: 10),
                            ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                                style: ElevatedButton.styleFrom(
                                    elevation: 0,
                                    foregroundColor: Colors.white,
                                    backgroundColor: Colors.green),
                                child: const Text(
                                  "No",
                                  style: TextStyle(fontSize: 16),
                                )),
                          ],
                        ),
                      ],
                    );
                  });
                },
              );
              // Get.find<HomeScreenController>()
              //     .changeActiveScreen(BottomNavigationBarScreens.services);
              // await Get.find<LoginController>().logout(context: context);
            },
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Icon(
                  Icons.login,
                  size: 25.sp,
                ),
                WidthBox(20.w),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      HeightBox(10.h),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Expanded(child: Text("Logout")),
                          const Spacer(),
                          Icon(
                            Icons.arrow_right_outlined,
                            size: 30.sp,
                          ),
                        ],
                      ),
                      HeightBox(5.h),
                      const Divider(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ));
    // Center(
    //   child:
    //   ElevatedButton(
    //     onPressed: () {
    //       Get.find<HomeScreenController>()
    //           .changeActiveScreen(BottomNavigationBarScreens.services);
    //       Get.find<LoginController>().logout(context: context);
    //     },
    //     style: ElevatedButton.styleFrom(
    //       backgroundColor: colorsConstants.primaryRed,
    //       foregroundColor: colorsConstants.whiteColor,
    //       shape: ContinuousRectangleBorder(
    //         borderRadius: BorderRadius.circular(5.r),
    //       ),
    //     ),
    //     child: const Row(
    //       mainAxisAlignment: MainAxisAlignment.center,
    //       children: [
    //         Text("Logout"),
    //       ],
    //     ),
    //   ),
    // );
  }
}
