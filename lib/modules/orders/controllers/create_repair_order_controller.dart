import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/selected_images_model.dart';
import 'package:speed_force_franchise/modules/add_make_models/models/make_models_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/orders/controllers/repair_details_controller.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/additional_information_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/insurance_provider_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/vehicle_details_model.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';
import 'package:uuid/uuid.dart';

class CreateRepairOrderController extends GetxController {
  final idGenerator = const Uuid();

  bool editVehicle = false;

  bool showSearchField = true;
  List<String> gstTypes = ['Without GST', 'With GST'];
  String? selectedGsttype;
  bool igst = false;
  OverlayPortalController makeOverlayPortalController =
      OverlayPortalController();
  OverlayPortalController modelOverlayPortalController =
      OverlayPortalController();

  OverlayPortalController vehiclesOverlayPortalController =
      OverlayPortalController();

  VehicleDetailsModel? selectedVehicle;
  TextEditingController selectedvehicleTextEditingController =
      TextEditingController();

  TextEditingController searchTextEditingController = TextEditingController();

  //Personal Details
  TextEditingController userNameTextEditingController = TextEditingController();
  TextEditingController phoneNumberTextEditingController =
      TextEditingController();
  TextEditingController emailTextEditingController = TextEditingController();
  TextEditingController addressTextEditingController = TextEditingController();
  TextEditingController gstinTextEditingController = TextEditingController();

  //Vehicle Details
  TextEditingController makeTextEditingController = TextEditingController();
  TextEditingController modelTextEditingController = TextEditingController();
  TextEditingController purchaseDateEditingController = TextEditingController();
  DateFormat purchaseDateFormat = DateFormat("MMMM-y");

  TextEditingController engineTextEditingController = TextEditingController();
  TextEditingController vinTextEditingController = TextEditingController();
  TextEditingController insuranceProviderEditingController =
      TextEditingController();
  TextEditingController insurerGSTINEditingController = TextEditingController();
  TextEditingController insurerAddressTextEditingController =
      TextEditingController();
  TextEditingController policyNumberEditingController = TextEditingController();

  TextEditingController insuranceExpiryDateEditingController =
      TextEditingController();

  TextEditingController odometerEditingController = TextEditingController();

  TextEditingController registrationNumberEditingController =
      TextEditingController();

  double fuelLevel = 10;

  InsuranceProviderModel? newCreatedInsuranceProviderModel;

  List<InsuranceProviderModel> customersInsuranceProviders = [];

  DateFormat insuranceExpiryDateFormat = DateFormat("dd-MM-y");

  List<SelectedImageModel> registrationCertificatedImages = [];

  List<SelectedImageModel> insuranceImages = [];

  List<MakeModelsModel> vehiclesDataSource = [];

  Map<String, CustomerDetailsModel> allCustomers = {};

  Map<CustomerDetailsModel, List<VehicleDetailsModel>> allCustomersVehicles =
      {};

  Map<CustomerDetailsModel, List<VehicleDetailsModel>>
      searchedCustomersVehicles = {};

  bool addingNewCustomer = false;
  bool existingCustomerSelected = false;

  DateTime? vehiclePurchaseDate;
  DateTime? insuranceExpiryDate;

  RepairOrderModel? createRepairOrderModel;

  CreateRepairOrderController() {
    allCustomers = Get.find<HomeScreenController>().customers;
  }

  Future<void> fetchCustomersVehicles() async {
    CollectionReference<Map<String, dynamic>> customerVehiclesCollection =
        FirebaseFirestore.instance
            .collection(FirebaseCollections.customerVehicles.name);

    for (MapEntry<String, CustomerDetailsModel> customer
        in allCustomers.entries) {
      QuerySnapshot<Map<String, dynamic>> vehiclesQuerySnapshot =
          await customerVehiclesCollection
              .where("franchiseId",
                  isEqualTo: FirebaseAuth.instance.currentUser?.uid)
              .where('customerId', isEqualTo: customer.key)
              .get();
      print(customer.value.username);
      print("vehiclesQuerySnapshot.size");
      print(vehiclesQuerySnapshot.size);

      for (var vehicleDoc in vehiclesQuerySnapshot.docs) {
        Map<String, dynamic>? vehicleData = vehicleDoc.data();
        VehicleDetailsModel vehicleDetailsModel =
            VehicleDetailsModel.fromMap(vehicleData);
        if (allCustomersVehicles[customer.value] == null) {
          allCustomersVehicles[customer.value] = [vehicleDetailsModel];
        } else {
          allCustomersVehicles[customer.value]?.add(vehicleDetailsModel);
        }
      }
    }
  }

  Future<void> fetchVehicles() async {
    vehiclesDataSource.clear();

    QuerySnapshot<Map<String, dynamic>> masterVehiclesCollection =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.vehiclesCompanies.name)
            .where("franchiseId", isNull: true)
            .get();

    for (var element in masterVehiclesCollection.docs) {
      Map<String, dynamic> elementData = element.data();

      MakeModelsModel makeModelsModel = MakeModelsModel.fromMap(elementData);

      vehiclesDataSource.add(makeModelsModel);
    }

    QuerySnapshot<Map<String, dynamic>> vehiclesCollection =
        await FirebaseFirestore
            .instance
            .collection(FirebaseCollections.vehiclesCompanies.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .get();

    for (var element in vehiclesCollection.docs) {
      Map<String, dynamic> elementData = element.data();

      MakeModelsModel makeModelsModel = MakeModelsModel.fromMap(elementData);

      vehiclesDataSource.add(makeModelsModel);
    }
  }

  bool validateFields() {
    if (userNameTextEditingController.text.isEmpty ||
        phoneNumberTextEditingController.text.isEmpty ||
        addressTextEditingController.text.isEmpty ||
        makeTextEditingController.text.isEmpty ||
        modelTextEditingController.text.isEmpty ||
        registrationNumberEditingController.text.isEmpty ||
        odometerEditingController.text.isEmpty) {
      return false;
    }
    return true;
  }

  void clearVehicleData() {
    makeTextEditingController.clear();
    modelTextEditingController.clear();
    registrationNumberEditingController.clear();
    purchaseDateEditingController.clear();
    engineTextEditingController.clear();
    vinTextEditingController.clear();
    insuranceProviderEditingController.clear();
    insuranceExpiryDateEditingController.clear();
  }

  void seachCustomerUsingRegistrationNumber({required String searchTerm}) {
    if (searchTerm.length >= 3) {
      searchedCustomersVehicles = {};

      bool isFound = false;

      for (CustomerDetailsModel customerData in allCustomersVehicles.keys) {
        if (customerData.username
                ?.toLowerCase()
                .contains(searchTerm.toLowerCase()) ??
            false) {
          searchedCustomersVehicles[customerData] =
              allCustomersVehicles[customerData] ?? [];
          isFound = true;
        }
      }

      if (!isFound) {
        for (MapEntry<CustomerDetailsModel,
                List<VehicleDetailsModel>> customerVehicle
            in allCustomersVehicles.entries) {
          List<VehicleDetailsModel> searchedVehicleListOfCustomer =
              customerVehicle.value
                  .where((VehicleDetailsModel vehicleDetailsModel) {
            return vehicleDetailsModel.registrationNumber
                    ?.toLowerCase()
                    .contains(searchTerm.toLowerCase()) ??
                false;
          }).toList();

          if (searchedVehicleListOfCustomer.isNotEmpty) {
            searchedCustomersVehicles[customerVehicle.key] =
                searchedVehicleListOfCustomer;
          }
        }
      }
    } else {
      searchedCustomersVehicles = {};
    }

    update();
  }

  void updateRepairOrderModel(
      {required CustomerDetailsModel customerDetails,
      required VehicleDetailsModel vehicleDetails}) {
    createRepairOrderModel = RepairOrderModel(
      customerDetailsModel: customerDetails,
      vehicleDetailsModel: vehicleDetails,
    );

    populateUserDetails();

    populateVehicleDetails();

    searchTextEditingController.clear();

    update();
  }

  void populateVehicleDetails() {
    selectedvehicleTextEditingController.text =
        "${createRepairOrderModel?.vehicleDetailsModel?.make} ${createRepairOrderModel?.vehicleDetailsModel?.model}";
    registrationNumberEditingController.text = createRepairOrderModel
            ?.vehicleDetailsModel?.registrationNumber
            ?.toUpperCase() ??
        "";
    makeTextEditingController.text =
        createRepairOrderModel?.vehicleDetailsModel?.make ?? "";

    modelTextEditingController.text =
        createRepairOrderModel?.vehicleDetailsModel?.model ?? "";

    purchaseDateEditingController.text =
        createRepairOrderModel?.vehicleDetailsModel?.purchaseDate ?? "";

    engineTextEditingController.text =
        createRepairOrderModel?.vehicleDetailsModel?.engineNumber ?? "";

    vinTextEditingController.text =
        createRepairOrderModel?.vehicleDetailsModel?.chasisNumber ?? "";

    insuranceProviderEditingController.text = createRepairOrderModel
            ?.vehicleDetailsModel?.insuranceProviderModel?.companyName ??
        "";

    insurerGSTINEditingController.text =
        createRepairOrderModel?.vehicleDetailsModel?.insurerGSTIN ?? "";

    insurerAddressTextEditingController.text =
        createRepairOrderModel?.vehicleDetailsModel?.insurerAddress ?? "";

    policyNumberEditingController.text =
        createRepairOrderModel?.vehicleDetailsModel?.policyNumber ?? "";

    insuranceExpiryDateEditingController.text =
        createRepairOrderModel?.vehicleDetailsModel?.insuranceExpiryDate ?? "";
  }

  void populateUserDetails() {
    userNameTextEditingController.text =
        createRepairOrderModel?.customerDetailsModel?.username ?? "";
    phoneNumberTextEditingController.text =
        createRepairOrderModel?.customerDetailsModel?.phone ?? "";
    emailTextEditingController.text =
        createRepairOrderModel?.customerDetailsModel?.email ?? "";
    addressTextEditingController.text =
        createRepairOrderModel?.customerDetailsModel?.address ?? "";
    gstinTextEditingController.text =
        createRepairOrderModel?.customerDetailsModel?.gstin ?? "";
  }

  Future<void> createRepairOrder(BuildContext context) async {
    List<String> existingCustomersIds = [];
    List<CustomerDetailsModel> existingCustomers = [];

    print(0);

    existingCustomers.clear();
    final existingNumberSnap = await FirebaseFirestore.instance
        .collection(FirebaseCollections.customers.name)
        .where('franchiseId', isEqualTo: FirebaseAuth.instance.currentUser?.uid)
        .where('phone', isEqualTo: phoneNumberTextEditingController.text.trim())
        .get();
    existingCustomersIds.clear();
    existingCustomers.clear();
    existingCustomersIds = existingNumberSnap.docs.map((e) => e.id).toList();

    final existingVehicleSnap = existingCustomersIds.isNotEmpty
        ? await FirebaseFirestore.instance
            .collection(FirebaseCollections.customerVehicles.name)
            .where('franchiseId',
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .where('registrationNumber',
                isEqualTo: registrationNumberEditingController.text.trim())
            .where('customerId', whereNotIn: existingCustomersIds)
            .get()
        : await FirebaseFirestore.instance
            .collection(FirebaseCollections.customerVehicles.name)
            .where('franchiseId',
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .where('registrationNumber',
                isEqualTo: registrationNumberEditingController.text.trim())
            // .where('customerId', whereNotIn: existingCustomersIds)
            .get();

    existingCustomersIds.addAll(existingVehicleSnap.docs.map(
      (e) {
        print(e.data());
        return e.data()['customerId'];
      },
    ));
    print(existingCustomersIds);

    if (existingCustomersIds.isNotEmpty) {
      final existingCustomerSnap = await FirebaseFirestore.instance
          .collection(FirebaseCollections.customers.name)
          .where(FieldPath.documentId, whereIn: existingCustomersIds)
          .get();
      existingCustomers = existingCustomerSnap.docs
          .map((e) => CustomerDetailsModel.fromMap(e.data()))
          .toList();
      if (createRepairOrderModel == null && existingCustomers.isNotEmpty) {
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return linkCustomerDialog(context, existingCustomers);
          },
        );
      }
    }
    createRepairOrderModel ??= RepairOrderModel(
      customerDetailsModel: CustomerDetailsModel(
        franchiseId: FirebaseAuth.instance.currentUser?.uid,
        username: userNameTextEditingController.text,
        phone: phoneNumberTextEditingController.text,
        email: emailTextEditingController.text,
        address: addressTextEditingController.text,
        gstin: gstinTextEditingController.text,
      ),
      vehicleDetailsModel: VehicleDetailsModel(
        franchiseId: FirebaseAuth.instance.currentUser?.uid,

        registrationNumber:
            registrationNumberEditingController.text.toUpperCase(),
        make: makeTextEditingController.text,
        model: modelTextEditingController.text,
        purchaseDate: vehiclePurchaseDate
            ?.toIso8601String(), // purchaseDateEditingController.text,
        engineNumber: engineTextEditingController.text,
        chasisNumber: vinTextEditingController.text,
        insurerGSTIN: insurerGSTINEditingController.text,
        insurerAddress: insurerAddressTextEditingController.text,
        policyNumber: policyNumberEditingController.text,
        insuranceExpiryDate: insuranceExpiryDate
            ?.toIso8601String(), // insuranceExpiryDateEditingController.text,
        insuranceProviderModel: newCreatedInsuranceProviderModel,
        registrationCertificatesImages: registrationCertificatedImages,
        insuranceImages: insuranceImages,
      ),
    );

    if (createRepairOrderModel != null) {
      createRepairOrderModel?.franchiseId =
          FirebaseAuth.instance.currentUser?.uid;
      createRepairOrderModel?.additionalInformationModel =
          AdditionalInformationModel(
        odometer: odometerEditingController.text,
        fuelLevel: fuelLevel.toString(),
      );
      if (context.mounted) {
        moveToRepairDetails(
            context: context, createRepairOrder: createRepairOrderModel!);
      }
    }
  }

  Future<void> addNewCustomerDetailsInDatabase() async {
    RepairOrderModel customerDetails = RepairOrderModel(
      franchiseId: FirebaseAuth.instance.currentUser?.uid,
      customerDetailsModel: CustomerDetailsModel(
          franchiseId: FirebaseAuth.instance.currentUser?.uid,
          username: userNameTextEditingController.text,
          phone: phoneNumberTextEditingController.text,
          email: emailTextEditingController.text,
          address: addressTextEditingController.text,
          gstin: gstinTextEditingController.text,
          lastVisit: "-",
          totalBillAmount: 0),
      vehicleDetailsModel: VehicleDetailsModel(
        franchiseId: FirebaseAuth.instance.currentUser?.uid,
        registrationNumber:
            registrationNumberEditingController.text.toUpperCase(),
        make: makeTextEditingController.text,
        model: modelTextEditingController.text,
        purchaseDate: vehiclePurchaseDate
            ?.toIso8601String(), //purchaseDateEditingController.text,
        engineNumber: engineTextEditingController.text,
        chasisNumber: vinTextEditingController.text,
        insurerGSTIN: insurerGSTINEditingController.text,
        insurerAddress: insurerAddressTextEditingController.text,
        policyNumber: policyNumberEditingController.text,
        insuranceExpiryDate: insuranceExpiryDate?.toIso8601String(),
        insuranceProviderModel: newCreatedInsuranceProviderModel,
        registrationCertificatesImages: registrationCertificatedImages,
        insuranceImages: insuranceImages,
      ),
    );
    CollectionReference<Map<String, dynamic>> customerCollection =
        FirebaseFirestore.instance
            .collection(FirebaseCollections.customers.name);

    DocumentReference<Map<String, dynamic>> newCustomer =
        await customerCollection
            .add(customerDetails.customerDetailsModel!.toMap());
    customerDetails.customerId = newCustomer.id;
    customerDetails.customerDetailsModel!.customerId = newCustomer.id;
    customerDetails.vehicleDetailsModel!.customerId = newCustomer.id;
    await customerCollection
        .doc(customerDetails.customerId)
        .set(customerDetails.customerDetailsModel!.toMap());

    CollectionReference<Map<String, dynamic>> customerVehiclesCollection =
        FirebaseFirestore.instance
            .collection(FirebaseCollections.customerVehicles.name);

    customerDetails.vehicleDetailsModel?.registrationNumber =
        customerDetails.vehicleDetailsModel?.registrationNumber!.toUpperCase();

    DocumentReference<Map<String, dynamic>> newVehicle =
        await customerVehiclesCollection
            .add(customerDetails.vehicleDetailsModel!.toMap());

    customerDetails.vehicleId = newVehicle.id;
    customerDetails.vehicleDetailsModel?.vehicleId = newVehicle.id;

    await customerVehiclesCollection
        .doc(customerDetails.vehicleId)
        .set(customerDetails.vehicleDetailsModel!.toMap());

    update();
  }

  Future<CustomerDetailsModel?> fetchCustomerDetails(
      {required String customerId}) async {
    CustomerDetailsModel? customerDetailsModel;

    QuerySnapshot<Map<String, dynamic>> customersCollection =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.customers.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .where("customerId", isEqualTo: customerId)
            .get();

    for (QueryDocumentSnapshot<Map<String, dynamic>> customerSnapshot
        in customersCollection.docs) {
      Map<String, dynamic> customerData = customerSnapshot.data();
      customerDetailsModel = CustomerDetailsModel.fromMap(customerData);
    }

    return customerDetailsModel;
  }

  Future<void> addEditVehicleDetailsInDatabase() async {
    CustomerDetailsModel? customerDetailsModel = await fetchCustomerDetails(
        customerId: selectedVehicle?.customerId ?? "");
    RepairOrderModel vehicleDetails = RepairOrderModel(
      vehicleId: selectedVehicle?.vehicleId,
      customerId: selectedVehicle?.customerId,
      franchiseId: FirebaseAuth.instance.currentUser?.uid,
      customerDetailsModel: customerDetailsModel,
      vehicleDetailsModel: VehicleDetailsModel(
        vehicleId: selectedVehicle?.vehicleId,
        customerId: selectedVehicle?.customerId,
        franchiseId: FirebaseAuth.instance.currentUser?.uid,
        registrationNumber:
            registrationNumberEditingController.text.toUpperCase(),
        make: makeTextEditingController.text,
        model: modelTextEditingController.text,
        purchaseDate: vehiclePurchaseDate
            ?.toIso8601String(), //purchaseDateEditingController.text,
        engineNumber: engineTextEditingController.text,
        chasisNumber: vinTextEditingController.text,
        insurerGSTIN: insurerGSTINEditingController.text,
        insurerAddress: insurerAddressTextEditingController.text,
        policyNumber: policyNumberEditingController.text,
        insuranceExpiryDate: insuranceExpiryDate?.toIso8601String(),
        insuranceProviderModel: newCreatedInsuranceProviderModel,
        registrationCertificatesImages: registrationCertificatedImages,
        insuranceImages: insuranceImages,
      ),
    );

    CollectionReference<Map<String, dynamic>> customerVehiclesCollection =
        FirebaseFirestore.instance
            .collection(FirebaseCollections.customerVehicles.name);

    vehicleDetails.vehicleDetailsModel?.registrationNumber =
        vehicleDetails.vehicleDetailsModel?.registrationNumber!.toUpperCase();

    if (!editVehicle) {
      DocumentReference<Map<String, dynamic>> newVehicle =
          await customerVehiclesCollection
              .add(vehicleDetails.vehicleDetailsModel!.toMap());

      vehicleDetails.vehicleId = newVehicle.id;
      vehicleDetails.vehicleDetailsModel?.vehicleId = newVehicle.id;

      await uploadInsuranceAndRegistrationCertificatesImages(vehicleDetails);

      await customerVehiclesCollection
          .doc(vehicleDetails.vehicleId)
          .set(vehicleDetails.vehicleDetailsModel!.toMap());
    } else {
      await uploadInsuranceAndRegistrationCertificatesImages(vehicleDetails);

      await customerVehiclesCollection
          .doc(vehicleDetails.vehicleId)
          .set(vehicleDetails.vehicleDetailsModel!.toMap());
    }
    await fetchCustomersVehicles();
    update();
  }

  Future<void> uploadInsuranceAndRegistrationCertificatesImages(
      RepairOrderModel vehicleDetails) async {
    if (vehicleDetails.vehicleDetailsModel?.insuranceImages?.isNotEmpty ??
        false) {
      List<String> insuranceImagesLinks = await Get.find<HomeScreenController>()
          .uploadImages(
              folderPath:
                  '${FirebaseAuth.instance.currentUser?.uid}/${vehicleDetails.customerDetailsModel?.username?.replaceAll(' ', '_')}/${vehicleDetails.vehicleDetailsModel?.make?.replaceAll(' ', '_')}_${vehicleDetails.vehicleDetailsModel?.model?.replaceAll(' ', '_')}/insurance_images',
              imagesToUpload:
                  vehicleDetails.vehicleDetailsModel?.insuranceImages ?? []);

      vehicleDetails.vehicleDetailsModel?.insuranceImagesLinks =
          insuranceImagesLinks;
    }

    if (vehicleDetails
            .vehicleDetailsModel?.registrationCertificatesImages?.isNotEmpty ??
        false) {
      List<String> registrationCertificatesImagesLinks =
          await Get.find<HomeScreenController>().uploadImages(
              folderPath:
                  '${FirebaseAuth.instance.currentUser?.uid}/${vehicleDetails.customerDetailsModel?.username?.replaceAll(' ', '_')}/${vehicleDetails.vehicleDetailsModel?.make?.replaceAll(' ', '_')}_${vehicleDetails.vehicleDetailsModel?.model?.replaceAll(' ', '_')}/registration_certificatesImages',
              imagesToUpload: vehicleDetails
                      .vehicleDetailsModel?.registrationCertificatesImages ??
                  []);

      vehicleDetails.vehicleDetailsModel?.registrationCertificatesImagesLinks =
          registrationCertificatesImagesLinks;
    }
  }

  AlertDialog linkCustomerDialog(
      BuildContext context, List<CustomerDetailsModel> existingCustomers) {
    print(existingCustomers.map((e) => e.customerId));
    return AlertDialog(
      content: ConstrainedBox(
          constraints: BoxConstraints(maxHeight: 300.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "Existing Customers",
                style: TextStyle(fontSize: 22.sp, fontWeight: FontWeight.bold),
              ),
              Text(
                "Select Customer to be linked",
                style: TextStyle(
                  color: colorsConstants.hintGrey,
                  fontSize: 16.sp,
                ),
              ),
              const Divider(),
              SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: existingCustomers.map(
                    (customer) {
                      return InkWell(
                        onTap: () {
                          createRepairOrderModel = RepairOrderModel(
                            customerDetailsModel: customer,
                            vehicleDetailsModel: VehicleDetailsModel(
                              franchiseId:
                                  FirebaseAuth.instance.currentUser?.uid,

                              registrationNumber:
                                  registrationNumberEditingController.text
                                      .toUpperCase(),
                              make: makeTextEditingController.text,
                              model: modelTextEditingController.text,
                              purchaseDate: vehiclePurchaseDate
                                  ?.toIso8601String(), //purchaseDateEditingController.text,
                              engineNumber: engineTextEditingController.text,
                              chasisNumber: vinTextEditingController.text,
                              insurerGSTIN: insurerGSTINEditingController.text,
                              insurerAddress:
                                  insurerAddressTextEditingController.text,
                              policyNumber: policyNumberEditingController.text,
                              insuranceExpiryDate:
                                  insuranceExpiryDate?.toIso8601String(),
                              insuranceProviderModel:
                                  newCreatedInsuranceProviderModel,
                              registrationCertificatesImages:
                                  registrationCertificatedImages,
                              insuranceImages: insuranceImages,
                            ),
                          );
                          Navigator.pop(context);
                        },
                        child: Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(8.sp),
                              margin: EdgeInsets.symmetric(vertical: 10.sp),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: colorsConstants.primaryRed,
                                  width: 2.sp,
                                ),
                              ),
                            ),
                            WidthBox(10.w),
                            Text(customer.username ?? ""),
                          ],
                        ),
                      );
                    },
                  ).toList(),
                ),
              ),
              HeightBox(20.h),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Expanded(child: Divider()),
                  Text(
                    "   or   ",
                    style: TextStyle(
                        fontSize: 16.sp, color: colorsConstants.hintGrey),
                  ),
                  const Expanded(child: Divider())
                ],
              ),
              HeightBox(10.h),
              PrimaryButton(
                  onPress: () {
                    Navigator.pop(context);
                  },
                  title: "Add New Customer")
            ],
          )),
    );
  }
}
