import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/service_model.dart';
import 'package:speed_force_franchise/models/selected_images_model.dart';
import 'package:speed_force_franchise/models/stock_model.dart';
import 'package:speed_force_franchise/modules/accounts/modules/add_expenses/models/expense_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/repair_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/service_part.model.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import 'package:uuid/uuid.dart';

class RepairDetailsController extends GetxController {
  RepairDetailsController({
    required this.createRepairOrderModel,
  }) {
    allCustomers = Get.find<HomeScreenController>().customers;
  }

  @override
  void onInit() {
    super.onInit();
    print("------Faisal -----${createRepairOrderModel.gstIncluded}");
    createRepairOrderModel.customerDetailsModel =
        createRepairOrderModel.customerDetailsModel;
    selectedParts = createRepairOrderModel.repairDetailsModel?.parts ?? [];
    selectedServices =
        createRepairOrderModel.repairDetailsModel?.services ?? [];
    customerRemarks =
        createRepairOrderModel.repairDetailsModel?.customerRemarks ?? [];
    selectedTags = createRepairOrderModel.repairDetailsModel?.tags ?? [];
    selectedGsttype = (createRepairOrderModel.gstIncluded ?? false)
        ? gstTypes[1]
        : gstTypes[0];
    igst = createRepairOrderModel.isigst ?? false;
    for (var selectedService in selectedServices) {
      calculateServicePartAmount(
        servicePartModel: selectedService,
      );
    }
    print(selectedParts.map((e) => e.partsGst));
    for (var selectedPart in selectedParts) {
      calculateServicePartAmount(servicePartModel: selectedPart);
    }
    print(selectedParts.map((e) => e.partsGst));
  }

  List<String> gstTypes = ['Without GST', 'With GST'];
  String? selectedGsttype = "Without GST";
  bool igst = false;
  bool isLoading = false;

  Uint8List? signature;

  RepairOrderModel createRepairOrderModel;

  final idGenerator = const Uuid();

  final OverlayPortalController addServiceOverlayPortalController =
      OverlayPortalController();
  final OverlayPortalController addPartsOverlayPortalController =
      OverlayPortalController();
  final OverlayPortalController addTagsOverlayPortalController =
      OverlayPortalController();

  final TextEditingController searchCustomerTextEditingController =
      TextEditingController();

  Map<String, CustomerDetailsModel> allCustomers = {};

  List<CustomerDetailsModel>? searchedCustomer;

  List<ServiceModel> services = [];
  List<PartModel> parts = [];
  List<String> tags = [];

  List<String> customerRemarks = ["", "", ""];

  double _laborTotal = 0.0;
  num _servicesGst = 0.0;
  num _partsGst = 0.0;

  num get servicesGst {
    return _servicesGst;
  }

  num get partsGst {
    return _partsGst;
  }

  double get laborTotal {
    return _laborTotal;
  }

  double _partsTotal = 0.0;
  double get partsTotal {
    return _partsTotal;
  }

  double get discountTotal {
    double totalServicesRate = selectedServices.fold<double>(
      0,
      (previousValue, element) {
        if (element.quantity != null && element.rate != null) {
          return previousValue + (element.quantity! * element.rate!);
        }
        return previousValue;
      },
    );

    double totalPartsRate = selectedParts.fold<double>(
      0,
      (previousValue, element) {
        if (element.quantity != null && element.rate != null) {
          return previousValue + (element.quantity! * element.rate!);
        }
        return previousValue;
      },
    );

    return (totalServicesRate + totalPartsRate) - (_laborTotal + _partsTotal);
  }

  List<ServicePartModel> selectedServices = [];

  List<ServicePartModel> selectedParts = [];
  List<String> selectedTags = [];

  List<SelectedImageModel> repairOrderImages = [];

  DateTime estimatedDeliveryDateTime = DateTime.now();

  Future<void> fetchServices() async {
    services.clear();
    QuerySnapshot<Map<String, dynamic>> servicesCollection =
        await FirebaseFirestore
            .instance
            .collection(FirebaseCollections.services.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .get();

    for (var service in servicesCollection.docs) {
      ServiceModel serviceModel = ServiceModel.fromMap(service.data());
      services.add(serviceModel);
    }
  }

  Future<void> fetchParts() async {
    parts.clear();
    QuerySnapshot<Map<String, dynamic>> partsCollection =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.parts.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .get();

    for (var part in partsCollection.docs) {
      PartModel partModel = PartModel.fromMap(part.data());

      QuerySnapshot<Map<String, dynamic>> partsStockCollection =
          await FirebaseFirestore.instance
              .collection(FirebaseCollections.stock.name)
              .where("partId", isEqualTo: partModel.partId)
              .get();

      for (var stockDoc in partsStockCollection.docs) {
        StockModel stockModel = StockModel.fromMap(stockDoc.data());
        partModel.purchasePrice = stockModel.purchasePrice;
        partModel.mrp = stockModel.mrp;
      }

      parts.add(partModel);
    }
  }

  Future<void> fetchTags() async {
    QuerySnapshot<Map<String, dynamic>> tagsCollection = await FirebaseFirestore
        .instance
        .collection(FirebaseCollections.tags.name)
        .get();

    for (var tagDoc in tagsCollection.docs) {
      Map<String, dynamic> tagData = tagDoc.data();
      tags.addAll(List<String>.from(tagData["tags"]));
    }
  }

  void addServices({required List<String> servicesNames}) {
    for (String serviceTitle in servicesNames) {
      ServiceModel? serviceModel = services.firstWhereOrNull((el) {
        return el.name?.toLowerCase() == serviceTitle.toLowerCase();
      });
      ServicePartModel servicePartModel = ServicePartModel(
        id: idGenerator.v4(),
        title: serviceTitle,
        // partServiceNumber: serviceModel?.serviceNumber,
        quantity: 1,
        rate: serviceModel?.price ?? 0,
        amount: serviceModel?.price ?? 0,
        discount: 0,
        completed: createRepairOrderModel.orderStatus == OrderStatus.ready,
        gstRate: serviceModel?.gstRate ?? 0,
      );
      selectedServices.add(servicePartModel);

      calculateServicePartAmount(servicePartModel: servicePartModel);
    }

    update();
  }

  void addParts({required List<String> partsNames}) {
    for (String partsTitle in partsNames) {
      PartModel? partModel = parts.firstWhereOrNull((el) {
        return el.name?.toLowerCase() == partsTitle.toLowerCase();
      });

      ServicePartModel servicePartModel = ServicePartModel(
        id: partModel?.partId,
        title: partsTitle,
        partServiceNumber: partModel?.partNumber,
        quantity: 1,
        rate: partModel?.mrp ?? 0,
        purchasePrice: partModel?.purchasePrice ?? 0,
        isOil: partModel?.isOil ?? false,
        amount: partModel?.mrp ?? 0,
        discount: 0,
        completed: createRepairOrderModel.orderStatus == OrderStatus.ready,
        gstRate: partModel?.gstRate ?? 0,
      );

      selectedParts.add(servicePartModel);

      calculateServicePartAmount(servicePartModel: servicePartModel);
    }

    update();

    Future.delayed(const Duration(milliseconds: 100), () => {update()});
  }

  void addTags({required List<String> tags}) {
    selectedTags.addAll(tags);

    update(["tagsContainer"]);
  }

  void removeTag({required int index}) {
    selectedTags.removeAt(index);

    update(["tagsContainer"]);
  }

  void addRemark() {
    customerRemarks.add("");
    update(["customerRemarks"]);
  }

  void updateRemarkAt({required String remark, required int index}) {
    customerRemarks[index] = remark;
  }

  void removeRemark({required int index}) {
    customerRemarks.removeAt(index);
    update(["customerRemarks"]);
  }

  void removeServicePart({required ServicePartModel servicePart}) {
    ServicePartModel? serviceToBeDeleted = selectedServices.firstWhereOrNull(
      (ServicePartModel serviceElement) {
        return serviceElement.id == servicePart.id;
      },
    );

    if (serviceToBeDeleted != null) {
      selectedServices.remove(serviceToBeDeleted);
    } else {
      ServicePartModel? partToBeDeleted = selectedParts.firstWhereOrNull(
        (ServicePartModel partElement) {
          return partElement.id == servicePart.id;
        },
      );

      if (partToBeDeleted != null) {
        selectedParts.remove(partToBeDeleted);
      }
    }

    update(['costContainer']);
    update(['costContainer']);
    update();
  }

  void calculateServicePartAmount(
      {required ServicePartModel servicePartModel,
      double? alreadyAppliedDiscount}) {
    if (servicePartModel.rate != null && servicePartModel.quantity != null) {
      servicePartModel.amount =
          servicePartModel.quantity! * servicePartModel.rate!;
      if (alreadyAppliedDiscount != null && alreadyAppliedDiscount > 0.0) {
        servicePartModel.amount = servicePartModel.amount! -
            (servicePartModel.amount! * alreadyAppliedDiscount / 100);
      } else if (servicePartModel.discount != null &&
          servicePartModel.discount! > 0.0) {
        servicePartModel.amount = servicePartModel.amount! -
            (servicePartModel.amount! * servicePartModel.discount! / 100);
      }
    } else {
      servicePartModel.amount = 0.0;
    }

    servicePartModel.partsGst = (servicePartModel.gstRate! *
            (num.tryParse(servicePartModel.amount.toString()) ?? 0)) /
        100;

    _partsGst = selectedParts.fold<double>(
      0.0,
      (previousValue, element) {
        if (element.amount != null) {
          num amount = num.tryParse(element.amount.toString()) ?? 0;
          // print(
          //     '---------------  ${servicePartModel.partsGst}  -- ${element.gstRate!}');

          return previousValue + (element.gstRate! * amount) / 100;
        }
        return previousValue;
      },
    );
    servicePartModel.servicesGst = (servicePartModel.gstRate! *
            (num.tryParse(servicePartModel.amount.toString()) ?? 0)) /
        100;
    _servicesGst = selectedServices.fold<double>(
      0.0,
      (previousValue, element) {
        if (element.amount != null) {
          num amount = num.tryParse(element.amount.toString()) ?? 0;
          return previousValue + (element.gstRate! * amount) / 100;
        }
        return previousValue;
      },
    );

    _laborTotal = selectedServices.fold<double>(
      0.0,
      (previousValue, element) {
        if (element.amount != null) {
          return previousValue + element.amount!;
        }
        return previousValue;
      },
    );

    _partsTotal = selectedParts.fold<double>(0.0, (previousValue, element) {
      if (element.amount != null) {
        return previousValue + element.amount!;
      }
      return previousValue;
    });

    update([servicePartModel.id ?? "", "costContainer"]);
  }

  void searchCustomer({required String searchTerm}) {
    if (searchTerm.length < 3) {
      return;
    }
    List<CustomerDetailsModel> searchingCustomer = [];
    for (CustomerDetailsModel customer in allCustomers.values) {
      if (customer.username?.toLowerCase().contains(searchTerm.toLowerCase()) ??
          false) {
        searchingCustomer.add(customer);
      } else if (customer.phone
              ?.toLowerCase()
              .contains(searchTerm.toLowerCase()) ??
          false) {
        searchingCustomer.add(customer);
      }
    }

    if (searchingCustomer.isNotEmpty) {
      searchedCustomer = searchingCustomer;
    } else {
      searchedCustomer = null;
    }
    update();
  }

  void confirmOrder(
      {required BuildContext context,
      required bool isInvoice,
      void Function()? onCreate}) {
    RepairDetailsModel repairDetailsModel = RepairDetailsModel();
    // print(
    //     "==-=-=${createRepairOrderModel.repairDetailsModel?.vehicleImagesLinks}");
    repairDetailsModel.servicesTotal = laborTotal;
    repairDetailsModel.partsTotal = partsTotal;
    repairDetailsModel.total = laborTotal + partsTotal;
    repairDetailsModel.discount = discountTotal;
    // repairDetailsModel.repairOrderImagesLinks =
    // createRepairOrderModel.repairDetailsModel?.repairOrderImagesLinks;

    repairDetailsModel.services = selectedServices;
    repairDetailsModel.parts = selectedParts;
    repairDetailsModel.tags = selectedTags;
    repairDetailsModel.customerRemarks =
        customerRemarks.where((remark) => remark.isNotEmpty).toList();
    repairDetailsModel.estimatedDelivery = estimatedDeliveryDateTime.toString();

    createRepairOrderModel.repairDetailsModel = repairDetailsModel;
    if (isInvoice) {
      repairDetailsModel.paymentReceived = repairDetailsModel.total;
      repairDetailsModel.paymentDue = 0;
    }

    moveToConfirmOrder(
        context: context,
        createRepairOrder: createRepairOrderModel,
        isInvoice: isInvoice,
        onCreate: onCreate);
  }

  Future<void> addRepairOrderInDatabase({required BuildContext context}) async {
    try {
      createRepairOrderModel.customerId =
          createRepairOrderModel.customerDetailsModel?.customerId;
      createRepairOrderModel.vehicleId =
          createRepairOrderModel.vehicleDetailsModel?.vehicleId;
      createRepairOrderModel.customerDetailsModel?.lastVisit =
          DateTime.now().toString().split(' ').first;
      await addUpdateCustomerDetailsInDatabase();

      createRepairOrderModel.vehicleDetailsModel?.customerId =
          createRepairOrderModel.customerId;

      await addUpdateVehicleDetailsInDatabase();

      createRepairOrderModel.orderStatus = OrderStatus.created;

      createRepairOrderModel.createdAt = DateTime.now().toIso8601String();

      CollectionReference<Map<String, dynamic>> orderCollection =
          FirebaseFirestore.instance
              .collection(FirebaseCollections.orders.name);

      DocumentReference<Map<String, dynamic>> newOrder = await orderCollection
          .add(createRepairOrderModel.toRepairDetailsMap());

      createRepairOrderModel.orderId = newOrder.id;
      debugPrint(createRepairOrderModel.orderId);

      await uploadRepairDetailsImages(
          repairOrderId: createRepairOrderModel.orderId ?? "");
      print("==--------${FirebaseAuth.instance.currentUser?.uid}");
      if (signature != null) {
        createRepairOrderModel.repairDetailsModel?.customerSignatureLink =
            await uploadSignatureImage(
                signatureImage: SelectedImageModel(
                  name: "customerSignature",
                  uInt8List: signature!,
                ),
                folderPath:
                    '${FirebaseAuth.instance.currentUser?.uid}/${createRepairOrderModel.customerDetailsModel?.username?.replaceAll(' ', '_')}/${createRepairOrderModel.vehicleDetailsModel?.make?.replaceAll(' ', '_')}_${createRepairOrderModel.vehicleDetailsModel?.model?.replaceAll(' ', '_')}/repair_orders/${createRepairOrderModel.orderId}');
      }
      if (repairOrderImages.isNotEmpty) {
        for (var element in repairOrderImages) {
          final data = await uploadSignatureImage(
              signatureImage: SelectedImageModel(
                name: "repairOrderImages",
                uInt8List: element.uInt8List,
              ),
              folderPath:
                  '${FirebaseAuth.instance.currentUser?.uid}/${createRepairOrderModel.customerDetailsModel?.username?.replaceAll(' ', '_')}/${createRepairOrderModel.vehicleDetailsModel?.make?.replaceAll(' ', '_')}_${createRepairOrderModel.vehicleDetailsModel?.model?.replaceAll(' ', '_')}/repair_orders/${createRepairOrderModel.orderId}');
          if (data != null) {
            createRepairOrderModel.repairDetailsModel?.repairOrderImagesLinks
                ?.add(data);
          }
        }
      }
      if (context.mounted) {
        createRepairOrderModel.invoiceId = await addInvoiceInDatabase(
          fromCounterSale: false,
          context: context,
          repairOrderId: createRepairOrderModel.orderId,
          addSignature: false,
        );
      }

      await orderCollection
          .doc(createRepairOrderModel.orderId)
          .set(createRepairOrderModel.toRepairDetailsMap());
    } catch (e) {
      Utils.showSnackBar(title: e.toString());
    }
  }

  Future<String?> addInvoiceInDatabase({
    required BuildContext context,
    String? repairOrderId,
    bool addSignature = true,
    bool fromCounterSale = true,
    void Function()? onCreate,
  }) async {
    try {
      print("addInvoiceInDatabase.....");

      CollectionReference<Map<String, dynamic>> invoicesCollection =
          FirebaseFirestore.instance
              .collection(FirebaseCollections.invoices.name);

      createRepairOrderModel.customerId =
          createRepairOrderModel.customerDetailsModel?.customerId;
      createRepairOrderModel.franchiseId =
          createRepairOrderModel.customerDetailsModel?.franchiseId;
      createRepairOrderModel.createdAt = DateTime.now().toIso8601String();
      createRepairOrderModel.orderId = repairOrderId;

      AggregateQuerySnapshot query = await invoicesCollection.count().get();
      if (createRepairOrderModel.invoiceId == null &&
          createRepairOrderModel.jobCardId == null) {
        if (query.count != null) {
          createRepairOrderModel.jobCardId =
              "SF-${Utils.getRandomId(4, true)}-${query.count != null ? query.count! + 1 : 1}";
        }

        DocumentReference<Map<String, dynamic>> newInvoice =
            await invoicesCollection
                .add(createRepairOrderModel.toRepairDetailsMap());
        createRepairOrderModel.invoiceId = newInvoice.id;
        if (fromCounterSale) {
          await FirebaseFirestore.instance
              .collection(FirebaseCollections.customers.name)
              .doc(createRepairOrderModel.customerDetailsModel?.customerId)
              .update({
            'totalBillAmount': FieldValue.increment(
                createRepairOrderModel.repairDetailsModel?.total ?? 0)
          });
          createRepairOrderModel.orderStatus = OrderStatus.completed;
          createRepairOrderModel.completionDate = Timestamp.now();
        } else {
          createRepairOrderModel.orderStatus = OrderStatus.created;
        }
        print("-------------------------Faisal");
        if (repairOrderId == null) {
          print("Counter Sale");
          double expenseTotal = 0.0;
          for (ServicePartModel element
              in createRepairOrderModel.repairDetailsModel?.parts ?? []) {
            expenseTotal += element.purchasePrice ?? 0;
          }
          // repairOrderDetailsController
          //     .repairOrderModel?.repairDetailsModel?.parts
          //     ?.map((e) =>
          //         expenseTotal += e.purchasePrice ?? 0);
          await FirebaseFirestore.instance
              .collection(FirebaseCollections.expenses.name)
              .add(ExpenseModel(
                      repairOrderId: createRepairOrderModel.orderId,
                      title: "Parts (CS)",
                      comment: '-',
                      paymentDate: DateTime.now().toString(),
                      franchiseId: createRepairOrderModel.franchiseId,
                      // paymentMode: selectedPaymentMode,
                      expenseDate: createRepairOrderModel.createdAt,
                      // expenseMode: ExpenseMode.paid,
                      totalAmount: expenseTotal)
                  .toMap());
          createRepairOrderModel.invoiceId = newInvoice.id;
        }

        if (signature != null && addSignature) {
          createRepairOrderModel
                  .repairDetailsModel?.businessOwnerSignatureLink =
              await uploadSignatureImage(
                  signatureImage: SelectedImageModel(
                      name: "businessOwnerSignature", uInt8List: signature!),
                  folderPath:
                      '${FirebaseAuth.instance.currentUser?.uid}/${createRepairOrderModel.customerDetailsModel?.username?.replaceAll(' ', '_')}/invoices/${createRepairOrderModel.invoiceId}');
        }

        await invoicesCollection
            .doc(createRepairOrderModel.invoiceId)
            .set(createRepairOrderModel.toInvoiceDetailsMap());
        for (ServicePartModel part
            in createRepairOrderModel.repairDetailsModel?.parts ?? []) {
          QuerySnapshot<Map<String, dynamic>> partsStockCollection =
              await FirebaseFirestore.instance
                  .collection(FirebaseCollections.stock.name)
                  .where("partId", isEqualTo: part.id)
                  .get();

          for (var stockDoc in partsStockCollection.docs) {
            StockModel stockModel = StockModel.fromMap(stockDoc.data());
            if (part.quantity != null) {
              stockModel.currentStock =
                  stockModel.currentStock! - part.quantity!.toDouble() ?? 0.0;
            }

            await FirebaseFirestore.instance
                .collection(FirebaseCollections.stock.name)
                .doc(stockModel.stockId)
                .update(stockModel.toMap());
          }
        }
      } else {
        CollectionReference<Map<String, dynamic>> orderCollection =
            FirebaseFirestore.instance
                .collection(FirebaseCollections.orders.name);

        RepairOrderModel repairOrderModel =
            RepairOrderModel.fromInvoiceDetailsMap(
                repairOrderId, createRepairOrderModel.toInvoiceDetailsMap());

        await orderCollection
            .doc(createRepairOrderModel.orderId)
            .update(createRepairOrderModel.toRepairDetailsMap());
        await invoicesCollection
            .doc(createRepairOrderModel.invoiceId)
            .set(createRepairOrderModel.toInvoiceDetailsMap());
      }

      onCreate?.call();

      return createRepairOrderModel.invoiceId;
    } catch (e) {
      Utils.showSnackBar(title: e.toString());
    }
    return null;
  }

  Future<void> addUpdateVehicleDetailsInDatabase() async {
    CollectionReference<Map<String, dynamic>> customerVehiclesCollection =
        FirebaseFirestore.instance
            .collection(FirebaseCollections.customerVehicles.name);

    if (createRepairOrderModel.vehicleId == null) {
      createRepairOrderModel.vehicleDetailsModel?.registrationNumber =
          createRepairOrderModel.vehicleDetailsModel?.registrationNumber!
              .toUpperCase();

      DocumentReference<Map<String, dynamic>> newVehicle =
          await customerVehiclesCollection
              .add(createRepairOrderModel.vehicleDetailsModel!.toMap());

      createRepairOrderModel.vehicleId = newVehicle.id;
      createRepairOrderModel.vehicleDetailsModel?.vehicleId = newVehicle.id;

      await uploadInsuranceAndRegistrationCertificatesImages();

      await customerVehiclesCollection
          .doc(createRepairOrderModel.vehicleId)
          .set(createRepairOrderModel.vehicleDetailsModel!.toMap());
    } else {
      await uploadInsuranceAndRegistrationCertificatesImages();

      await customerVehiclesCollection
          .doc(createRepairOrderModel.vehicleId)
          .set(createRepairOrderModel.vehicleDetailsModel!.toMap());
    }
  }

  Future<void> addUpdateCustomerDetailsInDatabase() async {
    CollectionReference<Map<String, dynamic>> customerCollection =
        FirebaseFirestore.instance
            .collection(FirebaseCollections.customers.name);

    if (createRepairOrderModel.customerId == null) {
      if (createRepairOrderModel.customerDetailsModel?.franchiseId == null ||
          createRepairOrderModel.customerDetailsModel!.franchiseId!.isEmpty) {
        createRepairOrderModel.customerDetailsModel?.franchiseId =
            FirebaseAuth.instance.currentUser?.uid ?? "";
      }

      DocumentReference<Map<String, dynamic>> newCustomer =
          await customerCollection
              .add(createRepairOrderModel.customerDetailsModel!.toMap());
      createRepairOrderModel.customerId = newCustomer.id;
      createRepairOrderModel.customerDetailsModel!.customerId = newCustomer.id;

      await customerCollection
          .doc(createRepairOrderModel.customerId)
          .set(createRepairOrderModel.customerDetailsModel!.toMap());
    } else {
      await customerCollection
          .doc(createRepairOrderModel.customerId)
          .set(createRepairOrderModel.customerDetailsModel!.toMap());
    }
    searchCustomerTextEditingController.clear();
    update();
  }

  Future<void> uploadInsuranceAndRegistrationCertificatesImages() async {
    if (createRepairOrderModel
            .vehicleDetailsModel?.insuranceImages?.isNotEmpty ??
        false) {
      List<String> insuranceImagesLinks = await Get.find<HomeScreenController>()
          .uploadImages(
              folderPath:
                  '${FirebaseAuth.instance.currentUser?.uid}/${createRepairOrderModel.customerDetailsModel?.username?.replaceAll(' ', '_')}/${createRepairOrderModel.vehicleDetailsModel?.make?.replaceAll(' ', '_')}_${createRepairOrderModel.vehicleDetailsModel?.model?.replaceAll(' ', '_')}/insurance_images',
              imagesToUpload:
                  createRepairOrderModel.vehicleDetailsModel?.insuranceImages ??
                      []);

      createRepairOrderModel.vehicleDetailsModel?.insuranceImagesLinks =
          insuranceImagesLinks;
    }

    if (createRepairOrderModel
            .vehicleDetailsModel?.registrationCertificatesImages?.isNotEmpty ??
        false) {
      List<String> registrationCertificatesImagesLinks =
          await Get.find<HomeScreenController>().uploadImages(
              folderPath:
                  '${FirebaseAuth.instance.currentUser?.uid}/${createRepairOrderModel.customerDetailsModel?.username?.replaceAll(' ', '_')}/${createRepairOrderModel.vehicleDetailsModel?.make?.replaceAll(' ', '_')}_${createRepairOrderModel.vehicleDetailsModel?.model?.replaceAll(' ', '_')}/registration_certificatesImages',
              imagesToUpload: createRepairOrderModel
                      .vehicleDetailsModel?.registrationCertificatesImages ??
                  []);

      createRepairOrderModel
              .vehicleDetailsModel?.registrationCertificatesImagesLinks =
          registrationCertificatesImagesLinks;
    }
  }

  Future<void> uploadRepairDetailsImages(
      {required String repairOrderId}) async {
    try {
      if (repairOrderImages.isNotEmpty) {
        List<String> repairOrderImagesLinks =
            await Get.find<HomeScreenController>().uploadImages(
                folderPath:
                    '${FirebaseAuth.instance.currentUser?.uid}/${createRepairOrderModel.customerDetailsModel?.username?.replaceAll(' ', '_')}/${createRepairOrderModel.vehicleDetailsModel?.make?.replaceAll(' ', '_')}_${createRepairOrderModel.vehicleDetailsModel?.model?.replaceAll(' ', '_')}/repair_orders/$repairOrderId',
                imagesToUpload: repairOrderImages);

        createRepairOrderModel.repairDetailsModel?.repairOrderImagesLinks =
            repairOrderImagesLinks;
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<String?> uploadSignatureImage({
    required SelectedImageModel signatureImage,
    required String folderPath,
  }) async {
    try {
      List<String> signatureImageLink = await Get.find<HomeScreenController>()
          .uploadImages(
              folderPath: folderPath, imagesToUpload: [signatureImage]);

      return signatureImageLink.first;
    } catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }

  Future<String?> uploadRepairOrderImages({
    required SelectedImageModel signatureImage,
    required String folderPath,
  }) async {
    try {
      List<String> repairOrderImageLink = await Get.find<HomeScreenController>()
          .uploadImages(
              folderPath: folderPath, imagesToUpload: [signatureImage]);

      return repairOrderImageLink.first;
    } catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }
}
