import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/service_part.model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/vehicle_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class RepairOrderDetailsController extends GetxController {
  RepairOrderModel? repairOrderModel;
  RepairOrderModel? invoice;

  bool isLoading = false;
  bool isEditingServices = false;
  bool isEditingParts = false;

  void toggleEditingServices() {
    isEditingServices = !isEditingServices;
    update(["Progress List"]);
  }

  void toggleEditingParts() {
    isEditingParts = !isEditingParts;
    update(["Progress List"]);
  }

  void toggleServicePartProgress(
      {required String servicePartId, required bool completed}) {
    repairOrderModel?.repairDetailsModel?.services?.firstWhereOrNull((service) {
      return service.id == servicePartId;
    })?.completed = completed;

    repairOrderModel?.repairDetailsModel?.parts?.firstWhereOrNull((part) {
      return part.id == servicePartId;
    })?.completed = completed;
    update(["Progress List"]);
  }

  Future<void> fetchRepairOrderDetails({required String orderId}) async {
    try {
      isLoading = true;
      update();
      DocumentSnapshot<Map<String, dynamic>> orderDocument =
          await FirebaseFirestore.instance
              .collection(FirebaseCollections.orders.name)
              .doc(orderId)
              .get();

      Map<String, dynamic>? orderMap = orderDocument.data();
      if (orderMap != null) {
        repairOrderModel = RepairOrderModel.fromMap(orderDocument.id, orderMap);

        DocumentSnapshot<Map<String, dynamic>> customerDocument =
            await FirebaseFirestore.instance
                .collection(FirebaseCollections.customers.name)
                .doc(repairOrderModel?.customerId ?? "")
                .get();

        Map<String, dynamic>? customerMap = customerDocument.data();
        if (customerMap != null) {
          repairOrderModel?.customerDetailsModel =
              CustomerDetailsModel.fromMap(customerMap);
        }

        DocumentSnapshot<Map<String, dynamic>> vehicleDocument =
            await FirebaseFirestore.instance
                .collection(FirebaseCollections.customerVehicles.name)
                .doc(repairOrderModel?.vehicleId ?? "")
                .get();

        Map<String, dynamic>? vehicleMap = vehicleDocument.data();

        if (vehicleMap != null) {
          repairOrderModel?.vehicleDetailsModel =
              VehicleDetailsModel.fromMap(vehicleMap);
        }
        // print(repairOrderModel?.invoiceId);
        if (repairOrderModel?.invoiceId != null) {
          DocumentSnapshot<Map<String, dynamic>> invoiceDocument =
              await FirebaseFirestore.instance
                  .collection(FirebaseCollections.invoices.name)
                  .doc(repairOrderModel?.invoiceId ?? "")
                  .get();

          Map<String, dynamic>? invoiceMap = invoiceDocument.data();

          if (invoiceMap != null) {
            invoice = RepairOrderModel.fromInvoiceDetailsMap(
                invoiceDocument.id, invoiceMap);
          }
        }
      }
      repairOrderModel?.gstIncluded = invoice?.gstIncluded;
      repairOrderModel?.isigst = invoice?.isigst;
      isLoading = false;
      update();
    } catch (e) {
      debugPrint(e.toString());
      // update();
    }
  }

  Future<void> updateRepairOrder() async {
    isLoading = true;
    update();

    DocumentReference<Map<String, dynamic>> orderDocument = FirebaseFirestore
        .instance
        .collection(FirebaseCollections.orders.name)
        .doc(repairOrderModel?.orderId);

    await orderDocument.set(repairOrderModel!.toMap());
    // await orderDocument.set(repairOrderModel!.toMap());

    isLoading = false;
    update();
  }

  Future<void> updateOrderStatus(
      {required OrderStatus updateStatusTo, PaymentMode? paymentMode}) async {
    isLoading = true;
    update();

    DocumentReference<Map<String, dynamic>> orderDocument = FirebaseFirestore
        .instance
        .collection(FirebaseCollections.orders.name)
        .doc(repairOrderModel?.orderId);

    DocumentReference<Map<String, dynamic>> invoiceDocument = FirebaseFirestore
        .instance
        .collection(FirebaseCollections.invoices.name)
        .doc(repairOrderModel?.invoiceId);

    repairOrderModel?.orderStatus = updateStatusTo;
    repairOrderModel?.paymentMode = paymentMode;

    if (updateStatusTo == OrderStatus.completed) {
      await FirebaseFirestore.instance
          .collection(FirebaseCollections.customers.name)
          .doc(repairOrderModel?.customerDetailsModel?.customerId)
          .update({
        'totalBillAmount': FieldValue.increment(
            repairOrderModel?.repairDetailsModel?.total ?? 0)
      });
      repairOrderModel?.completionDate = Timestamp.now();
    }
    print("orderStatus");
    // if (updateStatusTo == OrderStatus.completed) {
    //   print(repairOrderModel?.repairDetailsModel?.paymentDue);
    //   repairOrderModel?.repairDetailsModel?.paymentReceived =
    //       repairOrderModel?.repairDetailsModel?.paymentDue;
    //   print("from repair order detail----------------");

    //   repairOrderModel?.repairDetailsModel?.paymentDue = 0.0;
    //   repairOrderModel?.paymentMode = paymentMode;
    // }

    await orderDocument.set(repairOrderModel!.toMap());
    await invoiceDocument.set(repairOrderModel!.toInvoiceDetailsMap());
    print(repairOrderModel?.repairDetailsModel?.toMap());
    print(repairOrderModel?.repairDetailsModel?.toInvoiceMap());

    isLoading = false;
    update();
  }

  bool validateProgressList() {
    bool isServiceFinished = true;
    bool isPartsFinished = true;

    ServicePartModel? unfinishedService;
    ServicePartModel? unfinishedParts;
    if (repairOrderModel?.repairDetailsModel?.services?.isNotEmpty ?? false) {
      unfinishedService = repairOrderModel?.repairDetailsModel?.services
          ?.firstWhereOrNull((service) {
        return service.completed == false;
      });
    }

    if (repairOrderModel?.repairDetailsModel?.parts?.isNotEmpty ?? false) {
      unfinishedParts =
          repairOrderModel?.repairDetailsModel?.parts?.firstWhereOrNull((part) {
        return part.completed == false;
      });
    }

    if (repairOrderModel?.repairDetailsModel?.parts?.isNotEmpty ?? false) {
      if (unfinishedParts != null) {
        isPartsFinished = false;
      }
    }

    if (repairOrderModel?.repairDetailsModel?.services?.isNotEmpty ?? false) {
      if (unfinishedService != null) {
        isServiceFinished = false;
      }
    }

    return isPartsFinished && isServiceFinished;
  }
}
