// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:flutter/material.dart';
// import 'package:speed_force_franchise/enums/enums.dart';
// import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
// import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/personal_details_model.dart';
// import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/vehicle_details_model.dart';
// import 'package:speed_force_franchise/utils/common_exports.dart';

// class ViewInvoicesController extends GetxController {
//   bool isLoading = false;
//   bool isFiltered = false;

//   List<RepairOrderModel> repairOrderModels = [];

//   List<RepairOrderModel> searchedRepairInvoiceModels = [];

//   TextEditingController searchOrderTextEditingController =
//       TextEditingController();

//   Future<void> fetchInvoices({
//     String? orderStatus,
//     String? vehicleId,
//   }) async {
//     repairOrderModels.clear();
//     isLoading = true;
//     update();

//     QuerySnapshot<Map<String, dynamic>>? invoices;
//     invoices = await FirebaseFirestore.instance
//         .collection(FirebaseCollections.invoices.name)
//         .where("franchiseId", isEqualTo: FirebaseAuth.instance.currentUser?.uid)
//         .where("orderStatus", isEqualTo: OrderStatus.completed.name)
//         .get();
//     if (invoices != null) {
//       for (var invoice in invoices.docs) {
//         Map<String, dynamic> invoiceMap = invoice.data();

//         RepairOrderModel repairOrderModel =
//             RepairOrderModel.fromInvoiceDetailsMap(invoice.id, invoiceMap);

//         DocumentSnapshot<Map<String, dynamic>> customerDetailsSnapShot =
//             await FirebaseFirestore.instance
//                 .collection(FirebaseCollections.customers.name)
//                 .doc(repairOrderModel.customerId)
//                 .get();

//         Map<String, dynamic>? customerDetailsMap =
//             customerDetailsSnapShot.data();
//         if (customerDetailsMap != null) {
//           repairOrderModel.customerDetailsModel =
//               CustomerDetailsModel.fromMap(customerDetailsMap);
//         }

//         DocumentSnapshot<Map<String, dynamic>> vehicleDetailsSnapShot =
//             await FirebaseFirestore.instance
//                 .collection(FirebaseCollections.customerVehicles.name)
//                 .doc(repairOrderModel.vehicleId)
//                 .get();

//         Map<String, dynamic>? vehicleDetailsMap = vehicleDetailsSnapShot.data();
//         if (vehicleDetailsMap != null) {
//           repairOrderModel.vehicleDetailsModel =
//               VehicleDetailsModel.fromMap(vehicleDetailsMap);
//         }

//         // DocumentSnapshot<Map<String, dynamic>> invoicesSnapShot =
//         //     await FirebaseFirestore.instance
//         //         .collection(FirebaseCollections.invoices.name)
//         //         .doc(repairOrderModel.invoiceId)
//         //         .get();

//         // if (invoicesSnapShot.data() != null) {
//         //   RepairOrderModel repairOrderInvoice =
//         //       RepairOrderModel.fromInvoiceDetailsMap(invoicesSnapShot.data()!);
//         //   repairOrderModel.jobCardId = repairOrderInvoice.jobCardId;
//         // }

//         repairOrderModels.add(repairOrderModel);
//       }
//     }

//     isLoading = false;
//     update();
//   }

//   void searchInvoices({required String searchTerm}) {
//     if (searchTerm.isEmpty) {
//       searchedRepairInvoiceModels.clear();
//       update();
//       return;
//     }
//     List<RepairOrderModel> searchingInvoices = [];
//     for (RepairOrderModel invoice in repairOrderModels) {
//       if (invoice.customerDetailsModel?.username
//               ?.toLowerCase()
//               .contains(searchTerm.toLowerCase()) ??
//           false) {
//         searchingInvoices.add(invoice);
//       } else if (invoice.customerDetailsModel?.phone
//               ?.toLowerCase()
//               .contains(searchTerm.toLowerCase()) ??
//           false) {
//         searchingInvoices.add(invoice);
//       } else if (invoice.vehicleDetailsModel?.registrationNumber
//               ?.toLowerCase()
//               .contains(searchTerm.toLowerCase()) ??
//           false) {
//         searchingInvoices.add(invoice);
//       }
//     }

//     if (searchingInvoices.isNotEmpty) {
//       searchedRepairInvoiceModels = searchingInvoices;
//     } else {
//       searchedRepairInvoiceModels.clear();
//     }

//     update();
//   }

//   void filterInvoiceByDates(
//       {required DateTime startDate, required DateTime endDate}) {
//     isFiltered = true;
//     List<RepairOrderModel> filteredInvvoices = [];
//     for (RepairOrderModel invoice in repairOrderModels) {
//       DateTime createdAt = DateTime.parse(invoice.createdAt ?? "");
//       if (createdAt.isAfter(startDate.subtract(const Duration(days: 1))) &&
//           createdAt.isBefore(endDate.add(const Duration(days: 1)))) {
//         filteredInvvoices.add(invoice);
//       }
//     }

//     if (filteredInvvoices.isNotEmpty) {
//       searchedRepairInvoiceModels = filteredInvvoices;
//     }

//     update();
//   }

//   void clearFilter() {
//     isFiltered = false;
//     searchedRepairInvoiceModels.clear();
//     update();
//   }
// }
