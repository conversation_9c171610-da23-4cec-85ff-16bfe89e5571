import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/vehicle_details_model.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class ViewOrdersController extends GetxController {
  bool isLoading = false;
  bool isFiltered = false;

  List<RepairOrderModel> repairOrderModels = [];

  List<RepairOrderModel> searchedRepairOrderModels = [];

  TextEditingController searchOrderTextEditingController =
      TextEditingController();

  Future<void> fetchOrders({
    required ViewOrdersBy viewOrdersBy,
    String? orderStatus,
    String? vehicleId,
  }) async {
    repairOrderModels.clear();
    isLoading = true;
    update();

    QuerySnapshot<Map<String, dynamic>>? orders;

    if (viewOrdersBy == ViewOrdersBy.orderStatus) {
      orders = await FirebaseFirestore.instance
          .collection(FirebaseCollections.orders.name)
          .where("franchiseId",
              isEqualTo: FirebaseAuth.instance.currentUser?.uid)
          .where("orderStatus", isEqualTo: orderStatus)
          .orderBy('createdAt', descending: true)
          .get();
    } else if (viewOrdersBy == ViewOrdersBy.vehicleId) {
      orders = await FirebaseFirestore.instance
          .collection(FirebaseCollections.orders.name)
          .where("franchiseId",
              isEqualTo: FirebaseAuth.instance.currentUser?.uid)
          .where("vehicleId", isEqualTo: vehicleId)
          .orderBy('createdAt', descending: true)
          .get();
    }

    if (orders != null) {
      for (var order in orders.docs) {
        Map<String, dynamic> orderMap = order.data();
        RepairOrderModel repairOrderModel =
            RepairOrderModel.fromMap(order.id, orderMap);

        DocumentSnapshot<Map<String, dynamic>> customerDetailsSnapShot =
            await FirebaseFirestore.instance
                .collection(FirebaseCollections.customers.name)
                .doc(repairOrderModel.customerId)
                .get();

        Map<String, dynamic>? customerDetailsMap =
            customerDetailsSnapShot.data();
        if (customerDetailsMap != null) {
          repairOrderModel.customerDetailsModel =
              CustomerDetailsModel.fromMap(customerDetailsMap);
        }

        DocumentSnapshot<Map<String, dynamic>> vehicleDetailsSnapShot =
            await FirebaseFirestore.instance
                .collection(FirebaseCollections.customerVehicles.name)
                .doc(repairOrderModel.vehicleId)
                .get();

        Map<String, dynamic>? vehicleDetailsMap = vehicleDetailsSnapShot.data();
        if (vehicleDetailsMap != null) {
          repairOrderModel.vehicleDetailsModel =
              VehicleDetailsModel.fromMap(vehicleDetailsMap);
        }

        // DocumentSnapshot<Map<String, dynamic>> invoicesSnapShot =
        //     await FirebaseFirestore.instance
        //         .collection(FirebaseCollections.invoices.name)
        //         .doc(repairOrderModel.invoiceId)
        //         .get();

        // if (invoicesSnapShot.data() != null) {
        //   RepairOrderModel repairOrderInvoice =
        //       RepairOrderModel.fromInvoiceDetailsMap(invoicesSnapShot.data()!);
        //   repairOrderModel.jobCardId = repairOrderInvoice.jobCardId;
        // }

        repairOrderModels.add(repairOrderModel);
      }
    }
    if (orderStatus == OrderStatus.completed.name)
      Get.put(ViewOrdersController()).filterOrdersByDates(
          startDate: DateTime(
              DateTime.now().year, DateTime.now().month, DateTime.now().day),
          endDate: DateTime(
              DateTime.now().year, DateTime.now().month, DateTime.now().day));
    isLoading = false;
    update();
  }

  void searchOrders({required String searchTerm}) {
    if (searchTerm.isEmpty) {
      searchedRepairOrderModels.clear();
      update();
      return;
    }
    List<RepairOrderModel> searchingOrders = [];
    for (RepairOrderModel order in repairOrderModels) {
      if (order.customerDetailsModel?.username
              ?.toLowerCase()
              .contains(searchTerm.toLowerCase()) ??
          false) {
        searchingOrders.add(order);
      } else if (order.customerDetailsModel?.phone
              ?.toLowerCase()
              .contains(searchTerm.toLowerCase()) ??
          false) {
        searchingOrders.add(order);
      } else if (order.vehicleDetailsModel?.registrationNumber
              ?.toLowerCase()
              .contains(searchTerm.toLowerCase()) ??
          false) {
        searchingOrders.add(order);
      }
    }

    if (searchingOrders.isNotEmpty) {
      searchedRepairOrderModels = searchingOrders;
    } else {
      searchedRepairOrderModels.clear();
    }

    update();
  }

  void filterOrdersByDates(
      {required DateTime startDate, required DateTime endDate}) {
    isFiltered = true;
    List<RepairOrderModel> filteredOrders = [];
    print(startDate);
    print(endDate);
    for (RepairOrderModel order in repairOrderModels) {
      DateTime createdAt = DateTime.parse(order.createdAt ?? "");
      if (createdAt.isAfter(startDate.subtract(const Duration(days: 1))) &&
          createdAt.isBefore(endDate.add(const Duration(days: 1)))) {
        filteredOrders.add(order);
      }
    }

    if (filteredOrders.isNotEmpty) {
      searchedRepairOrderModels = filteredOrders;
    }

    update();
  }

  void clearFilter() {
    isFiltered = false;
    searchedRepairOrderModels.clear();
    update();
  }
}
