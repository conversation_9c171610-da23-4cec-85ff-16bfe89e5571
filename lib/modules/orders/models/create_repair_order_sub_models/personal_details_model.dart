import 'dart:convert';

class CustomerDetailsModel {
  String? customerId;
  String? franchiseId;
  String? username;
  String? phone;
  String? email;
  String? address;
  String? gstin;
  String? lastVisit;
  num? totalBillAmount;

  CustomerDetailsModel({
    this.customerId,
    this.franchiseId,
    this.username,
    this.phone,
    this.email,
    this.address,
    this.gstin,
    this.lastVisit,
    this.totalBillAmount,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (customerId != null) {
      result.addAll({'customerId': customerId});
    }
    if (franchiseId != null) {
      result.addAll({'franchiseId': franchiseId});
    }
    if (username != null) {
      result.addAll({'username': username});
    }
    if (phone != null) {
      result.addAll({'phone': phone});
    }
    if (email != null) {
      result.addAll({'email': email});
    }
    if (address != null) {
      result.addAll({'address': address});
    }
    if (gstin != null) {
      result.addAll({'gstin': gstin});
    }
    if (lastVisit != null) {
      result.addAll({'lastVisit': lastVisit});
    }
    if (totalBillAmount != null) {
      result.addAll({'totalBillAmount': totalBillAmount});
    }
    return result;
  }

  factory CustomerDetailsModel.fromMap(Map<String, dynamic> map) {
    return CustomerDetailsModel(
      customerId: map['customerId'],
      franchiseId: map['franchiseId'],
      username: map['username'],
      phone: map['phone'],
      email: map['email'],
      address: map['address'],
      gstin: map['gstin'],
      lastVisit: map['lastVisit'],
      totalBillAmount: map['totalBillAmount'],
    );
  }

  String toJson() => json.encode(toMap());

  factory CustomerDetailsModel.fromJson(String source) =>
      CustomerDetailsModel.fromMap(json.decode(source));
}
