import 'dart:convert';

import 'package:speed_force_franchise/models/selected_images_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/insurance_provider_model.dart';

class VehicleDetailsModel {
  String? vehicleId;
  String? customerId;
  String? franchiseId;
  String? make;
  String? model;
  String? registrationNumber;
  String? purchaseDate;
  String? engineNumber;
  String? chasisNumber;
  String? insurerGSTIN;
  String? insurerAddress;
  String? policyNumber;
  String? insuranceExpiryDate;
  InsuranceProviderModel? insuranceProviderModel;
  List<SelectedImageModel>? registrationCertificatesImages;
  List<SelectedImageModel>? insuranceImages;

  List<String>? registrationCertificatesImagesLinks;
  List<String>? insuranceImagesLinks;

  VehicleDetailsModel({
    this.vehicleId,
    this.customerId,
    this.franchiseId,
    this.make,
    this.model,
    this.registrationNumber,
    this.purchaseDate,
    this.engineNumber,
    this.chasisNumber,
    this.insurerGSTIN,
    this.insurerAddress,
    this.policyNumber,
    this.insuranceExpiryDate,
    this.insuranceProviderModel,
    this.registrationCertificatesImages,
    this.insuranceImages,
    this.registrationCertificatesImagesLinks,
    this.insuranceImagesLinks,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (vehicleId != null) {
      result.addAll({'vehicleId': vehicleId});
    }
    if (customerId != null) {
      result.addAll({'customerId': customerId});
    }
    if (franchiseId != null) {
      result.addAll({'franchiseId': franchiseId});
    }
    if (make != null) {
      result.addAll({'make': make});
    }
    if (model != null) {
      result.addAll({'model': model});
    }
    if (registrationNumber != null) {
      result.addAll({'registrationNumber': registrationNumber});
    }
    if (purchaseDate != null) {
      result.addAll({'purchaseDate': purchaseDate});
    }
    if (engineNumber != null) {
      result.addAll({'engineNumber': engineNumber});
    }
    if (chasisNumber != null) {
      result.addAll({'chasisNumber': chasisNumber});
    }
    if (insurerGSTIN != null) {
      result.addAll({'insurerGSTIN': insurerGSTIN});
    }
    if (insurerAddress != null) {
      result.addAll({'insurerAddress': insurerAddress});
    }
    if (policyNumber != null) {
      result.addAll({'policyNumber': policyNumber});
    }
    if (insuranceExpiryDate != null) {
      result.addAll({'insuranceExpiryDate': insuranceExpiryDate});
    }
    if (insuranceProviderModel != null) {
      result
          .addAll({'insuranceProviderModel': insuranceProviderModel!.toMap()});
    }
    if (insuranceImagesLinks != null) {
      result.addAll({'insuranceImagesLinks': insuranceImagesLinks});
    }
    if (registrationCertificatesImagesLinks != null) {
      result.addAll({
        'registrationCertificatesImagesLinks':
            registrationCertificatesImagesLinks
      });
    }

    return result;
  }

  factory VehicleDetailsModel.fromMap(Map<String, dynamic> map) {
    return VehicleDetailsModel(
      vehicleId: map['vehicleId'],
      customerId: map['customerId'],
      franchiseId: map['franchiseId'],
      make: map['make'],
      model: map['model'],
      registrationNumber: map['registrationNumber'],
      purchaseDate: map['purchaseDate'],
      engineNumber: map['engineNumber'],
      chasisNumber: map['chasisNumber'],
      insurerGSTIN: map['insurerGSTIN'],
      insurerAddress: map['insurerAddress'],
      policyNumber: map['policyNumber'],
      insuranceExpiryDate: map['insuranceExpiryDate'],
      insuranceProviderModel: map['insuranceProviderModel'] != null
          ? InsuranceProviderModel.fromMap(map['insuranceProviderModel'])
          : null,
      insuranceImagesLinks: map['insuranceImagesLinks'] != null
          ? List<String>.from(map['insuranceImagesLinks'])
          : [],
      registrationCertificatesImagesLinks:
          map['registrationCertificatesImagesLinks'] != null
              ? List<String>.from(map['registrationCertificatesImagesLinks'])
              : [],
    );
  }

  String toJson() => json.encode(toMap());

  factory VehicleDetailsModel.fromJson(String source) =>
      VehicleDetailsModel.fromMap(json.decode(source));
}
