import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class DetailsCardModel {
  String hintText;
  Widget prefixIcon;
  Widget? suffixIcon;
  double? prefixIconSize;
  double? suffixIconSize;
  bool? readOnly;
  VoidCallback? onTap;
  TextEditingController textEditingController;
  TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;

  DetailsCardModel({
    required this.hintText,
    required this.prefixIcon,
    required this.textEditingController,
    this.suffixIcon,
    this.prefixIconSize,
    this.suffixIconSize,
    this.readOnly,
    this.onTap,
    this.keyboardType,
    this.inputFormatters,
  });
}
