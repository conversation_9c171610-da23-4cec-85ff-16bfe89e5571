import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/orders/controllers/create_repair_order_controller.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/insurance_provider_model.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class AddInsuranceProvider extends StatefulWidget {
  const AddInsuranceProvider({super.key});

  @override
  State<AddInsuranceProvider> createState() => _AddInsuranceProviderState();
}

class _AddInsuranceProviderState extends State<AddInsuranceProvider> {
  TextEditingController companyNameTextEditingController =
      TextEditingController();
  TextEditingController addressTextEditingController = TextEditingController();
  TextEditingController gstinTextEditingController = TextEditingController();

  List<InsuranceProviderModel> insuranceProviders = [
    InsuranceProviderModel(companyName: "MARUTI INSURANCE BROKING PVT LTD"),
    InsuranceProviderModel(companyName: "IFFCO TOKIO GENERAL INSURANCE CO LTD"),
    InsuranceProviderModel(companyName: "INDIA ASSURANCE CO.LTD."),
    InsuranceProviderModel(companyName: "MOTOR SECURE-PRIVATE CAR INSURANCE"),
  ];

  @override
  Widget build(BuildContext context) {
    CreateRepairOrderController createRepairOrderController =
        Get.find<CreateRepairOrderController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text("Insurance Provider"),
        elevation: 0.5,
        backgroundColor: colorsConstants.whiteColor,
        shadowColor: colorsConstants.whiteColor,
        surfaceTintColor: colorsConstants.whiteColor,
      ),
      body: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          children: [
            HeightBox(20.h),
            CustomTextField(
              hintText: "Insurance Company",
              contentPadding: EdgeInsets.zero,
              controller: companyNameTextEditingController,
              filled: true,
              fillColor: colorsConstants.whiteColor,
              suffix: IconButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        content: SizedBox(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: insuranceProviders
                                .map(
                                  (insuranceProvider) => Container(
                                    margin: EdgeInsets.symmetric(vertical: 5.h),
                                    child: InkWell(
                                      onTap: () {
                                        companyNameTextEditingController.text =
                                            insuranceProvider.companyName ?? "";
                                        Navigator.pop(context);
                                      },
                                      child: Row(
                                        children: [
                                          Container(
                                            padding: EdgeInsets.all(8.sp),
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              border: Border.all(),
                                            ),
                                          ),
                                          WidthBox(10.w),
                                          SizedBox(
                                            width: 200.w,
                                            child: Text(
                                              insuranceProvider.companyName ??
                                                  "",
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                )
                                .toList(),
                          ),
                        ),
                      );
                    },
                  );
                },
                icon: const Icon(CupertinoIcons.chevron_down),
              ),
              border: const OutlineInputBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(5),
                ),
                borderSide: BorderSide.none,
              ),
              enabledBorder: const OutlineInputBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(5),
                ),
                borderSide: BorderSide.none,
              ),
              focusedBorder: const OutlineInputBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(5),
                ),
                borderSide: BorderSide.none,
              ),
            ),
            HeightBox(20.h),
            CustomTextField(
              hintText: "Address",
              contentPadding: EdgeInsets.zero,
              controller: addressTextEditingController,
              filled: true,
              fillColor: colorsConstants.whiteColor,
              border: const OutlineInputBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(5),
                ),
                borderSide: BorderSide.none,
              ),
              enabledBorder: const OutlineInputBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(5),
                ),
                borderSide: BorderSide.none,
              ),
              focusedBorder: const OutlineInputBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(5),
                ),
                borderSide: BorderSide.none,
              ),
            ),
            HeightBox(20.h),
            CustomTextField(
              hintText: "GSTIN",
              contentPadding: EdgeInsets.zero,
              controller: gstinTextEditingController,
              filled: true,
              fillColor: colorsConstants.whiteColor,
              border: const OutlineInputBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(5),
                ),
                borderSide: BorderSide.none,
              ),
              enabledBorder: const OutlineInputBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(5),
                ),
                borderSide: BorderSide.none,
              ),
              focusedBorder: const OutlineInputBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(5),
                ),
                borderSide: BorderSide.none,
              ),
            ),
            HeightBox(50.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorsConstants.primaryRed,
                    foregroundColor: colorsConstants.whiteColor,
                    shape: ContinuousRectangleBorder(
                      borderRadius: BorderRadius.circular(5.r),
                    ),
                  ),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text("Cancel"),
                    ],
                  ),
                ),
                WidthBox(10.w),
                ElevatedButton(
                  onPressed: () {
                    createRepairOrderController
                            .newCreatedInsuranceProviderModel =
                        InsuranceProviderModel(
                            companyName: companyNameTextEditingController.text,
                            address: addressTextEditingController.text,
                            gstin: gstinTextEditingController.text);
                    createRepairOrderController
                        .insuranceProviderEditingController
                        .text = companyNameTextEditingController.text;

                    createRepairOrderController.customersInsuranceProviders.add(
                        createRepairOrderController
                            .newCreatedInsuranceProviderModel!);

                    Navigator.pop(context);
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color.fromARGB(255, 40, 146, 44),
                    foregroundColor: colorsConstants.whiteColor,
                    shape: ContinuousRectangleBorder(
                      borderRadius: BorderRadius.circular(5.r),
                    ),
                  ),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text("Save"),
                    ],
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
