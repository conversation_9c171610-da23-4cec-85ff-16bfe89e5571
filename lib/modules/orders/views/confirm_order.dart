import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:pdf/pdf.dart';
import 'package:printing/printing.dart';
import 'package:signature/signature.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/orders/controllers/repair_details_controller.dart';
import 'package:speed_force_franchise/modules/orders/controllers/repair_order_details_controller.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/modules/orders/views/repair_order_details.dart';
import 'package:speed_force_franchise/modules/orders/widgets/order_info_container.dart';
import 'package:speed_force_franchise/routing_manager/extensions.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';
import 'package:pdf/widgets.dart' as pw;

class ConfirmOrder extends StatefulWidget {
  const ConfirmOrder({
    super.key,
    required this.createRepairOrderModel,
    this.isInvoice = false,
    this.onCreate,
  });

  final RepairOrderModel createRepairOrderModel;

  final bool isInvoice;
  final void Function()? onCreate;

  @override
  State<ConfirmOrder> createState() => _ConfirmOrderState();
}

class _ConfirmOrderState extends State<ConfirmOrder> {
  final SignatureController _signatureController = SignatureController(
    penStrokeWidth: 2,
    penColor: Colors.red,
    exportBackgroundColor: Colors.transparent,
  );

  HomeScreenController? homeScreenController;

  final GlobalKey _globalKey = GlobalKey();
  final orderServiceInfoHeaders = ['SERVICES', 'RATE', 'AMOUNT'];
  final orderServiceInfoHeadersGst = [
    'SERVICES',
    'RATE',
    'TAX RATE',
    'TAX AMOUNT',
    'AMOUNT'
  ];
  final orderPartsInfoHeaders = ['PARTS', 'RATE', 'AMOUNT'];
  final orderPartsInfoHeadersGST = [
    'PARTS',
    'RATE',
    'TAX RATE',
    'TAX AMOUNT',
    'AMOUNT'
  ];
  // final cutomerInfoHeaders = ['Customer', 'Vehicle', 'Invoice'];
  List<List<String>> orderserviceinfoList = [];
  List<List<dynamic>> orderPartsinfoList = [];
  num servicesGST = 0;
  num partssGST = 0;
  double subtotal = 0;

  @override
  void initState() {
    super.initState();

    Get.put(RepairOrderDetailsController()).repairOrderModel =
        Get.find<RepairDetailsController>().createRepairOrderModel;
    RepairOrderDetailsController repairOrderDetailsController =
        Get.find<RepairOrderDetailsController>();
    homeScreenController = Get.find<HomeScreenController>();
    // print(widget.createRepairOrderModel.repairDetailsModel
    //         ?.repairOrderImagesLinks ??
    //     ['ewww']);
    orderserviceinfoList = [
      ...List.generate(
        widget.createRepairOrderModel.repairDetailsModel!.services!.length,
        (index) {
          final serviceModel = widget
              .createRepairOrderModel.repairDetailsModel!.services![index];
          subtotal = (serviceModel.rate!) + subtotal;
          (widget.createRepairOrderModel.gstIncluded ?? false)
              ? servicesGST += serviceModel.servicesGst ?? 0
              : 0;

          return (widget.createRepairOrderModel.gstIncluded ?? false)
              ? [
                  serviceModel.title!,
                  serviceModel.rate.toString(),
                  serviceModel.gstRate.toString(),
                  serviceModel.servicesGst.toString(),
                  ((serviceModel.amount ?? 0) +
                          (serviceModel.servicesGst?.toDouble() ?? 0))
                      .toString(),
                ]
              : [
                  serviceModel.title!,
                  serviceModel.rate.toString(),
                  serviceModel.amount.toString(),
                ];
        },
      )
    ];
    orderPartsinfoList = [
      ...List.generate(
        repairOrderDetailsController
            .repairOrderModel!.repairDetailsModel!.parts!.length,
        (index) {
          final partModel =
              widget.createRepairOrderModel.repairDetailsModel!.parts![index];
          subtotal = (partModel.rate!) + subtotal;
          (widget.createRepairOrderModel.gstIncluded ?? false)
              ? partssGST += partModel.partsGst ?? 0
              : 0;
          print(partModel.toMap());
          return (widget.createRepairOrderModel.gstIncluded ?? false)
              ? [
                  [
                    partModel.title!,
                    partModel.quantity!.toString(),
                  ],
                  partModel.rate.toString(),
                  partModel.gstRate.toString(),
                  partModel.partsGst.toString(),
                  ((partModel.amount ?? 0) +
                          (partModel.partsGst?.toDouble() ?? 0))
                      .toString(),
                ]
              : [
                  [
                    partModel.title!,
                    partModel.quantity!.toString(),
                  ],
                  partModel.rate.toString(),
                  partModel.amount.toString()
                ];
        },
      )
    ];
  }

  @override
  void dispose() {
    _signatureController.dispose();
    super.dispose();
  }

  Future<void> _captureAndSharePdf() async {
    pw.Font font = await Constants.invoiceFonts;
    final txtStyle = pw.TextStyle(font: font);
    // RenderRepaintBoundary boundary =
    //     _globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
    // ui.Image image = await boundary.toImage(pixelRatio: 3.0);
    // ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    // final buffer = byteData!.buffer.asUint8List();
    pw.Widget columnData({
      required List<OrderInfoModel> orderInfoModels,
      required String? totalTitle,
      required String? totalAmount,
      required bool? showBorderInEveryColumnData,
      String? cgst,
      String? sgst,
      String? igst,
      List<bool>? gstIncluded,
    }) =>
        pw.Column(children: [
          pw.Container(
            color: PdfColor.fromHex("#AEE4FF"),
            padding: pw.EdgeInsets.symmetric(horizontal: 2.w),
            child: pw.Row(
              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: orderInfoModels
                  .mapIndexed((int index, OrderInfoModel orderInfoModel) {
                return pw.Expanded(
                  child: pw.Text(
                    orderInfoModel.header,
                    // overflow: pw.TextOverflow.ellipsis,
                    textAlign: index == 0
                        ? pw.TextAlign.start
                        : index == orderInfoModels.length - 1
                            ? pw.TextAlign.end
                            : pw.TextAlign.center,
                    // style: txtStyle,
                  ),
                );
              }).toList(),
            ),
          ),
          pw.SizedBox(height: 5.h),
          pw.Padding(
            padding: pw.EdgeInsets.symmetric(horizontal: 5.w),
            child: pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: orderInfoModels.mapIndexed((index, orderInfoModel) {
                return pw.Expanded(
                  child: pw.Column(
                    crossAxisAlignment: index == 0
                        ? pw.CrossAxisAlignment.start
                        : index == orderInfoModels.length - 1
                            ? pw.CrossAxisAlignment.end
                            : pw.CrossAxisAlignment.center,
                    children: [
                      ...orderInfoModel.columnData.map((element) {
                        return pw.Column(
                          children: [
                            pw.Text(
                              element,
                              style: txtStyle.copyWith(
                                  color: PdfColor.fromHex("#343A40")),
                              maxLines: 4,
                              // overflow: pw.TextOverflow.ellipsis,
                              textAlign: index == 0
                                  ? pw.TextAlign.start
                                  : index == orderInfoModels.length - 1
                                      ? pw.TextAlign.end
                                      : pw.TextAlign.center,
                            ),
                            pw.SizedBox(height: 5.h),
                          ],
                        );
                      }),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
          if (totalAmount != null) ...[
            // pw.Divider(
            //   height: 1,
            // ),
            if (totalAmount != null) ...[
              pw.Padding(
                padding: pw.EdgeInsets.symmetric(horizontal: 5.w),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    if (((gstIncluded?[0] ?? false) == true) &&
                        ((gstIncluded?[1] ?? false) == false)) ...[
                      pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.end,
                        children: [
                          pw.Spacer(),
                          pw.Text(
                            "CGST :",
                            style: txtStyle.copyWith(
                                color: PdfColor.fromHex("#343A40")),
                          ),
                          pw.Spacer(),
                          pw.Text(
                            "${Constants.rupeeSign} ${cgst}",
                            style: txtStyle.copyWith(
                                color: PdfColor.fromHex("#343A40")),
                          ),
                        ],
                      ),
                      pw.SizedBox(height: 5.h),
                    ],
                    if (((gstIncluded?[0] ?? false) == true) &&
                        ((gstIncluded?[1] ?? false) == false)) ...[
                      pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.end,
                        children: [
                          pw.Spacer(),
                          pw.Text(
                            "SGST :",
                            style: txtStyle.copyWith(
                                color: PdfColor.fromHex("#343A40")),
                          ),
                          pw.Spacer(),
                          pw.Text(
                            "₹ ${sgst}",
                            style: txtStyle.copyWith(
                                color: PdfColor.fromHex("#343A40")),
                          ),
                        ],
                      ),
                      pw.SizedBox(height: 5.h),
                    ],
                    if (((gstIncluded?[0] ?? false) == true) &&
                        ((gstIncluded?[1] ?? false) == true)) ...[
                      pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.end,
                        children: [
                          pw.Spacer(),
                          pw.Text(
                            "IGST :",
                            style: txtStyle.copyWith(
                                color: PdfColor.fromHex("#343A40")),
                          ),
                          pw.Spacer(),
                          pw.Text(
                            "₹ ${igst}",
                            style: txtStyle.copyWith(
                                color: PdfColor.fromHex("#343A40")),
                          ),
                        ],
                      ),
                      pw.SizedBox(height: 5.h),
                    ],
                    pw.Divider(
                      height: 1,
                    ),
                    pw.SizedBox(height: 5.h),
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.end,
                      children: [
                        pw.Spacer(),
                        pw.Text(
                          "${totalTitle} :",
                          style: txtStyle.copyWith(
                              color: PdfColor.fromHex("#343A40")),
                        ),
                        pw.Spacer(),
                        pw.Text(
                          "₹ ${totalAmount}",
                          style: txtStyle.copyWith(
                              color: PdfColor.fromHex("#343A40")),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ]
          ],
        ]);

    final pdf = pw.Document();
    // final pdfImage = pw.MemoryImage(buffer);
    final img = await rootBundle.load('assets/images/logo.png');
    final imageBytes = img.buffer.asUint8List();
    Utils.showSnackBar(title: "title");
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        // mainAxisAlignment: pw.MainAxisAlignment.center,
        crossAxisAlignment: pw.CrossAxisAlignment.center,
        margin: const pw.EdgeInsets.symmetric(horizontal: 30, vertical: 30),
        build: (pw.Context context) {
          return [
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Center(
                    child: pw.Text('Invoice',
                        style: const pw.TextStyle(fontSize: 20))),
                pw.SizedBox(height: 10.h),
                pw.Row(
                  children: [
                    pw.Expanded(
                      child: pw.Image(pw.MemoryImage(imageBytes)),
                    ),
                    pw.SizedBox(width: 50.w),
                    pw.Expanded(
                      flex: 2,
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.end,
                        children: [
                          pw.Text(
                            homeScreenController
                                    ?.franchiseDetails?.garageName ??
                                "",
                            textAlign: pw.TextAlign.right,
                          ),
                          pw.SizedBox(height: 5.h),
                          pw.Text(
                            homeScreenController?.franchiseDetails?.address ??
                                "",
                            textAlign: pw.TextAlign.right,
                          ),
                          pw.SizedBox(height: 5.h),
                          pw.Row(
                            children: [
                              // const Icon(Icons.phone),
                              // WidthBox(5.w),
                              pw.Expanded(
                                child: pw.Text(
                                  homeScreenController
                                          ?.franchiseDetails?.contactNumber ??
                                      "",
                                  textAlign: pw.TextAlign.right,
                                ),
                              ),
                            ],
                          ),
                          pw.SizedBox(height: 5.h),
                          pw.Row(
                            children: [
                              // const Icon(Icons.email),
                              // WidthBox(5.w),
                              pw.Expanded(
                                child: pw.Text(
                                  homeScreenController
                                          ?.franchiseDetails?.email ??
                                      "",
                                  textAlign: pw.TextAlign.right,
                                ),
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 20.h),
                pw.Row(
                  children: [
                    pw.SizedBox(width: 5.w),
                    pw.Text(
                      !widget.isInvoice
                          ? "Delivery Time: ${(DateFormat.d().add_MMM().add_y().add_jm().format(
                                DateTime.parse(
                                  // DateTime.now().toString(),
                                  widget
                                          .createRepairOrderModel
                                          .repairDetailsModel
                                          ?.estimatedDelivery ??
                                      "",
                                ),
                              ).toString())}"
                          : "Job Card: ${widget.createRepairOrderModel.createdAt.toString()}  ",
                      style: pw.TextStyle(
                          fontSize: 11, color: PdfColor.fromHex("#5B5E62")
                          // color: Color(0xFF5B5E62),
                          ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 10.h),
                columnData(
                    orderInfoModels: [
                      OrderInfoModel(header: "CUSTOMER", columnData: [
                        widget.createRepairOrderModel.customerDetailsModel
                                ?.username ??
                            "",

                        widget.createRepairOrderModel.customerDetailsModel
                                ?.phone ??
                            "",
                        // widget.repairOrderModel.customerDetailsModel
                        //         ?.email ??
                        //     "",
                        // widget.repairOrderModel.customerDetailsModel
                        //         ?.address ??
                        //     "",
                      ]),
                      OrderInfoModel(
                        header: "VEHICLE",
                        columnData: [
                          "${widget.createRepairOrderModel.vehicleDetailsModel?.registrationNumber ?? ""}"
                              "${widget.createRepairOrderModel.vehicleDetailsModel?.make ?? ""} ${widget.createRepairOrderModel.vehicleDetailsModel?.model ?? ""} "
                              "",

                          " Odo mt: ${widget.createRepairOrderModel.additionalInformationModel?.odometer} KMs" // widget.repairOrderModel.vehicleDetailsModel
                          //         ?.model ??
                          //     "",
                        ],
                      ),
                      OrderInfoModel(
                        header: !widget.isInvoice ? "ESTIMATE" : "INVOICE",
                        columnData: !widget.isInvoice
                            ? [
                                (DateFormat.d()
                                    .add_MMM()
                                    .add_y()
                                    .add_jm()
                                    .format(
                                      DateTime.parse(
                                        widget
                                                .createRepairOrderModel
                                                .repairDetailsModel
                                                ?.estimatedDelivery ??
                                            "",
                                      ),
                                    )),
                                "",
                                "Amount: ₹${widget.createRepairOrderModel.repairDetailsModel?.total.toString()}",
                              ]
                            : [
                                """# ${widget.createRepairOrderModel.jobCardId}""",
                                // DateFormat("MMM d, y").format(
                                //   DateTime.parse(
                                //       widget.invoiceModel!.createdAt ??
                                //           ""),
                                // ),
                                "",
                                "Amount: ₹${widget.createRepairOrderModel.repairDetailsModel?.total.toString()}",
                              ],
                      ),
                    ],
                    totalTitle: null,
                    totalAmount: null,
                    showBorderInEveryColumnData: null),
                pw.SizedBox(height: 10.h),
                pw.Container(
                  color: PdfColor.fromHex("#AEE4FF"),
                  padding: pw.EdgeInsets.symmetric(horizontal: 2.w),
                  child: pw.Row(
                    // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      ...List.generate(
                        (widget.createRepairOrderModel.gstIncluded ?? false)
                            ? orderPartsInfoHeadersGST.length
                            : orderPartsInfoHeaders.length,
                        (index) {
                          return pw.Expanded(
                            child: pw.Text(
                              (widget.createRepairOrderModel.gstIncluded ??
                                      false)
                                  ? orderPartsInfoHeadersGST[index]
                                  : orderPartsInfoHeaders[index],
                              // overflow: pw.TextOverflow.ellipsis,
                              textAlign: index == 0
                                  ? pw.TextAlign.start
                                  : index ==
                                          ((widget.createRepairOrderModel
                                                      .gstIncluded ??
                                                  false)
                                              ? orderPartsInfoHeadersGST
                                                      .length -
                                                  1
                                              : orderPartsInfoHeaders.length -
                                                  1)
                                      ? pw.TextAlign.end
                                      : pw.TextAlign.center,
                              style: pw.TextStyle(fontSize: 12),
                            ),
                          );
                        },
                      )
                    ],
                  ),
                ),
                pw.SizedBox(height: 5.h),
                pw.Padding(
                  padding: pw.EdgeInsets.symmetric(horizontal: 5.w),
                  child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        ...List.generate(
                          orderserviceinfoList.length,
                          (index1) {
                            return pw.Row(
                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                ...List.generate(
                                  orderserviceinfoList[index1].length,
                                  (index2) {
                                    return pw.Expanded(
                                      child: pw.Text(
                                        orderserviceinfoList[index1][index2],
                                        maxLines: 4,
                                        // overflow: pw.TextOverflow.ellipsis,
                                        textAlign: index2 == 0
                                            ? pw.TextAlign.start
                                            : index2 ==
                                                    ((widget.createRepairOrderModel
                                                                .gstIncluded ??
                                                            false)
                                                        ? orderServiceInfoHeadersGst
                                                                .length -
                                                            1
                                                        : orderServiceInfoHeaders
                                                                .length -
                                                            1)
                                                ? pw.TextAlign.end
                                                : pw.TextAlign.center,
                                      ),
                                    );
                                  },
                                )
                              ],
                            );
                          },
                        )
                      ]),
                ),
                if (widget.createRepairOrderModel.repairDetailsModel
                        ?.servicesTotal !=
                    null) ...[
                  pw.Divider(height: 1),
                  pw.Padding(
                    padding: pw.EdgeInsets.symmetric(horizontal: 5.w),
                    child: pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.end,
                      children: [
                        pw.Spacer(),
                        pw.Text(
                          "Total :",
                          style:
                              pw.TextStyle(color: PdfColor.fromHex("#343A40")),
                        ),
                        pw.Spacer(),
                        pw.Text(
                          "₹ ${widget.createRepairOrderModel.repairDetailsModel?.servicesTotal?.toStringAsFixed(2)}",
                          style: txtStyle.copyWith(
                              color: PdfColor.fromHex("#343A40")),
                        ),
                      ],
                    ),
                  ),
                ],
                pw.SizedBox(height: 10.h),
                pw.Container(
                  color: PdfColor.fromHex("#AEE4FF"),
                  padding: pw.EdgeInsets.symmetric(horizontal: 2.w),
                  child: pw.Row(
                    // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      ...List.generate(
                        (widget.createRepairOrderModel.gstIncluded ?? false)
                            ? orderPartsInfoHeadersGST.length
                            : orderPartsInfoHeaders.length,
                        (index) {
                          return pw.Expanded(
                            child: pw.Text(
                              (widget.createRepairOrderModel.gstIncluded ??
                                      false)
                                  ? orderPartsInfoHeadersGST[index]
                                  : orderPartsInfoHeaders[index],
                              // overflow: pw.TextOverflow.ellipsis,
                              textAlign: index == 0
                                  ? pw.TextAlign.start
                                  : index ==
                                          ((widget.createRepairOrderModel
                                                      .gstIncluded ??
                                                  false)
                                              ? orderPartsInfoHeadersGST
                                                      .length -
                                                  1
                                              : orderPartsInfoHeaders.length -
                                                  1)
                                      ? pw.TextAlign.end
                                      : pw.TextAlign.center,
                              style: pw.TextStyle(fontSize: 12),
                            ),
                          );
                        },
                      )
                    ],
                  ),
                ),
                pw.SizedBox(height: 5.h),
                pw.Padding(
                  padding: pw.EdgeInsets.symmetric(horizontal: 5.w),
                  child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        ...List.generate(
                          orderPartsinfoList.length,
                          (index1) {
                            return pw.Row(
                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                ...List.generate(
                                  orderPartsinfoList[index1].length,
                                  (index2) {
                                    return pw.Expanded(
                                      child: index2 == 0
                                          ? pw.Row(
                                              crossAxisAlignment:
                                                  pw.CrossAxisAlignment.start,
                                              children: [
                                                pw.Expanded(
                                                  child: pw.Text(
                                                    '${orderPartsinfoList[index1][index2][0]}',
                                                    maxLines: 4,
                                                    // overflow:
                                                    //     pw.TextOverflow.ellipsis,
                                                    textAlign: index2 == 0
                                                        ? pw.TextAlign.start
                                                        : index2 ==
                                                                orderPartsInfoHeaders
                                                                        .length -
                                                                    1
                                                            ? pw.TextAlign.end
                                                            : pw.TextAlign
                                                                .center,
                                                  ),
                                                ),
                                                pw.SizedBox(width: 5),
                                                pw.Text(
                                                  "x",
                                                  maxLines: 4,
                                                  // overflow:
                                                  //     TextOverflow.ellipsis,
                                                  textAlign: index2 == 0
                                                      ? pw.TextAlign.start
                                                      : index2 ==
                                                              orderPartsInfoHeaders
                                                                      .length -
                                                                  1
                                                          ? pw.TextAlign.end
                                                          : pw.TextAlign.center,
                                                ),
                                                pw.SizedBox(width: 7),
                                                pw.Text(
                                                  orderPartsinfoList[index1]
                                                      [index2][1],
                                                  maxLines: 4,
                                                  // overflow:
                                                  //     TextOverflow.ellipsis,
                                                  textAlign: index2 == 0
                                                      ? pw.TextAlign.start
                                                      : index2 ==
                                                              orderPartsInfoHeaders
                                                                      .length -
                                                                  1
                                                          ? pw.TextAlign.end
                                                          : pw.TextAlign.center,
                                                ),
                                                // '${orderPartsinfoList[index1][index2][0]} X ${orderPartsinfoList[index1][index2][1]}'
                                              ],
                                            )
                                          : pw.Text(
                                              orderPartsinfoList[index1]
                                                  [index2],
                                              maxLines: 4,
                                              // overflow: pw.TextOverflow.ellipsis,
                                              textAlign: index2 == 0
                                                  ? pw.TextAlign.start
                                                  : index2 ==
                                                          ((widget.createRepairOrderModel
                                                                      .gstIncluded ??
                                                                  false)
                                                              ? orderPartsInfoHeadersGST
                                                                      .length -
                                                                  1
                                                              : orderPartsInfoHeaders
                                                                      .length -
                                                                  1)
                                                      ? pw.TextAlign.end
                                                      : pw.TextAlign.center,
                                            ),
                                    );
                                  },
                                )
                              ],
                            );
                          },
                        )
                      ]),
                ),
                if (widget.createRepairOrderModel.repairDetailsModel
                        ?.partsTotal !=
                    null) ...[
                  pw.Divider(height: 1),
                  pw.Padding(
                    padding: pw.EdgeInsets.symmetric(horizontal: 5.w),
                    child: pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.end,
                      children: [
                        pw.Spacer(),
                        pw.Text(
                          "Total :",
                          style:
                              pw.TextStyle(color: PdfColor.fromHex("#343A40")),
                        ),
                        pw.Spacer(),
                        pw.Text(
                          "₹ ${widget.createRepairOrderModel.repairDetailsModel?.partsTotal?.toStringAsFixed(2)}",
                          style: txtStyle.copyWith(
                              color: PdfColor.fromHex("#343A40")),
                        ),
                      ],
                    ),
                  ),
                ],
                pw.SizedBox(height: 10.h),
                columnData(
                    gstIncluded: [
                      widget.createRepairOrderModel.gstIncluded ?? false,
                      widget.createRepairOrderModel.isigst ?? false,
                    ],
                    cgst: ((partssGST + servicesGST) / 2).toString(),
                    sgst: ((partssGST + servicesGST) / 2).toString(),
                    igst: (partssGST + servicesGST).toString(),
                    orderInfoModels: [
                      OrderInfoModel(header: 'SUMMARY', columnData: []),
                      OrderInfoModel(
                        header: '',
                        columnData: ["SUB TOTAL :"],
                      ),
                      OrderInfoModel(
                        header: '',
                        columnData: [
                          "₹ $subtotal",
                        ],
                      )
                    ],
                    totalTitle: "PAYABLE AMOUNT",
                    totalAmount: widget
                        .createRepairOrderModel.repairDetailsModel!.total
                        .toString(),
                    showBorderInEveryColumnData: null)
              ],
            )
          ];
        },
      ),
    );

    RepairDetailsController repairDetailsController =
        Get.find<RepairDetailsController>();

    String pdfName = repairDetailsController
            .createRepairOrderModel.customerDetailsModel?.username
            ?.replaceAll(' ', '_') ??
        "";

    if (widget.createRepairOrderModel.vehicleDetailsModel == null) {
      pdfName += "_invoice.pdf";
    } else {
      pdfName += "_repair_order.pdf";
    }

    await Printing.sharePdf(bytes: await pdf.save(), filename: pdfName);
  }

  @override
  Widget build(BuildContext context) {
    final signatureCanvas = Signature(
      controller: _signatureController,
      width: double.maxFinite,
      backgroundColor: colorsConstants.whiteColor,
    );

    return Scaffold(
      appBar: AppBar(
          title: const Center(child: Text("Preview")),
          elevation: 0.5,
          backgroundColor: colorsConstants.whiteColor,
          shadowColor: colorsConstants.whiteColor,
          surfaceTintColor: colorsConstants.whiteColor,
          // centerTitle: true,
          actions: [
            !widget.isInvoice
                ? InkWell(
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (context) {
                          return getSignaturePad(signatureCanvas, context);
                        },
                      );
                    },
                    child: Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 2.w, vertical: 2.h),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: colorsConstants.primaryRed,
                          width: 1.5.sp,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            CupertinoIcons.add_circled_solid,
                            color: colorsConstants.primaryRed,
                            size: 20.sp,
                          ),
                          WidthBox(5.w),
                          Text(
                            "SIGNATURE",
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: colorsConstants.primaryRed,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                : const SizedBox(),
            WidthBox(10.w),
          ]),
      body: GetBuilder<RepairDetailsController>(
          builder: (repairDetailsController) {
        print("-------777${repairDetailsController.repairOrderImages.length}");
        return Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    RepaintBoundary(
                      key: _globalKey,
                      child: Column(
                        children: [
                          HeightBox(10.h),
                          Text(
                            !widget.isInvoice ? "Repair Order" : "Invoices",
                            style: TextStyle(
                                fontSize: 20.sp, fontWeight: FontWeight.bold),
                          ),
                          HeightBox(10.h),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 20.w),
                            child: Row(
                              children: [
                                Expanded(
                                  flex: 1,
                                  child: Image.asset(
                                    assetsConstants.logo,
                                    // height: 80.h,
                                    width: 100.w,
                                    fit: BoxFit.fill,
                                  ),
                                ),
                                WidthBox(50.w),
                                Expanded(
                                  flex: 2,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      Text(
                                        homeScreenController?.franchiseDetails
                                                ?.garageName ??
                                            "",
                                        textAlign: TextAlign.right,
                                      ),
                                      HeightBox(5.h),
                                      Text(
                                        homeScreenController
                                                ?.franchiseDetails?.address ??
                                            "",
                                        textAlign: TextAlign.right,
                                      ),
                                      HeightBox(5.h),
                                      Row(
                                        children: [
                                          const Icon(Icons.phone),
                                          WidthBox(5.w),
                                          Expanded(
                                            child: Text(
                                              homeScreenController
                                                      ?.franchiseDetails
                                                      ?.contactNumber ??
                                                  "",
                                              textAlign: TextAlign.right,
                                            ),
                                          ),
                                        ],
                                      ),
                                      Row(
                                        children: [
                                          const Icon(Icons.email),
                                          WidthBox(5.w),
                                          Expanded(
                                            child: Text(
                                              homeScreenController
                                                      ?.franchiseDetails
                                                      ?.email ??
                                                  "",
                                              textAlign: TextAlign.right,
                                            ),
                                          ),
                                        ],
                                      )
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          HeightBox(20.h),
                          Row(
                            children: [
                              WidthBox(5.w),
                              Text(
                                !widget.isInvoice
                                    ? """Delivery Time: ${DateFormat.d().add_MMM().add_y().add_jm().format(
                                          DateTime.parse(
                                            widget
                                                    .createRepairOrderModel
                                                    .repairDetailsModel
                                                    ?.estimatedDelivery ??
                                                "",
                                          ),
                                        )}"""
                                    : """Job Card: ${DateFormat.d().add_MMM().add_y().add_jm().format(
                                          DateTime.tryParse(
                                                widget.createRepairOrderModel
                                                        .createdAt ??
                                                    "",
                                              ) ??
                                              DateTime.now(),
                                        )}  """,
                                style: TextStyle(
                                  fontSize: 11.sp,
                                  color: colorsConstants.hintGrey,
                                ),
                              ),
                            ],
                          ),
                          HeightBox(10.h),
                          OrderInfoContainer(
                            orderInfoModels: [
                              OrderInfoModel(header: "CUSTOMER", columnData: [
                                widget.createRepairOrderModel
                                        .customerDetailsModel?.username ??
                                    "",
                                widget.createRepairOrderModel
                                        .customerDetailsModel?.phone ??
                                    "",

                                // widget.repairOrderModel.customerDetailsModel
                                //         ?.email ??
                                //     "",
                                // widget.repairOrderModel.customerDetailsModel
                                //         ?.address ??
                                //     "",
                              ]),
                              OrderInfoModel(
                                header: "VEHICLE",
                                columnData: [
                                  "${widget.createRepairOrderModel.vehicleDetailsModel?.registrationNumber ?? "-"}"
                                      "${widget.createRepairOrderModel.vehicleDetailsModel?.make ?? ""} ${widget.createRepairOrderModel.vehicleDetailsModel?.model ?? ""} "
                                      "",
                                  "Odo mt: ${widget.createRepairOrderModel.additionalInformationModel?.odometer ?? "-"} KMs"
                                  // widget.repairOrderModel.vehicleDetailsModel
                                  //         ?.model ??
                                  //     "",
                                ],
                              ),
                              OrderInfoModel(
                                header:
                                    !widget.isInvoice ? "ESTIMATE" : "INVOICE",
                                columnData: !widget.isInvoice
                                    ? [
                                        (DateFormat.d()
                                            .add_MMM()
                                            .add_y()
                                            .add_jm()
                                            .format(
                                              DateTime.parse(
                                                widget
                                                        .createRepairOrderModel
                                                        .repairDetailsModel
                                                        ?.estimatedDelivery ??
                                                    "",
                                              ),
                                            )),
                                        '',
                                        "Amount: ${Constants.rupeeSign}${widget.createRepairOrderModel.repairDetailsModel?.total.toString()}",
                                      ]
                                    : [
                                        """# ${widget.createRepairOrderModel.jobCardId}""",
                                        " ",
                                        // DateFormat("MMM d, y").format(
                                        //   DateTime.parse(
                                        //       widget.invoiceModel!.createdAt ??
                                        //           ""),
                                        // ),
                                        "Amount: ${Constants.rupeeSign}${widget.createRepairOrderModel.repairDetailsModel?.total.toString()}",
                                      ],
                              ),
                            ],
                          ),
                          HeightBox(10.h),

                          Column(
                            children: [
                              Container(
                                color: colorsConstants.lightBlue,
                                padding: EdgeInsets.symmetric(horizontal: 2.w),
                                child: Row(
                                  // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    ...List.generate(
                                      (widget.createRepairOrderModel
                                                  .gstIncluded ??
                                              false)
                                          ? orderServiceInfoHeadersGst.length
                                          : orderServiceInfoHeaders.length,
                                      (index) {
                                        return Expanded(
                                          child: Text(
                                            (widget.createRepairOrderModel
                                                        .gstIncluded ??
                                                    false)
                                                ? orderServiceInfoHeadersGst[
                                                    index]
                                                : orderServiceInfoHeaders[
                                                    index],
                                            overflow: TextOverflow.ellipsis,
                                            textAlign: index == 0
                                                ? TextAlign.start
                                                : index ==
                                                        ((widget.createRepairOrderModel
                                                                    .gstIncluded ??
                                                                false)
                                                            ? orderServiceInfoHeadersGst
                                                                    .length -
                                                                1
                                                            : orderServiceInfoHeaders
                                                                    .length -
                                                                1)
                                                    ? TextAlign.end
                                                    : TextAlign.center,
                                            style: TextStyle(fontSize: 12),
                                          ),
                                        );
                                      },
                                    )
                                  ],
                                ),
                              ),
                              HeightBox(5.h),
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: 5.w),
                                child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      ...List.generate(
                                        orderserviceinfoList.length,
                                        (index1) {
                                          return Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              ...List.generate(
                                                orderserviceinfoList[index1]
                                                    .length,
                                                (index2) {
                                                  print(orderserviceinfoList[
                                                      index1]);
                                                  return Expanded(
                                                    child: Text(
                                                      orderserviceinfoList[
                                                          index1][index2],
                                                      maxLines: 4,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                      textAlign: index2 == 0
                                                          ? TextAlign.start
                                                          : index2 ==
                                                                  ((widget.createRepairOrderModel.gstIncluded ??
                                                                          false)
                                                                      ? orderServiceInfoHeadersGst
                                                                              .length -
                                                                          1
                                                                      : orderServiceInfoHeaders
                                                                              .length -
                                                                          1)
                                                              ? TextAlign.end
                                                              : TextAlign
                                                                  .center,
                                                      style: TextStyle(
                                                          fontSize: 12),
                                                    ),
                                                  );
                                                },
                                              )
                                            ],
                                          );
                                        },
                                      )
                                    ]
                                    // : widget.orderInfoModels
                                    //     .mapIndexed((index, orderInfoModel) {
                                    //     return Expanded(
                                    //       child: Column(
                                    //         crossAxisAlignment: index == 0
                                    //             ? CrossAxisAlignment.start
                                    //             : index ==
                                    //                     widget.orderInfoModels
                                    //                             .length -
                                    //                         1
                                    //                 ? CrossAxisAlignment.end
                                    //                 : CrossAxisAlignment
                                    //                     .center,
                                    //         children: [
                                    //           ...orderInfoModel.columnData
                                    //               .map((element) {
                                    //             return Column(
                                    //               children: [
                                    //                 Text(
                                    //                   element,
                                    //                   style: TextStyle(
                                    //                       color: colorsConstants
                                    //                           .secondaryBlack),
                                    //                   maxLines: 4,
                                    //                   overflow: TextOverflow
                                    //                       .ellipsis,
                                    //                   textAlign: index == 0
                                    //                       ? TextAlign.start
                                    //                       : index ==
                                    //                               widget.orderInfoModels
                                    //                                       .length -
                                    //                                   1
                                    //                           ? TextAlign.end
                                    //                           : TextAlign
                                    //                               .center,
                                    //                 ),
                                    //                 HeightBox(5.h),
                                    //               ],
                                    //             );
                                    //           }),
                                    //         ],
                                    //       ),
                                    //     );
                                    //   }).toList(),
                                    ),
                              ),
                              if (widget.createRepairOrderModel
                                      .repairDetailsModel?.servicesTotal !=
                                  null) ...[
                                const Divider(
                                  height: 1,
                                ),
                                HeightBox(10.h),
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 5.w),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      const Spacer(),
                                      Text(
                                        "Total :",
                                        style: TextStyle(
                                            color:
                                                colorsConstants.secondaryBlack),
                                      ),
                                      const Spacer(),
                                      (widget.createRepairOrderModel
                                                  .gstIncluded ??
                                              false)
                                          ? Text(
                                              "${Constants.rupeeSign} ${(((widget.createRepairOrderModel.repairDetailsModel?.servicesTotal) ?? 0) + servicesGST).toStringAsFixed(2)}",
                                              style: TextStyle(
                                                  color: colorsConstants
                                                      .secondaryBlack),
                                            )
                                          : Text(
                                              "${Constants.rupeeSign} ${widget.createRepairOrderModel.repairDetailsModel?.servicesTotal.toString()}",
                                              style: TextStyle(
                                                  color: colorsConstants
                                                      .secondaryBlack),
                                            ),
                                    ],
                                  ),
                                ),
                              ]
                            ],
                          ),
                          // OrderInfoContainer(
                          //   orderInfoModels: [

                          //     /*  OrderInfoModel(
                          //       header: "SERVICES",
                          //       columnData: widget.createRepairOrderModel
                          //               .repairDetailsModel?.services
                          //               ?.map((service) {
                          //             return service.title ?? "";
                          //           }).toList() ??
                          //           [],
                          //     ),
                          //     OrderInfoModel(
                          //       header: "QTY",
                          //       columnData: widget.createRepairOrderModel
                          //           .repairDetailsModel!.services!
                          //           .map((service) {
                          //         return service.quantity.toString();
                          //       }).toList(),
                          //     ),
                          //     OrderInfoModel(
                          //       header: "RATE",
                          //       columnData: widget.createRepairOrderModel
                          //           .repairDetailsModel!.services!
                          //           .map((service) {
                          //         return service.rate.toString();
                          //       }).toList(),
                          //     ),
                          //     OrderInfoModel(
                          //       header: "AMOUNT",
                          //       columnData: widget.createRepairOrderModel
                          //           .repairDetailsModel!.services!
                          //           .map((service) {
                          //         return service.amount.toString();
                          //       }).toList(),
                          //     ), */
                          //   ],
                          //   totalTitle: "Total",
                          //   totalAmount:
                          //       "${widget.createRepairOrderModel.repairDetailsModel?.servicesTotal.toString()}",
                          // ),

                          HeightBox(10.h),

                          Column(
                            children: [
                              Container(
                                color: colorsConstants.lightBlue,
                                padding: EdgeInsets.symmetric(horizontal: 2.w),
                                child: Row(
                                  // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    ...List.generate(
                                      (widget.createRepairOrderModel
                                                  .gstIncluded ??
                                              false)
                                          ? orderPartsInfoHeadersGST.length
                                          : orderPartsInfoHeaders.length,
                                      (index) {
                                        return Expanded(
                                          child: Text(
                                            (widget.createRepairOrderModel
                                                        .gstIncluded ??
                                                    false)
                                                ? orderPartsInfoHeadersGST[
                                                    index]
                                                : orderPartsInfoHeaders[index],
                                            overflow: TextOverflow.ellipsis,
                                            textAlign: index == 0
                                                ? TextAlign.start
                                                : index ==
                                                        ((widget.createRepairOrderModel
                                                                    .gstIncluded ??
                                                                false)
                                                            ? orderPartsInfoHeadersGST
                                                                    .length -
                                                                1
                                                            : orderPartsInfoHeaders
                                                                    .length -
                                                                1)
                                                    ? TextAlign.end
                                                    : TextAlign.center,
                                            style: TextStyle(fontSize: 12),
                                          ),
                                        );
                                      },
                                    )
                                  ],
                                ),
                              ),
                              HeightBox(5.h),
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: 5.w),
                                child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      ...List.generate(
                                        orderPartsinfoList.length,
                                        (index1) {
                                          return Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              ...List.generate(
                                                orderPartsinfoList[index1]
                                                    .length,
                                                (index2) {
                                                  return Expanded(
                                                    child: index2 == 0
                                                        ? Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .symmetric(
                                                                    vertical:
                                                                        1.0),
                                                            child: Row(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Expanded(
                                                                  child: Text(
                                                                    '${orderPartsinfoList[index1][index2][0]}',
                                                                    // '${orderPartsinfoList[index1][index2][0]}, ${orderPartsinfoList[index1][index2][0]}, ${orderPartsinfoList[index1][index2][0]}, ${orderPartsinfoList[index1][index2][0]},${orderPartsinfoList[index1][index2][0]}',
                                                                    maxLines: 4,
                                                                    overflow:
                                                                        TextOverflow
                                                                            .ellipsis,
                                                                    textAlign: index2 ==
                                                                            0
                                                                        ? TextAlign
                                                                            .start
                                                                        : index2 ==
                                                                                orderPartsInfoHeaders.length - 1
                                                                            ? TextAlign.end
                                                                            : TextAlign.center,
                                                                    style: TextStyle(
                                                                        fontSize:
                                                                            12),
                                                                  ),
                                                                ),
                                                                const SizedBox(
                                                                    width: 5),
                                                                Text(
                                                                  "x",
                                                                  maxLines: 4,
                                                                  overflow:
                                                                      TextOverflow
                                                                          .ellipsis,
                                                                  textAlign: index2 ==
                                                                          0
                                                                      ? TextAlign
                                                                          .start
                                                                      : index2 ==
                                                                              orderPartsInfoHeaders.length -
                                                                                  1
                                                                          ? TextAlign
                                                                              .end
                                                                          : TextAlign
                                                                              .center,
                                                                  style: TextStyle(
                                                                      fontSize:
                                                                          12),
                                                                ),
                                                                const SizedBox(
                                                                    width: 7),
                                                                Text(
                                                                  orderPartsinfoList[
                                                                          index1]
                                                                      [
                                                                      index2][1],
                                                                  maxLines: 4,
                                                                  overflow:
                                                                      TextOverflow
                                                                          .ellipsis,
                                                                  textAlign: index2 ==
                                                                          0
                                                                      ? TextAlign
                                                                          .start
                                                                      : index2 ==
                                                                              orderPartsInfoHeaders.length -
                                                                                  1
                                                                          ? TextAlign
                                                                              .end
                                                                          : TextAlign
                                                                              .center,
                                                                  style: TextStyle(
                                                                      fontSize:
                                                                          12),
                                                                ),
                                                                // '${orderPartsinfoList[index1][index2][0]} X ${orderPartsinfoList[index1][index2][1]}'
                                                              ],
                                                            ),
                                                          )
                                                        : Text(
                                                            orderPartsinfoList[
                                                                index1][index2],
                                                            maxLines: 4,
                                                            overflow:
                                                                TextOverflow
                                                                    .ellipsis,
                                                            textAlign: index2 ==
                                                                    0
                                                                ? TextAlign
                                                                    .start
                                                                : index2 ==
                                                                        ((widget.createRepairOrderModel.gstIncluded ?? false)
                                                                            ? orderPartsInfoHeadersGST.length -
                                                                                1
                                                                            : orderPartsInfoHeaders.length -
                                                                                1)
                                                                    ? TextAlign
                                                                        .end
                                                                    : TextAlign
                                                                        .center,
                                                            style: TextStyle(
                                                                fontSize: 12),
                                                          ),
                                                  );
                                                },
                                              )
                                            ],
                                          );
                                        },
                                      )
                                    ]
                                    // : widget.orderInfoModels
                                    //     .mapIndexed((index, orderInfoModel) {
                                    //     return Expanded(
                                    //       child: Column(
                                    //         crossAxisAlignment: index == 0
                                    //             ? CrossAxisAlignment.start
                                    //             : index ==
                                    //                     widget.orderInfoModels
                                    //                             .length -
                                    //                         1
                                    //                 ? CrossAxisAlignment.end
                                    //                 : CrossAxisAlignment
                                    //                     .center,
                                    //         children: [
                                    //           ...orderInfoModel.columnData
                                    //               .map((element) {
                                    //             return Column(
                                    //               children: [
                                    //                 Text(
                                    //                   element,
                                    //                   style: TextStyle(
                                    //                       color: colorsConstants
                                    //                           .secondaryBlack),
                                    //                   maxLines: 4,
                                    //                   overflow: TextOverflow
                                    //                       .ellipsis,
                                    //                   textAlign: index == 0
                                    //                       ? TextAlign.start
                                    //                       : index ==
                                    //                               widget.orderInfoModels
                                    //                                       .length -
                                    //                                   1
                                    //                           ? TextAlign.end
                                    //                           : TextAlign
                                    //                               .center,
                                    //                 ),
                                    //                 HeightBox(5.h),
                                    //               ],
                                    //             );
                                    //           }),
                                    //         ],
                                    //       ),
                                    //     );
                                    //   }).toList(),
                                    ),
                              ),
                              if (widget.createRepairOrderModel
                                      .repairDetailsModel?.partsTotal !=
                                  null) ...[
                                const Divider(
                                  height: 1,
                                ),
                                HeightBox(10.h),
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 5.w),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      const Spacer(),
                                      Text(
                                        "Total :",
                                        style: TextStyle(
                                            color:
                                                colorsConstants.secondaryBlack),
                                      ),
                                      const Spacer(),
                                      (widget.createRepairOrderModel
                                                  .gstIncluded ??
                                              false)
                                          ? Text(
                                              "${Constants.rupeeSign} ${((widget.createRepairOrderModel.repairDetailsModel?.partsTotal ?? 0) + partssGST).toStringAsFixed(2)}",
                                              style: TextStyle(
                                                  color: colorsConstants
                                                      .secondaryBlack),
                                            )
                                          : Text(
                                              "${Constants.rupeeSign} ${widget.createRepairOrderModel.repairDetailsModel?.partsTotal.toString()}",
                                              style: TextStyle(
                                                  color: colorsConstants
                                                      .secondaryBlack),
                                            ),
                                    ],
                                  ),
                                ),
                              ],
                            ],
                          ),
                          // HeightBox(10.h),
                          // OrderInfoContainer(
                          //   orderInfoModels: [
                          //     OrderInfoModel(
                          //       header: "PARTS",
                          //       columnData: widget.createRepairOrderModel
                          //           .repairDetailsModel!.parts!
                          //           .map((part) {
                          //         return part.title ?? "";
                          //       }).toList(),
                          //     ),
                          //     OrderInfoModel(
                          //       header: "QTY",
                          //       columnData: widget.createRepairOrderModel
                          //           .repairDetailsModel!.parts!
                          //           .map((part) {
                          //         return part.quantity.toString();
                          //       }).toList(),
                          //     ),
                          //     OrderInfoModel(
                          //       header: "RATE",
                          //       columnData: widget.createRepairOrderModel
                          //           .repairDetailsModel!.parts!
                          //           .map((part) {
                          //         return part.rate.toString();
                          //       }).toList(),
                          //     ),
                          //     OrderInfoModel(
                          //       header: "AMOUNT",
                          //       columnData: widget.createRepairOrderModel
                          //           .repairDetailsModel!.parts!
                          //           .map((part) {
                          //         return part.amount.toString();
                          //       }).toList(),
                          //     ),
                          //   ],
                          //   totalTitle: "Total",
                          //   totalAmount:
                          //       "${widget.createRepairOrderModel.repairDetailsModel?.partsTotal.toString()}",
                          // ),
                          HeightBox(10.h),

                          OrderInfoContainer(
                            totalTitle: "PAYABLE AMOUNT",
                            totalAmount: ((widget.createRepairOrderModel
                                            .repairDetailsModel!.total ??
                                        0) +
                                    (partssGST + servicesGST))
                                .toString(),
                            orderInfoModels: [
                              OrderInfoModel(header: 'SUMMARY', columnData: []),
                              OrderInfoModel(
                                header: '',
                                columnData: ["SUB TOTAL :"],
                              ),
                              OrderInfoModel(
                                header: '',
                                columnData: [
                                  "₹ ${widget.createRepairOrderModel.repairDetailsModel!.total}",
                                ],
                              ),
                            ],
                            gstIncluded: [
                              widget.createRepairOrderModel.gstIncluded ??
                                  false,
                              widget.createRepairOrderModel.isigst ?? false,
                            ],
                            cgst: ((partssGST + servicesGST) / 2).toString(),
                            sgst: ((partssGST + servicesGST) / 2).toString(),
                            igst: (partssGST + servicesGST).toString(),
                          ),
                          // if (!widget.isInvoice) ...[
                          if (Get.find<RepairDetailsController>()
                              .repairOrderImages
                              .isNotEmpty) ...[
                            SizedBox(
                              height: 10,
                            ),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 10.0),
                              child: Row(
                                children: [
                                  Wrap(
                                    runSpacing: 20,
                                    spacing: 20,
                                    runAlignment: WrapAlignment.start,
                                    children: [
                                      ...List.generate(
                                        Get.find<RepairDetailsController>()
                                            .repairOrderImages
                                            .length,
                                        (index) {
                                          return Image.memory(
                                            fit: BoxFit.cover,
                                            Get.find<RepairDetailsController>()
                                                .repairOrderImages[index]
                                                .uInt8List,
                                            height: 100.h,
                                            width: 100.w,
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            const Text(
                              "Images",
                            ),
                          ],
                          // ],
                          if (widget.createRepairOrderModel.repairDetailsModel
                                  ?.customerRemarks?.isNotEmpty ??
                              false) ...[
                            HeightBox(50.h),
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 2.w),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    "Customer Remarks :",
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  ...widget.createRepairOrderModel
                                      .repairDetailsModel!.customerRemarks!
                                      .mapIndexed((index, remark) {
                                    return Row(
                                      children: [
                                        WidthBox(15.w),
                                        Text("${index + 1}. $remark"),
                                      ],
                                    );
                                  }),
                                ],
                              ),
                            ),
                          ],
                          HeightBox(20.h),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 2.w),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Column(
                                  children: [
                                    if (!widget.isInvoice) ...[
                                      if (widget
                                              .createRepairOrderModel
                                              .repairDetailsModel
                                              ?.customerSignatureLink !=
                                          null) ...[
                                        Image.network(
                                          widget
                                                  .createRepairOrderModel
                                                  .repairDetailsModel
                                                  ?.customerSignatureLink ??
                                              "",
                                          height: 100.h,
                                          width: 100.w,
                                        ),
                                        const Text(
                                          "Customer Signature",
                                        ),
                                      ],
                                      if (widget
                                                  .createRepairOrderModel
                                                  .repairDetailsModel
                                                  ?.customerSignatureLink ==
                                              null &&
                                          repairDetailsController.signature !=
                                              null) ...[
                                        Image.memory(
                                          repairDetailsController.signature!,
                                          height: 100.h,
                                          width: 100.w,
                                        ),
                                        const Text(
                                          "Customer Signature",
                                        ),
                                      ],
                                    ],
                                    if (widget.isInvoice) ...[
                                      if (widget
                                              .createRepairOrderModel
                                              .repairDetailsModel
                                              ?.businessOwnerSignatureLink !=
                                          null) ...[
                                        Image.network(
                                          widget
                                                  .createRepairOrderModel
                                                  .repairDetailsModel
                                                  ?.businessOwnerSignatureLink ??
                                              "",
                                          height: 100.h,
                                          width: 100.w,
                                        ),
                                        const Text(
                                          "Business Owner Signature",
                                        ),
                                      ],
                                      if (widget
                                                  .createRepairOrderModel
                                                  .repairDetailsModel
                                                  ?.businessOwnerSignatureLink ==
                                              null &&
                                          repairDetailsController.signature !=
                                              null) ...[
                                        Image.memory(
                                          repairDetailsController.signature!,
                                          height: 100.h,
                                          width: 100.w,
                                        ),
                                        const Text(
                                          "Business Owner Signature",
                                        ),
                                      ],
                                    ]
                                  ],
                                )
                              ],
                            ),
                          ),
                          HeightBox(50.h),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Container(
              width: double.maxFinite,
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              padding: EdgeInsets.only(top: 5.h, bottom: 12.h),
              child: PrimaryButton(
                onPress: () async {
                  try {
                    if (!widget.isInvoice &&
                        repairDetailsController.signature == null) {
                      showDialog(
                        context: context,
                        builder: (context) {
                          return getSignaturePad(signatureCanvas, context);
                        },
                      );
                      return;
                    }
                    dataUpdate();

                    context.loaderOverlay.show(
                        progress: !widget.isInvoice
                            ? "Preparing Your Repair Order..."
                            : "Preparing Your Invoice...");

                    !widget.isInvoice
                        ? await repairDetailsController
                            .addRepairOrderInDatabase(
                            context: context,
                          )
                        : {
                            await FirebaseFirestore.instance
                                .collection(FirebaseCollections.customers.name)
                                .doc(widget.createRepairOrderModel
                                    .customerDetailsModel?.customerId)
                                .update({
                              'lastVisit':
                                  DateTime.now().toString().split(' ').first
                            }),
                            await repairDetailsController.addInvoiceInDatabase(
                              fromCounterSale: widget.isInvoice,
                              context: context,
                              repairOrderId:
                                  widget.createRepairOrderModel.orderId,
                              onCreate: widget.onCreate,
                            )
                          };
                    await _captureAndSharePdf();

                    if (context.mounted) {
                      context.loaderOverlay.hide();
                    }

                    if (context.mounted) {
                      moveToHomeScreen(
                          context: context,
                          navigationOperation:
                              NavigationOperations.pushNamedAndRemoveUntil);
                    }
                  } catch (e) {
                    if (context.mounted) {
                      context.loaderOverlay.hide();
                    }
                  }
                },
                title: !widget.isInvoice
                    ? "Create Repair Order"
                    : "Create Invoice",
              ),
            )
          ],
        );
      }),
    );
  }

  AlertDialog getSignaturePad(Signature signatureCanvas, BuildContext context) {
    RepairDetailsController repairDetailsController =
        Get.find<RepairDetailsController>();

    return AlertDialog(
      insetPadding: EdgeInsets.zero,
      titlePadding: EdgeInsets.zero,
      contentPadding: EdgeInsets.zero,
      shape: const Border(),
      title: Container(
        color: colorsConstants.slateGrey,
        padding: EdgeInsets.symmetric(horizontal: 10.w),
        child: Row(
          children: [
            Text(
              widget.createRepairOrderModel.vehicleDetailsModel != null
                  ? "Customer Signature"
                  : "Business Owner Signature",
              style: TextStyle(fontSize: 16.sp),
            ),
            const Spacer(),
            IconButton(
              onPressed: () {
                _signatureController.clear();
              },
              icon: const Icon(Icons.refresh),
            ),
          ],
        ),
      ),
      content: SizedBox(
        height: 300.h,
        width: 300.w,
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Expanded(child: signatureCanvas),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: 10.w,
                vertical: 5.h,
              ),
              color: colorsConstants.slateGrey,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: PrimaryButton(
                      title: "Close",
                      backgroundColor: colorsConstants.hintGrey,
                      onPress: () {
                        Navigator.pop(context);
                      },
                    ),
                  ),
                  WidthBox(10.w),
                  Expanded(
                    child: PrimaryButton(
                      title: "Save",
                      onPress: () async {
                        Navigator.pop(context);

                        if (_signatureController.isNotEmpty) {
                          repairDetailsController.signature =
                              await _signatureController.toPngBytes();
                        } else {
                          repairDetailsController.signature = null;
                        }

                        setState(() {});
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  dataUpdate() {
    widget.createRepairOrderModel.repairDetailsModel?.servicesTotal = (widget
                .createRepairOrderModel.gstIncluded ??
            false)
        ? (((widget.createRepairOrderModel.repairDetailsModel?.servicesTotal) ??
                0) +
            servicesGST)
        : widget.createRepairOrderModel.repairDetailsModel?.servicesTotal ?? 0;
    widget.createRepairOrderModel.repairDetailsModel?.partsTotal =
        (widget.createRepairOrderModel.gstIncluded ?? false)
            ? (((widget.createRepairOrderModel.repairDetailsModel
                        ?.partsTotal) ??
                    0) +
                partssGST)
            : widget.createRepairOrderModel.repairDetailsModel?.partsTotal ?? 0;
    widget.createRepairOrderModel.repairDetailsModel?.total =
        (widget.createRepairOrderModel.repairDetailsModel!.total ?? 0) +
            (partssGST + servicesGST);
  }
}

class DummyServiceModel {
  final String serviceName;
  final String qty;
  final String rate;
  final String amount;

  DummyServiceModel(
      {required this.serviceName,
      required this.qty,
      required this.rate,
      required this.amount});
}
