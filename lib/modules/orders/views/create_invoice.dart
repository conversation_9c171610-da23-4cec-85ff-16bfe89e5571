import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:speed_force_franchise/modules/orders/controllers/repair_details_controller.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/service_part.model.dart';
import 'package:speed_force_franchise/modules/orders/models/details_card_model.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/modules/orders/widgets/custom_overlay.dart';
import 'package:speed_force_franchise/modules/orders/widgets/details_card.dart';
import 'package:speed_force_franchise/modules/orders/widgets/repair_cost_container.dart';
import 'package:speed_force_franchise/modules/orders/widgets/services_parts_list_view.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

class CreateInvoice extends StatefulWidget {
  const CreateInvoice(
      {super.key, this.invoiceModel, this.onInvoiceCreate, this.isCounterSale});

  final bool? isCounterSale;
  final RepairOrderModel? invoiceModel;
  final void Function()? onInvoiceCreate;

  @override
  State<CreateInvoice> createState() => _CreateInvoiceState();
}

class _CreateInvoiceState extends State<CreateInvoice> {
  TextEditingController userNameTextEditingController = TextEditingController();
  TextEditingController phoneTextEditingController = TextEditingController();
  TextEditingController emailTextEditingController = TextEditingController();
  TextEditingController addressTextEditingController = TextEditingController();
  TextEditingController gstinTextEditingController = TextEditingController();
  List<String> gstTypes = ['Without GST', 'With GST'];
  String? selectedGsttype;
  bool igst = false;

  @override
  void initState() {
    super.initState();
    print(widget.invoiceModel?.toMap());
    Get.delete<RepairDetailsController>();
    RepairDetailsController repairDetailsController = Get.put(
      RepairDetailsController(
          createRepairOrderModel: widget.invoiceModel ?? RepairOrderModel())
        //
        ..fetchServices()
        ..fetchParts()
        ..fetchTags()
        ..update(),
    );

    // for (ServicePartModel element
    //     in widget.invoiceModel?.repairDetailsModel?.services ?? []) {
    //   repairDetailsController.calculateServicePartAmount(
    //       servicePartModel: element);
    // }
    // for (ServicePartModel element
    //     in widget.invoiceModel?.repairDetailsModel?.parts ?? []) {
    //   repairDetailsController.calculateServicePartAmount(
    //       servicePartModel: element);
    // }

    Future.delayed(const Duration(milliseconds: 0), () {
      repairDetailsController.update();
    });
  }

  @override
  void dispose() {
    Get.delete<RepairDetailsController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<RepairDetailsController>(
      builder: (repairDetailsController) {
        // final RepairDetailsController repairDetailsController =
        //     Get.find<RepairDetailsController>();
        return GestureDetector(
          onTap: () {
            FocusManager.instance.primaryFocus?.unfocus();
          },
          child: Scaffold(
            appBar: AppBar(
              title: const Text("Counter Sale"),
              elevation: 0.5,
              backgroundColor: colorsConstants.whiteColor,
              shadowColor: colorsConstants.whiteColor,
              surfaceTintColor: colorsConstants.whiteColor,
            ),
            body: Padding(
              padding: EdgeInsets.zero,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // HeightBox(16.h),
                    // CustomerDetailsContainer(
                    //   repairDetailsModel:
                    //       repairDetailsController.createRepairOrderModel,
                    // ),
                    HeightBox(16.h),

                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 16.w),
                      decoration: BoxDecoration(
                        boxShadow: Constants.boxShadow,
                      ),
                      child: CustomTextField(
                        controller: repairDetailsController
                            .searchCustomerTextEditingController,
                        prefix: Icon(
                          Icons.search_rounded,
                          size: 22.sp,
                        ),
                        hintText: "Search Customer",
                        contentPadding: EdgeInsets.zero,
                        filled: true,
                        textInputAction: TextInputAction.done,
                        fillColor: colorsConstants.whiteColor,
                        onChange: (value) {
                          repairDetailsController.searchCustomer(
                              searchTerm: value);
                          // createRepairOrderController
                          //     .seachCustomerUsingRegistrationNumber(
                          //         searchTerm: value);
                        },
                        border: const OutlineInputBorder(
                          borderRadius: BorderRadius.all(
                            Radius.circular(5),
                          ),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: const OutlineInputBorder(
                          borderRadius: BorderRadius.all(
                            Radius.circular(5),
                          ),
                          borderSide: BorderSide.none,
                        ),
                        focusedBorder: const OutlineInputBorder(
                          borderRadius: BorderRadius.all(
                            Radius.circular(5),
                          ),
                          borderSide: BorderSide.none,
                        ),
                      ),
                    ),
                    HeightBox(16.h),
                    if (repairDetailsController
                            .searchCustomerTextEditingController.text.length >=
                        3) ...[
                      if (repairDetailsController.searchedCustomer != null &&
                          repairDetailsController
                              .searchedCustomer!.isNotEmpty) ...[
                        Column(
                          children: repairDetailsController.searchedCustomer
                                  ?.map(
                                (customer) {
                                  return InkWell(
                                    onTap: () {
                                      repairDetailsController
                                          .createRepairOrderModel
                                          .customerDetailsModel = customer;
                                      repairDetailsController.searchedCustomer =
                                          null;
                                      repairDetailsController
                                          .searchCustomerTextEditingController
                                          .clear();
                                      repairDetailsController.update();
                                    },
                                    child: Container(
                                      margin: EdgeInsets.symmetric(
                                          horizontal: 16.w, vertical: 5.h),
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 10.w, vertical: 5.h),
                                      decoration: BoxDecoration(
                                        color: colorsConstants.whiteColor,
                                        boxShadow: Constants.boxShadow,
                                      ),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                              customer.username ?? "",
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                          Expanded(
                                              child: Text(
                                            "(${customer.phone})",
                                            overflow: TextOverflow.ellipsis,
                                          )),
                                          Expanded(
                                              child: Text(
                                            "Odo meter: (${repairDetailsController.createRepairOrderModel.additionalInformationModel?.odometer ?? ""})",
                                            overflow: TextOverflow.ellipsis,
                                          ))
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ).toList() ??
                              [],
                        ),
                      ],
                      if (repairDetailsController.searchedCustomer == null &&
                          repairDetailsController
                                  .searchCustomerTextEditingController
                                  .text
                                  .length >=
                              3) ...[
                        Center(
                          child: PrimaryButton(
                            onPress: () {
                              showDialog(
                                context: context,
                                builder: (context) {
                                  return addNewCustomerDialog(
                                      repairDetailsController, context);
                                },
                              );
                            },
                            title: "Add New Customer",
                          ),
                        )
                      ]
                    ],
                    HeightBox(16.h),
                    if (repairDetailsController
                            .createRepairOrderModel.customerDetailsModel !=
                        null) ...[
                      Container(
                        width: double.maxFinite,
                        margin: EdgeInsets.symmetric(horizontal: 16.w),
                        padding: EdgeInsets.symmetric(
                            horizontal: 16.w, vertical: 10.h),
                        decoration: BoxDecoration(
                          color: colorsConstants.whiteColor,
                          boxShadow: Constants.boxShadow,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(repairDetailsController.createRepairOrderModel
                                    .customerDetailsModel?.username ??
                                ""),
                            HeightBox(10.h),
                            Text(repairDetailsController.createRepairOrderModel
                                    .customerDetailsModel?.phone ??
                                ""),
                            HeightBox(10.h),
                            Text(
                              "Odo meter: ${repairDetailsController.createRepairOrderModel.additionalInformationModel?.odometer ?? ""} KMs",
                              overflow: TextOverflow.ellipsis,
                            ),
                            HeightBox(10.h),
                            Text(repairDetailsController.createRepairOrderModel
                                    .customerDetailsModel?.email ??
                                ""),
                          ],
                        ),
                      ),
                    ],
                    HeightBox(16.h),
                    ServicesPartsListView(
                      title: "SERVICES",
                      onAdd: () {
                        repairDetailsController
                            .addServiceOverlayPortalController
                            .show();
                      },
                      servicePartList: repairDetailsController.selectedServices,
                    ),
                    HeightBox(16.h),
                    ServicesPartsListView(
                      title: "PARTS",
                      onAdd: () {
                        repairDetailsController.addPartsOverlayPortalController
                            .show();
                      },
                      servicePartList: repairDetailsController.selectedParts,
                    ),
                    // HeightBox(16.h),
                    // Container(
                    //   margin: EdgeInsets.symmetric(horizontal: 16.w),
                    //   padding: EdgeInsets.symmetric(
                    //     horizontal: 16.w,
                    //     vertical: 10.h,
                    //   ),
                    //   decoration: BoxDecoration(
                    //     boxShadow: Constants.boxShadow,
                    //     borderRadius: BorderRadius.circular(5.r),
                    //     color: Colors.white,
                    //   ),
                    //   child: LoadMediaContainer(
                    //     title: "IMAGES",
                    //     onMediaLoaded: (XFile pickedMedia) {
                    //       repairDetailsController.vehicleImages
                    //           .add(pickedMedia);
                    //       repairDetailsController.update();
                    //     },
                    //     onRemoveMedia: (XFile removedMedia) {
                    //       repairDetailsController.vehicleImages
                    //           .remove(removedMedia);
                    //       repairDetailsController.update();
                    //     },
                    //   ),
                    // ),
                    // HeightBox(16.h),
                    // const TagsContainer(),

                    HeightBox(16.h),
                    const RepairCostContainer(),

                    HeightBox(16.h),
                    // const CustomerRemarksContainer(),
                    // HeightBox(16.h),
                    // const EstimatedDeliveryContainer(),
                    // HeightBox(16.h),
                    Container(
                      decoration: BoxDecoration(boxShadow: Constants.boxShadow),
                      margin: EdgeInsets.symmetric(horizontal: 16.w),
                      child: ElevatedButton(
                        onPressed: () {
                          FocusManager.instance.primaryFocus?.unfocus();
                          if (repairDetailsController.createRepairOrderModel
                                  .customerDetailsModel ==
                              null) {
                            Utils.showSnackBar(
                                title: "Please select a customer");
                            return;
                          }

                          if (repairDetailsController
                                  .selectedServices.isEmpty &&
                              repairDetailsController.selectedParts.isEmpty) {
                            Utils.showSnackBar(
                                title: "Please add atleast 1 Service or Part");
                            return;
                          }
                          repairDetailsController.confirmOrder(
                            context: context,
                            isInvoice: (widget.isCounterSale ?? false) ||
                                (widget.invoiceModel != null),
                            onCreate: widget.onInvoiceCreate,
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: colorsConstants.primaryRed,
                          foregroundColor: colorsConstants.whiteColor,
                          shape: ContinuousRectangleBorder(
                            borderRadius: BorderRadius.circular(5.r),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              widget.invoiceModel != null
                                  ? "Preview Invoice"
                                  : "Prepare Invoice",
                            ),
                          ],
                        ),
                      ),
                    ),
                    HeightBox(40.h),
                    OverlayPortal(
                      controller: repairDetailsController
                          .addServiceOverlayPortalController,
                      overlayChildBuilder: (BuildContext context) {
                        return CustomOverlay(
                          title: "Choose Services",
                          allowMultiSelection: true,
                          onBackPress: () {
                            repairDetailsController
                                .addServiceOverlayPortalController
                                .hide();
                          },
                          onMultiSelectSubmit: (List<String> selectedServices) {
                            repairDetailsController
                                .addServiceOverlayPortalController
                                .hide();
                            repairDetailsController.addServices(
                              servicesNames: selectedServices,
                            );
                          },
                          dataSource: repairDetailsController.services
                              .fold(<String>[], (previousValue, element) {
                            previousValue.add(element.name ?? "");
                            return previousValue;
                          }),
                          addNewOnPress: () {
                            moveToAddServicePartScreen(
                              context: context,
                              isAddingService: true,
                              revertCallback: (_) async {
                                await repairDetailsController.fetchServices();
                                repairDetailsController.update();
                              },
                            );
                          },
                        );
                      },
                    ),
                    OverlayPortal(
                      controller: repairDetailsController
                          .addPartsOverlayPortalController,
                      overlayChildBuilder: (BuildContext context) {
                        return CustomOverlay(
                          title: "Choose Parts",
                          allowMultiSelection: true,
                          onBackPress: () {
                            repairDetailsController
                                .addPartsOverlayPortalController
                                .hide();
                          },
                          onMultiSelectSubmit: (List<String> selectedParts) {
                            repairDetailsController
                                .addPartsOverlayPortalController
                                .hide();
                            repairDetailsController.addParts(
                              partsNames: selectedParts,
                            );
                          },
                          dataSource: repairDetailsController.parts
                              .fold(<String>[], (previousValue, element) {
                            previousValue.add(element.name ?? "");
                            return previousValue;
                          }),
                          addNewOnPress: () {
                            moveToAddServicePartScreen(
                              context: context,
                              isAddingService: false,
                              revertCallback: (_) async {
                                await repairDetailsController.fetchParts();
                                repairDetailsController.update();
                              },
                            );
                          },
                        );
                      },
                    ),
                    OverlayPortal(
                      controller: repairDetailsController
                          .addTagsOverlayPortalController,
                      overlayChildBuilder: (BuildContext context) {
                        return CustomOverlay(
                          title: "Select Tags",
                          allowMultiSelection: true,
                          onBackPress: () {
                            repairDetailsController
                                .addTagsOverlayPortalController
                                .hide();
                          },
                          onMultiSelectSubmit: (List<String> selectedTags) {
                            repairDetailsController
                                .addTagsOverlayPortalController
                                .hide();
                            repairDetailsController.addTags(
                              tags: selectedTags,
                            );
                          },
                          dataSource: repairDetailsController.tags,
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  AlertDialog addNewCustomerDialog(
      RepairDetailsController repairDetailsController, BuildContext context) {
    return AlertDialog(
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text("Add New"),
            HeightBox(10.h),
            const Text("Enter customer details"),
            HeightBox(10.h),
            DetailsCard(
              cardTitle: "Personal Details",
              /* extraFields: [
                Divider(
                  height: 2.h,
                  color: colorsConstants.slateGrey,
                ),
                Row(
                  children: [
                    ...List.generate(
                      gstTypes.length,
                      (index) {
                        return Row(
                          children: [
                            Radio<String>(
                              value: gstTypes[index],
                              groupValue: selectedGsttype,
                              onChanged: (value) {
                                selectedGsttype = value;
                                setState(() {});
                              },
                            ),
                            // SizedBox(width: 10),
                            Text(gstTypes[index])
                          ],
                        );
                      },
                    ),
                  ],
                ),
                if (selectedGsttype == gstTypes[1]) ...[
                  const SizedBox(width: 5),
                  Row(
                    children: [
                      Checkbox(
                        value: igst,
                        onChanged: (value) {
                          if (value != null) {
                            igst = value;
                            setState(() {});
                          }
                        },
                      ),
                      const Text('IGST')
                    ],
                  )
                ]
                // ElevatedButton(
                //     onPressed: () {}, child: Text("data"))
              ],
               */
              detailsCardTextsFieldData: [
                DetailsCardModel(
                  hintText: "User Name*",
                  textEditingController: userNameTextEditingController,
                  prefixIcon: Icon(
                    CupertinoIcons.person_alt,
                    size: 24.sp,
                  ),
                ),
                DetailsCardModel(
                  hintText: "Phone Number*",
                  keyboardType: TextInputType.phone,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(10),
                  ],
                  textEditingController: phoneTextEditingController,
                  prefixIcon: Icon(
                    CupertinoIcons.phone_fill,
                    size: 24.sp,
                  ),
                  suffixIcon: IconButton(
                    icon: Icon(
                      Icons.contact_phone_outlined,
                      size: 24.sp,
                    ),
                    onPressed: () async {
                      if (await FlutterContacts.requestPermission()) {
                        Contact? contact =
                            await FlutterContacts.openExternalPick();

                        if (contact != null) {
                          debugPrint(contact.phones[0].number);
                          phoneTextEditingController.text =
                              contact.phones[0].number;
                        }
                      }
                    },
                  ),
                ),
                DetailsCardModel(
                  hintText: "Email Address",
                  textEditingController: emailTextEditingController,
                  prefixIcon: Icon(
                    CupertinoIcons.mail_solid,
                    size: 24.sp,
                  ),
                ),
                DetailsCardModel(
                  hintText: "Address",
                  textEditingController: addressTextEditingController,
                  prefixIcon: Icon(
                    Icons.location_pin,
                    size: 24.sp,
                  ),
                ),
                DetailsCardModel(
                  hintText: "GSTIN",
                  textEditingController: gstinTextEditingController,
                  prefixIcon: Icon(
                    CupertinoIcons.doc_text_fill,
                    size: 24.sp,
                  ),
                ),
              ],
            ),
            HeightBox(16.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                PrimaryButton(
                  onPress: () async {
                    if (userNameTextEditingController.text.isEmpty ||
                        phoneTextEditingController.text.isEmpty) {
                      Utils.showSnackBar(title: "Required fields are missing");
                      return;
                    }
                    repairDetailsController.createRepairOrderModel
                        .customerDetailsModel = CustomerDetailsModel(
                      username: userNameTextEditingController.text,
                      phone: phoneTextEditingController.text,
                      email: emailTextEditingController.text,
                      address: addressTextEditingController.text,
                      gstin: gstinTextEditingController.text,
                    );
                    await repairDetailsController
                        .addUpdateCustomerDetailsInDatabase();
                    if (context.mounted) {
                      Navigator.pop(context);
                    }
                  },
                  title: "      Add      ",
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
