import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/orders/controllers/create_repair_order_controller.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/vehicle_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/details_card_model.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/modules/orders/widgets/custom_overlay.dart';
import 'package:speed_force_franchise/modules/orders/widgets/details_card.dart';
import 'package:speed_force_franchise/modules/orders/widgets/fuel_level_slider.dart';
import 'package:speed_force_franchise/modules/orders/widgets/insurance_expiry_date_container.dart';
import 'package:speed_force_franchise/modules/orders/widgets/insurance_provider_dialog.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

class CreateRepairOrderOrAddCustomer extends StatefulWidget {
  const CreateRepairOrderOrAddCustomer({
    super.key,
    this.addCustomerOnly = false,
    this.vehicleOnly = false,
    this.customerId,
  });

  final bool addCustomerOnly;
  final bool vehicleOnly;
  final String? customerId;

  @override
  State<CreateRepairOrderOrAddCustomer> createState() =>
      _CreateRepairOrderOrAddCustomerState();
}

class _CreateRepairOrderOrAddCustomerState
    extends State<CreateRepairOrderOrAddCustomer> {
  bool addCustomerOnly = false;
  bool vehicleOnly = false;
  DateTime? vehiclePurchaseDate;
  bool submitted = false;

  @override
  void initState() {
    super.initState();

    CreateRepairOrderController createRepairOrderController =
        Get.put(CreateRepairOrderController());
    createRepairOrderController.fetchVehicles();
    createRepairOrderController.fetchCustomersVehicles();

    addCustomerOnly = widget.addCustomerOnly;
    vehicleOnly = widget.vehicleOnly;
    createRepairOrderController.editVehicle = vehicleOnly;
  }

  @override
  void dispose() {
    Get.delete<CreateRepairOrderController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: GetBuilder<CreateRepairOrderController>(
        builder: (_) {
          CreateRepairOrderController createRepairOrderController =
              Get.find<CreateRepairOrderController>();

          return Scaffold(
            appBar: AppBar(
              title: Text(addCustomerOnly
                  ? "Add Customer"
                  : vehicleOnly
                      ? "Add Vehicle"
                      : "Customer (Repair Order)"),
              elevation: 0.5,
              backgroundColor: colorsConstants.whiteColor,
              shadowColor: colorsConstants.whiteColor,
              surfaceTintColor: colorsConstants.whiteColor,
            ),
            body: Column(
              children: [
                HeightBox(16.h),
                if (createRepairOrderController.showSearchField &&
                    (!addCustomerOnly && !vehicleOnly)) ...[
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 16.w),
                    child: const Text(
                      "Search customer vehicle by customer name/registration number. If not found create new vehicle.",
                      textAlign: TextAlign.center,
                      textScaler: TextScaler.linear(0.9),
                    ),
                  ),
                  HeightBox(16.h),
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 16.w),
                    decoration: BoxDecoration(
                      boxShadow: Constants.boxShadow,
                    ),
                    child: CustomTextField(
                      controller: createRepairOrderController
                          .searchTextEditingController,
                      prefix: Icon(
                        Icons.search_rounded,
                        size: 22.sp,
                      ),
                      hintText: "Customer Name/Registration Number",
                      contentPadding: EdgeInsets.zero,
                      filled: true,
                      fillColor: colorsConstants.whiteColor,
                      textInputAction: TextInputAction.done,
                      onChange: (value) {
                        createRepairOrderController
                            .seachCustomerUsingRegistrationNumber(
                                searchTerm: value);
                      },
                      border: const OutlineInputBorder(
                        borderRadius: BorderRadius.all(
                          Radius.circular(5),
                        ),
                        borderSide: BorderSide.none,
                      ),
                      enabledBorder: const OutlineInputBorder(
                        borderRadius: BorderRadius.all(
                          Radius.circular(5),
                        ),
                        borderSide: BorderSide.none,
                      ),
                      focusedBorder: const OutlineInputBorder(
                        borderRadius: BorderRadius.all(
                          Radius.circular(5),
                        ),
                        borderSide: BorderSide.none,
                      ),
                    ),
                  ),
                  HeightBox(20.h),
                  if ((!createRepairOrderController.existingCustomerSelected &&
                          !createRepairOrderController.addingNewCustomer) &&
                      createRepairOrderController
                          .searchedCustomersVehicles.isEmpty) ...[
                    const Spacer(),
                    PrimaryButton(
                      title: "Add New Customer",
                      onPress: () {
                        FocusManager.instance.primaryFocus?.unfocus();
                        createRepairOrderController.showSearchField = false;

                        createRepairOrderController.addingNewCustomer = true;
                        createRepairOrderController.update();
                        // setState(() {});
                      },
                    ),
                    const Spacer(),
                  ],
                ],
                if (createRepairOrderController
                        .searchTextEditingController.text.isNotEmpty &&
                    createRepairOrderController
                        .searchedCustomersVehicles.isNotEmpty) ...[
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: generateSearchedCustomer(
                          createRepairOrderController,
                        ),
                      ),
                    ),
                  ),
                ],
                if (vehicleOnly) ...[
                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            setState(() {
                              createRepairOrderController.editVehicle = true;
                            });
                            // createRepairOrderController.createRepairOrderModel =
                            //     RepairOrderModel(
                            //         vehicleDetailsModel:
                            //             createRepairOrderController
                            //                 .selectedVehicle);
                            createRepairOrderController
                                .populateVehicleDetails();
                          },
                          child: Container(
                            alignment: Alignment.center,
                            margin: EdgeInsets.only(left: 16.w),
                            padding: EdgeInsets.symmetric(
                                horizontal: 10.w, vertical: 5.h),
                            decoration: BoxDecoration(
                              color: createRepairOrderController.editVehicle
                                  ? const Color.fromARGB(255, 9, 64, 83)
                                  : colorsConstants.whiteColor,
                              boxShadow: Constants.boxShadow,
                            ),
                            child: Text(
                              "Edit Vehicle",
                              style: TextStyle(
                                fontSize: 15.sp,
                                color: createRepairOrderController.editVehicle
                                    ? colorsConstants.whiteColor
                                    : colorsConstants.hintGrey,
                              ),
                            ),
                          ),
                        ),
                      ),
                      WidthBox(10.w),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            setState(() {
                              createRepairOrderController.editVehicle = false;
                            });
                            createRepairOrderController.clearVehicleData();
                          },
                          child: Container(
                            alignment: Alignment.center,
                            margin: EdgeInsets.only(right: 16.w),
                            padding: EdgeInsets.symmetric(
                                horizontal: 10.w, vertical: 5.h),
                            decoration: BoxDecoration(
                              color: !createRepairOrderController.editVehicle
                                  ? const Color.fromARGB(255, 9, 64, 83)
                                  : colorsConstants.whiteColor,
                              boxShadow: Constants.boxShadow,
                            ),
                            child: Text(
                              "Add New Vehicle",
                              style: TextStyle(
                                fontSize: 15.sp,
                                color: !createRepairOrderController.editVehicle
                                    ? colorsConstants.whiteColor
                                    : colorsConstants.hintGrey,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  HeightBox(10.h),
                  if (createRepairOrderController.editVehicle) ...[
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 16.w),
                      decoration: BoxDecoration(
                        boxShadow: Constants.boxShadow,
                      ),
                      child: CustomTextField(
                        hintText: "Select Vehicle",
                        controller: createRepairOrderController
                            .selectedvehicleTextEditingController,
                        readOnly: true,
                        contentPadding: EdgeInsets.symmetric(
                            horizontal: 10.w, vertical: 10.h),
                        suffix: const Icon(CupertinoIcons.chevron_down),
                        filled: true,
                        fillColor: colorsConstants.whiteColor,
                        textInputAction: TextInputAction.next,
                        onTap: () {
                          createRepairOrderController
                              .vehiclesOverlayPortalController
                              .show();
                        },
                        onChange: (value) {},
                        border: const OutlineInputBorder(
                          borderRadius: BorderRadius.all(
                            Radius.circular(5),
                          ),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: const OutlineInputBorder(
                          borderRadius: BorderRadius.all(
                            Radius.circular(5),
                          ),
                          borderSide: BorderSide.none,
                        ),
                        focusedBorder: const OutlineInputBorder(
                          borderRadius: BorderRadius.all(
                            Radius.circular(5),
                          ),
                          borderSide: BorderSide.none,
                        ),
                      ),
                    ),
                    HeightBox(10.h),
                  ],
                ],
                if (addCustomerOnly ||
                    vehicleOnly ||
                    createRepairOrderController.addingNewCustomer ||
                    createRepairOrderController.existingCustomerSelected) ...[
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (!vehicleOnly) ...[
                            Container(
                              margin: EdgeInsets.symmetric(horizontal: 16.w),
                              child: DetailsCard(
                                cardTitle: "Personal Details",
                                /* extraFields: [
                                  Divider(
                                    height: 2.h,
                                    color: colorsConstants.slateGrey,
                                  ),
                                  Row(
                                    children: [
                                      ...List.generate(
                                        _.gstTypes.length,
                                        (index) {
                                          return Row(
                                            children: [
                                              Radio<String>(
                                                value: _.gstTypes[index],
                                                groupValue: _.selectedGsttype,
                                                onChanged: (value) {
                                                  _.selectedGsttype = value;
                                                  setState(() {});
                                                },
                                              ),
                                              // SizedBox(width: 10),
                                              Text(_.gstTypes[index])
                                            ],
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                  if (_.selectedGsttype == _.gstTypes[1]) ...[
                                    const SizedBox(width: 5),
                                    Row(
                                      children: [
                                        Checkbox(
                                          value: _.igst,
                                          onChanged: (value) {
                                            if (value != null) {
                                              _.igst = value;
                                              setState(() {});
                                            }
                                          },
                                        ),
                                        const Text('IGST')
                                      ],
                                    )
                                  ]
                                  // ElevatedButton(
                                  //     onPressed: () {}, child: Text("data"))
                                ],
                                 */
                                detailsCardTextsFieldData: [
                                  DetailsCardModel(
                                    hintText: "User Name*",
                                    readOnly: createRepairOrderController
                                                .createRepairOrderModel
                                                ?.customerDetailsModel
                                                ?.username !=
                                            null ||
                                        (createRepairOrderController
                                                .createRepairOrderModel
                                                ?.customerDetailsModel
                                                ?.username
                                                ?.isNotEmpty ??
                                            false),
                                    textEditingController:
                                        createRepairOrderController
                                            .userNameTextEditingController,
                                    prefixIcon: Icon(
                                      CupertinoIcons.person_alt,
                                      size: 24.sp,
                                    ),
                                  ),
                                  DetailsCardModel(
                                    hintText: "Phone Number*",
                                    readOnly: createRepairOrderController
                                                .createRepairOrderModel
                                                ?.customerDetailsModel
                                                ?.phone !=
                                            null ||
                                        (createRepairOrderController
                                                .createRepairOrderModel
                                                ?.customerDetailsModel
                                                ?.phone
                                                ?.isNotEmpty ??
                                            false),
                                    keyboardType: TextInputType.phone,
                                    inputFormatters: [
                                      LengthLimitingTextInputFormatter(10),
                                    ],
                                    textEditingController:
                                        createRepairOrderController
                                            .phoneNumberTextEditingController,
                                    prefixIcon: Icon(
                                      CupertinoIcons.phone_fill,
                                      size: 24.sp,
                                    ),
                                    suffixIcon: createRepairOrderController
                                                    .createRepairOrderModel
                                                    ?.customerDetailsModel
                                                    ?.phone !=
                                                null ||
                                            (createRepairOrderController
                                                    .createRepairOrderModel
                                                    ?.customerDetailsModel
                                                    ?.phone
                                                    ?.isNotEmpty ??
                                                false)
                                        ? null
                                        : IconButton(
                                            icon: Icon(
                                              Icons.contact_phone_outlined,
                                              size: 24.sp,
                                            ),
                                            onPressed: () async {
                                              if (await FlutterContacts
                                                  .requestPermission()) {
                                                Contact? contact =
                                                    await FlutterContacts
                                                        .openExternalPick();

                                                if (contact != null) {
                                                  debugPrint(
                                                      contact.phones[0].number);
                                                  createRepairOrderController
                                                          .phoneNumberTextEditingController
                                                          .text =
                                                      contact.phones[0].number;
                                                }
                                              }
                                            },
                                          ),
                                  ),
                                  DetailsCardModel(
                                    hintText: "Email Address",
                                    readOnly: createRepairOrderController
                                                .createRepairOrderModel
                                                ?.customerDetailsModel
                                                ?.email !=
                                            null ||
                                        (createRepairOrderController
                                                .createRepairOrderModel
                                                ?.customerDetailsModel
                                                ?.email
                                                ?.isNotEmpty ??
                                            false),
                                    textEditingController:
                                        createRepairOrderController
                                            .emailTextEditingController,
                                    prefixIcon: Icon(
                                      CupertinoIcons.mail_solid,
                                      size: 24.sp,
                                    ),
                                  ),
                                  DetailsCardModel(
                                    hintText: "Address*",
                                    readOnly: createRepairOrderController
                                                .createRepairOrderModel
                                                ?.customerDetailsModel
                                                ?.address !=
                                            null ||
                                        (createRepairOrderController
                                                .createRepairOrderModel
                                                ?.customerDetailsModel
                                                ?.address
                                                ?.isNotEmpty ??
                                            false),
                                    textEditingController:
                                        createRepairOrderController
                                            .addressTextEditingController,
                                    prefixIcon: Icon(
                                      Icons.location_pin,
                                      size: 24.sp,
                                    ),
                                  ),
                                  // Radio(value: , groupValue: groupValue, onChanged: onChanged)

                                  DetailsCardModel(
                                    hintText: "GSTIN",
                                    readOnly: createRepairOrderController
                                                .createRepairOrderModel
                                                ?.customerDetailsModel
                                                ?.gstin !=
                                            null ||
                                        (createRepairOrderController
                                                .createRepairOrderModel
                                                ?.customerDetailsModel
                                                ?.gstin
                                                ?.isNotEmpty ??
                                            false),
                                    textEditingController:
                                        createRepairOrderController
                                            .gstinTextEditingController,
                                    prefixIcon: Icon(
                                      CupertinoIcons.doc_text_fill,
                                      size: 24.sp,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            HeightBox(16.h),
                          ],
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 16.w),
                            child: DetailsCard(
                              cardTitle: "Vehicle Details",
                              detailsCardTextsFieldData: [
                                DetailsCardModel(
                                  readOnly: true,
                                  textEditingController:
                                      createRepairOrderController
                                          .makeTextEditingController,
                                  onTap: () {
                                    if (createRepairOrderController
                                                .createRepairOrderModel
                                                ?.vehicleDetailsModel
                                                ?.make !=
                                            null ||
                                        (createRepairOrderController
                                                .createRepairOrderModel
                                                ?.vehicleDetailsModel
                                                ?.make
                                                ?.isNotEmpty ??
                                            false)) {
                                      return;
                                    }

                                    createRepairOrderController
                                        .makeOverlayPortalController
                                        .show();
                                  },
                                  hintText: "Make*",
                                  prefixIcon: Icon(
                                    Icons.factory_rounded,
                                    size: 24.sp,
                                  ),
                                ),
                                DetailsCardModel(
                                  hintText: "Model*",
                                  readOnly: true,
                                  textEditingController:
                                      createRepairOrderController
                                          .modelTextEditingController,
                                  prefixIcon: Icon(
                                    Icons.motorcycle_rounded,
                                    size: 24.sp,
                                  ),
                                  onTap: () {
                                    if (createRepairOrderController
                                                .createRepairOrderModel
                                                ?.vehicleDetailsModel
                                                ?.model !=
                                            null ||
                                        (createRepairOrderController
                                                .createRepairOrderModel
                                                ?.vehicleDetailsModel
                                                ?.model
                                                ?.isNotEmpty ??
                                            false)) {
                                      return;
                                    }

                                    createRepairOrderController
                                        .modelOverlayPortalController
                                        .show();
                                  },
                                ),
                                DetailsCardModel(
                                  hintText: "Registration Number*",
                                  readOnly: createRepairOrderController
                                              .createRepairOrderModel
                                              ?.vehicleDetailsModel
                                              ?.registrationNumber !=
                                          null ||
                                      (createRepairOrderController
                                              .createRepairOrderModel
                                              ?.vehicleDetailsModel
                                              ?.registrationNumber
                                              ?.isNotEmpty ??
                                          false),
                                  textEditingController:
                                      createRepairOrderController
                                          .registrationNumberEditingController,
                                  prefixIcon: Icon(
                                    Icons.edit,
                                    size: 24.sp,
                                  ),
                                ),
                                DetailsCardModel(
                                  hintText: "Purchase Date",
                                  readOnly: true,
                                  onTap: () {
                                    showCupertinoModalPopup(
                                      context: context,
                                      builder: (context) {
                                        return Container(
                                          height: 300.h,
                                          color: Colors.white,
                                          child: Column(
                                            children: [
                                              Expanded(
                                                child: CupertinoDatePicker(
                                                  mode: CupertinoDatePickerMode
                                                      .monthYear,
                                                  onDateTimeChanged:
                                                      (DateTime value) {
                                                    debugPrint(
                                                        value.toString());

                                                    vehiclePurchaseDate = value;
                                                  },
                                                ),
                                              ),
                                              HeightBox(10.h),
                                              Container(
                                                margin: EdgeInsets.symmetric(
                                                    horizontal: 20.w),
                                                width: double.maxFinite,
                                                child: PrimaryButton(
                                                    onPress: () {
                                                      if (vehiclePurchaseDate !=
                                                          null) {
                                                        createRepairOrderController
                                                                .vehiclePurchaseDate =
                                                            vehiclePurchaseDate;

                                                        createRepairOrderController
                                                                .purchaseDateEditingController
                                                                .text =
                                                            createRepairOrderController
                                                                .purchaseDateFormat
                                                                .format(
                                                                    vehiclePurchaseDate!);
                                                      }

                                                      Navigator.pop(context);
                                                    },
                                                    title: "Add"),
                                              ),
                                              HeightBox(20.h),
                                            ],
                                          ),
                                        );
                                      },
                                    );
                                  },
                                  textEditingController:
                                      createRepairOrderController
                                          .purchaseDateEditingController,
                                  prefixIcon: Icon(
                                    CupertinoIcons.calendar,
                                    size: 24.sp,
                                  ),
                                ),
                                DetailsCardModel(
                                  hintText: "Engine Number",
                                  textEditingController:
                                      createRepairOrderController
                                          .engineTextEditingController,
                                  prefixIcon: Icon(
                                    Icons.settings_rounded,
                                    size: 24.sp,
                                  ),
                                ),
                                DetailsCardModel(
                                  hintText: "VIN (Chasis Number)",
                                  textEditingController:
                                      createRepairOrderController
                                          .vinTextEditingController,
                                  prefixIcon: Icon(
                                    CupertinoIcons.barcode,
                                    size: 24.sp,
                                  ),
                                ),
                                DetailsCardModel(
                                  hintText: "Insurance Provider",
                                  readOnly: true,
                                  textEditingController:
                                      createRepairOrderController
                                          .insuranceProviderEditingController,
                                  prefixIcon: Icon(
                                    Icons.medical_information_rounded,
                                    size: 24.sp,
                                  ),
                                  onTap: () {
                                    showDialog(
                                      context: context,
                                      builder: (BuildContext context) {
                                        return const InsuranceProviderDialog();
                                      },
                                    );
                                  },
                                ),
                                DetailsCardModel(
                                  keyboardType: TextInputType.number,
                                  textEditingController:
                                      createRepairOrderController
                                          .odometerEditingController,
                                  hintText: "Odometer (in KMs)*",
                                  prefixIcon: Icon(
                                    Icons.speed_outlined,
                                    size: 24.sp,
                                  ),
                                ),

                                // DetailsCardModel(
                                //   hintText: "Insurer GSTIN",
                                //   textEditingController:
                                //       createRepairOrderController
                                //           .insurerGSTINEditingController,
                                //   prefixIcon: Icon(
                                //     CupertinoIcons.doc_text_fill,
                                //     size: 24.sp,
                                //   ),
                                // ),
                                // DetailsCardModel(
                                //   hintText: "Insurer Address",
                                //   textEditingController:
                                //       createRepairOrderController
                                //           .insurerAddressTextEditingController,
                                //   prefixIcon: Icon(
                                //     Icons.badge_rounded,
                                //     size: 24.sp,
                                //   ),
                                // ),
                                // DetailsCardModel(
                                //   hintText: "Policy Number",
                                //   textEditingController:
                                //       createRepairOrderController
                                //           .policyNumberEditingController,
                                //   prefixIcon: Icon(
                                //     CupertinoIcons.doc_text_fill,
                                //     size: 24.sp,
                                //   ),
                                // ),
                              ],
                              trailing: InsuranceExpiryDateContainer(
                                createRepairOrderController:
                                    createRepairOrderController,
                              ),
                            ),
                          ),
                          HeightBox(16.h),
                          if (!addCustomerOnly && !vehicleOnly) ...[
                            Container(
                              margin: EdgeInsets.symmetric(horizontal: 16.w),
                              child: DetailsCard(
                                cardTitle: "Additional Information",
                                detailsCardTextsFieldData: const [
                                  // DetailsCardModel(
                                  //   keyboardType: TextInputType.number,
                                  //   textEditingController:
                                  //       createRepairOrderController
                                  //           .odometerEditingController,
                                  //   hintText: "Odometer (in KMs)",
                                  //   prefixIcon: Icon(
                                  //     Icons.speed_outlined,
                                  //     size: 24.sp,
                                  //   ),
                                  // ),
                                ],
                                trailing: FuelLevelSlider(
                                  onChange: (double fuelValue) {
                                    createRepairOrderController.fuelLevel =
                                        fuelValue;
                                  },
                                ),
                              ),
                            ),
                            HeightBox(16.h),
                          ],
                          Container(
                            decoration:
                                BoxDecoration(boxShadow: Constants.boxShadow),
                            margin: EdgeInsets.symmetric(horizontal: 18.w),
                            child: ElevatedButton(
                              onPressed: () async {
                                if (submitted) {
                                  return;
                                }
                                submitted = true;
                                try {
                                  if (vehicleOnly) {
                                    context.loaderOverlay.show();
                                    await createRepairOrderController
                                        .addEditVehicleDetailsInDatabase();
                                    if (context.mounted) {
                                      context.loaderOverlay.hide();
                                    }

                                    return;
                                  }
                                  if (createRepairOrderController
                                      .validateFields()) {
                                    if (addCustomerOnly) {
                                      MapEntry<String, CustomerDetailsModel>?
                                          alreadyCustomer =
                                          Get.find<HomeScreenController>()
                                              .customers
                                              .entries
                                              .firstWhereOrNull((data) {
                                        return data.value.phone
                                                ?.toLowerCase() ==
                                            createRepairOrderController
                                                .phoneNumberTextEditingController
                                                .text
                                                .toLowerCase();
                                      });

                                      if (alreadyCustomer != null) {
                                        Utils.showSnackBar(
                                            title: "Customer already existed");

                                        return;
                                      }

                                      await createRepairOrderController
                                          .addNewCustomerDetailsInDatabase();

                                      if (context.mounted)
                                        Navigator.pop(context);
                                    } else {
                                      await createRepairOrderController
                                          .createRepairOrder(context);
                                    }
                                  } else {
                                    Utils.showSnackBar(
                                      title:
                                          "Fields marked * are mandatory, Please fill all mandatory fields.",
                                      // snackBarType: SnackBarType.error,
                                    );
                                  }
                                  submitted = false;
                                } catch (e) {
                                  debugPrint(e.toString());
                                  submitted = false;
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: colorsConstants.primaryRed,
                                foregroundColor: colorsConstants.whiteColor,
                                shape: ContinuousRectangleBorder(
                                  borderRadius: BorderRadius.circular(5.r),
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    vehicleOnly
                                        ? "Save Vehicle"
                                        : addCustomerOnly
                                            ? "Create Customer"
                                            : "Next",
                                  ),
                                ],
                              ),
                            ),
                          ),
                          OverlayPortal(
                            controller: createRepairOrderController
                                .makeOverlayPortalController,
                            overlayChildBuilder: (BuildContext context) {
                              return CustomOverlay(
                                title: "Select Make",
                                onBackPress: () {
                                  createRepairOrderController
                                      .makeOverlayPortalController
                                      .hide();
                                },

                                onSelected: (String selectedValue) {
                                  createRepairOrderController
                                      .makeTextEditingController
                                      .text = selectedValue;
                                  createRepairOrderController
                                      .modelTextEditingController.text = "";
                                  createRepairOrderController
                                      .makeOverlayPortalController
                                      .hide();
                                },
                                dataSource2: createRepairOrderController
                                    .vehiclesDataSource,
                                dataSource: createRepairOrderController
                                    .vehiclesDataSource
                                    .fold(
                                  [],
                                  (previousValue, element) {
                                    previousValue.add(element.company ?? "");

                                    return previousValue;
                                  },
                                ),
                                addNewOnPress: () {
                                  moveToAddMakeModelsScreen(
                                    context: context,
                                    revertCallback: (_) async {
                                      await createRepairOrderController
                                          .fetchVehicles();
                                      createRepairOrderController.update();
                                    },
                                  );
                                },
                                // addNewActionWidget: PrimaryButton(
                                //   onPress: () {
                                //     moveToAddMakeModelsScreen(
                                //       context: context,
                                //       revertCallback: (_) async {
                                //         await createRepairOrderController
                                //             .fetchVehicles();
                                //         createRepairOrderController.update();
                                //       },
                                //     );
                                //     // showDialog(
                                //     //   context: context,
                                //     //   builder: (context) {
                                //     //     return showAddMakeModelDialog(context);
                                //     //   },
                                //     // );
                                //   },
                                //   title: "Add Make",
                                // ),
                              );
                            },
                          ),
                          OverlayPortal(
                            controller: createRepairOrderController
                                .modelOverlayPortalController,
                            overlayChildBuilder: (BuildContext context) {
                              return CustomOverlay(
                                title: "Select Model",
                                onBackPress: () {
                                  createRepairOrderController
                                      .modelOverlayPortalController
                                      .hide();
                                },
                                onSelected: (String selectedValue) {
                                  createRepairOrderController
                                      .modelTextEditingController
                                      .text = selectedValue;
                                  createRepairOrderController
                                      .modelOverlayPortalController
                                      .hide();
                                },
                                dataSource: createRepairOrderController
                                        .vehiclesDataSource
                                        .firstWhereOrNull((make) {
                                      return make.company ==
                                          createRepairOrderController
                                              .makeTextEditingController.text;
                                    })?.models ??
                                    [],
                                addNewOnPress: () {
                                  moveToAddMakeModelsScreen(
                                    context: context,
                                    makeModelsModel: createRepairOrderController
                                        .vehiclesDataSource
                                        .firstWhereOrNull((make) =>
                                            make.company ==
                                            createRepairOrderController
                                                .makeTextEditingController
                                                .text),
                                    revertCallback: (_) async {
                                      await createRepairOrderController
                                          .fetchVehicles();
                                      createRepairOrderController.update();
                                    },
                                  );
                                },
                                // addNewActionWidget: PrimaryButton(
                                //   onPress: () {
                                //     moveToAddMakeModelsScreen(
                                //       context: context,
                                //       makeModelsModel:
                                //           createRepairOrderController
                                //               .vehiclesDataSource
                                //               .firstWhereOrNull((make) =>
                                //                   make.company ==
                                //                   createRepairOrderController
                                //                       .makeTextEditingController
                                //                       .text),
                                //       revertCallback: (_) async {
                                //         await createRepairOrderController
                                //             .fetchVehicles();
                                //         createRepairOrderController.update();
                                //       },
                                //     );
                                //     // showDialog(
                                //     //   context: context,
                                //     //   builder: (context) {
                                //     //     return showAddMakeModelDialog(context,
                                //     //         showAddMake: false);
                                //     //   },
                                //     // );
                                //   },
                                //   title: "Add Model",
                                // ),
                              );
                            },
                          ),
                          OverlayPortal(
                            controller: createRepairOrderController
                                .vehiclesOverlayPortalController,
                            overlayChildBuilder: (BuildContext context) {
                              return CustomOverlay(
                                title: "Select Vehicle",
                                onBackPress: () {
                                  createRepairOrderController
                                      .vehiclesOverlayPortalController
                                      .hide();
                                },
                                onSelected: (String selectedVehicle) {
                                  debugPrint(selectedVehicle);
                                  VehicleDetailsModel? vehicleDetailsModel =
                                      createRepairOrderController
                                          .allCustomersVehicles.entries
                                          .firstWhereOrNull((el) {
                                            return el.key.customerId ==
                                                widget.customerId;
                                          })
                                          ?.value
                                          .firstWhereOrNull((VehicleDetailsModel
                                              vehicleDetails) {
                                            return "${vehicleDetails.make} ${vehicleDetails.model}" ==
                                                selectedVehicle;
                                          });

                                  if (vehicleDetailsModel != null) {
                                    createRepairOrderController
                                        .selectedVehicle = vehicleDetailsModel;
                                    createRepairOrderController
                                            .createRepairOrderModel =
                                        RepairOrderModel(
                                      vehicleDetailsModel: vehicleDetailsModel,
                                    );

                                    createRepairOrderController
                                        .populateVehicleDetails();
                                  }
                                  createRepairOrderController.update();
                                  createRepairOrderController
                                      .vehiclesOverlayPortalController
                                      .hide();
                                },
                                dataSource: createRepairOrderController
                                        .allCustomersVehicles.entries
                                        .firstWhereOrNull((el) {
                                          return el.key.customerId ==
                                              widget.customerId;
                                        })
                                        ?.value
                                        .fold(
                                          [],
                                          (previousValue, element) {
                                            previousValue?.add(
                                                "${element.make} ${element.model}");

                                            return previousValue;
                                          },
                                        ) ??
                                    [],
                              );
                            },
                          ),
                          HeightBox(20.h),
                        ],
                      ),
                    ),
                  ),
                ],
              ],
            ),
          );
        },
      ),
    );
  }

  AlertDialog showAddMakeModelDialog(BuildContext context,
      {bool showAddMake = true}) {
    TextEditingController controller = TextEditingController();

    return AlertDialog(
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            "Add Make",
            style: TextStyle(fontSize: 20.sp),
          ),
          HeightBox(10.h),
          CustomTextField(
            hintText: "Make Name",
            controller: controller,
            textInputAction: TextInputAction.next,
          ),
          HeightBox(20.h),
          PrimaryButton(
              onPress: () async {
                // await addServicePartController
                //     .addNewCategoryInDatabase(
                //   categoryName: controller.text,
                // );

                Navigator.pop(context);
              },
              title: "Add"),
        ],
      ),
    );
  }

  List<Widget> generateSearchedCustomer(
      CreateRepairOrderController createRepairOrderController) {
    List<Widget> widgets = [];

    for (var customerVehicleMap
        in createRepairOrderController.searchedCustomersVehicles.entries) {
      for (var vehicle in customerVehicleMap.value) {
        widgets.add(
          InkWell(
            onTap: () {
              FocusManager.instance.primaryFocus?.unfocus();

              createRepairOrderController.showSearchField = false;

              createRepairOrderController.searchedCustomersVehicles = {};
              createRepairOrderController.existingCustomerSelected = true;
              createRepairOrderController.updateRepairOrderModel(
                  customerDetails: customerVehicleMap.key,
                  vehicleDetails: vehicle);
            },
            child: Container(
              width: double.maxFinite,
              margin: EdgeInsets.symmetric(vertical: 10.h, horizontal: 20.w),
              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
              decoration: BoxDecoration(
                color: colorsConstants.whiteColor,
                boxShadow: Constants.boxShadow,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          "Registration no: ",
                          style: TextStyle(fontSize: 16.sp),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          "${vehicle.registrationNumber}",
                          style: TextStyle(
                            fontSize: 15.sp,
                            fontWeight: FontWeight.bold,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          "Customer name: ",
                          style: TextStyle(fontSize: 16.sp),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          customerVehicleMap.key.username ?? "",
                          style: TextStyle(
                            fontSize: 15.sp,
                            fontWeight: FontWeight.bold,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      }
    }

    return widgets;
  }
}
