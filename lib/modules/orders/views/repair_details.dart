import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get_connect/http/src/utils/utils.dart';
import 'package:speed_force_franchise/models/selected_images_model.dart';
import 'package:speed_force_franchise/modules/orders/controllers/repair_details_controller.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/modules/orders/widgets/custom_overlay.dart';
import 'package:speed_force_franchise/modules/orders/widgets/customer_details_container.dart';
import 'package:speed_force_franchise/modules/orders/widgets/customer_remarks_container.dart';
import 'package:speed_force_franchise/modules/orders/widgets/estimated_delivery_container.dart';
import 'package:speed_force_franchise/modules/orders/widgets/load_media_container.dart';
import 'package:speed_force_franchise/modules/orders/widgets/repair_cost_container.dart';
import 'package:speed_force_franchise/modules/orders/widgets/services_parts_list_view.dart';
import 'package:speed_force_franchise/modules/orders/widgets/tags_container.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';

class RepairDetails extends StatefulWidget {
  const RepairDetails({
    super.key,
    required this.createRepairOrderModel,
    this.isInvoice = false,
  });

  final RepairOrderModel createRepairOrderModel;
  final bool isInvoice;

  @override
  State<RepairDetails> createState() => _RepairDetailsState();
}

class _RepairDetailsState extends State<RepairDetails> {
  bool submitted = false;
  @override
  void initState() {
    super.initState();
    Get.delete<RepairDetailsController>();
    RepairDetailsController repairDetailsController = Get.put(
        RepairDetailsController(
            createRepairOrderModel: widget.createRepairOrderModel));
    repairDetailsController.fetchServices();
    repairDetailsController.fetchParts();
    repairDetailsController.fetchTags();
  }

  @override
  void dispose() {
    Get.delete<RepairDetailsController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<RepairDetailsController>(
      builder: (_) {
        final RepairDetailsController repairDetailsController =
            Get.find<RepairDetailsController>();
        return GestureDetector(
          onTap: () {
            FocusManager.instance.primaryFocus?.unfocus();
          },
          child: Scaffold(
            appBar: AppBar(
              title: const Text("Repair Order"),
              elevation: 0.5,
              backgroundColor: colorsConstants.whiteColor,
              shadowColor: colorsConstants.whiteColor,
              surfaceTintColor: colorsConstants.whiteColor,
            ),
            body: Padding(
              padding: EdgeInsets.zero,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    HeightBox(16.h),
                    CustomerDetailsContainer(
                      repairDetailsModel: widget.createRepairOrderModel,
                    ),
                    HeightBox(16.h),
                    ServicesPartsListView(
                      title: "SERVICES",
                      onAdd: () {
                        repairDetailsController
                            .addServiceOverlayPortalController
                            .show();
                      },
                      servicePartList: repairDetailsController.selectedServices,
                    ),
                    HeightBox(16.h),
                    ServicesPartsListView(
                      title: "PARTS",
                      onAdd: () {
                        repairDetailsController.addPartsOverlayPortalController
                            .show();
                      },
                      servicePartList: repairDetailsController.selectedParts,
                    ),
                    HeightBox(16.h),
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 16.w),
                      padding: EdgeInsets.symmetric(
                        horizontal: 16.w,
                        vertical: 10.h,
                      ),
                      decoration: BoxDecoration(
                        boxShadow: Constants.boxShadow,
                        borderRadius: BorderRadius.circular(5.r),
                        color: Colors.white,
                      ),
                      child: LoadMediaContainer(
                        title: "IMAGES",
                        onMediaLoaded: (SelectedImageModel pickedMedia) {
                          repairDetailsController.repairOrderImages
                              .add(pickedMedia);

                          repairDetailsController.update();
                        },
                        onRemoveMedia: (SelectedImageModel removedMedia) {
                          repairDetailsController.repairOrderImages
                              .remove(removedMedia);
                          repairDetailsController.update();
                        },
                      ),
                    ),
                    HeightBox(16.h),
                    const RepairCostContainer(),
                    HeightBox(16.h),
                    // const TagsContainer(),
                    // HeightBox(16.h),
                    const CustomerRemarksContainer(),
                    HeightBox(16.h),
                    EstimatedDeliveryContainer(),
                    HeightBox(16.h),
                    Container(
                      decoration: BoxDecoration(boxShadow: Constants.boxShadow),
                      margin: EdgeInsets.symmetric(horizontal: 16.w),
                      child: ElevatedButton(
                        onPressed: () {
                          if (submitted) {
                            return;
                          }
                          submitted = true;
                          setState(() {});
                          FocusManager.instance.primaryFocus?.unfocus();

                          if (repairDetailsController
                                  .selectedServices.isEmpty &&
                              repairDetailsController.selectedParts.isEmpty) {
                            Utils.showSnackBar(
                                title:
                                    "Please select atleast 1 Service or Part");
                            submitted = false;
                            setState(() {});
                            return;
                          }
                          repairDetailsController.confirmOrder(
                            context: context,
                            isInvoice: widget.isInvoice,
                          );
                          submitted = false;
                          setState(() {});
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: colorsConstants.primaryRed,
                          foregroundColor: colorsConstants.whiteColor,
                          shape: ContinuousRectangleBorder(
                            borderRadius: BorderRadius.circular(5.r),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            submitted
                                ? CircularProgressIndicator(
                                    color: Colors.white,
                                  )
                                : Text("Continue"),
                          ],
                        ),
                      ),
                    ),
                    HeightBox(40.h),
                    OverlayPortal(
                      controller: repairDetailsController
                          .addServiceOverlayPortalController,
                      overlayChildBuilder: (BuildContext context) {
                        return CustomOverlay(
                          title: "Choose Services",
                          allowMultiSelection: true,
                          onBackPress: () {
                            repairDetailsController
                                .addServiceOverlayPortalController
                                .hide();
                          },
                          onMultiSelectSubmit: (List<String> selectedServices) {
                            repairDetailsController
                                .addServiceOverlayPortalController
                                .hide();
                            repairDetailsController.addServices(
                              servicesNames: selectedServices,
                            );
                          },
                          dataSource: repairDetailsController.services
                              .fold(<String>[], (previousValue, element) {
                            previousValue.add(element.name ?? "");
                            return previousValue;
                          }),
                          addNewOnPress: () {
                            moveToAddServicePartScreen(
                              context: context,
                              isAddingService: true,
                              revertCallback: (_) async {
                                await repairDetailsController.fetchServices();
                                repairDetailsController.update();
                              },
                            );
                          },
                          // addNewActionWidget: PrimaryButton(
                          //   onPress: () {
                          //     moveToAddServicePartScreen(
                          //       context: context,
                          //       isAddingService: true,
                          //       revertCallback: (_) async {
                          //         await repairDetailsController.fetchServices();
                          //         repairDetailsController.update();
                          //       },
                          //     );
                          //   },
                          //   title: "Add New Service",
                          // ),
                        );
                      },
                    ),
                    OverlayPortal(
                      controller: repairDetailsController
                          .addPartsOverlayPortalController,
                      overlayChildBuilder: (BuildContext context) {
                        return CustomOverlay(
                          title: "Choose Parts",
                          allowMultiSelection: true,
                          onBackPress: () {
                            repairDetailsController
                                .addPartsOverlayPortalController
                                .hide();
                          },
                          onMultiSelectSubmit: (List<String> selectedParts) {
                            repairDetailsController
                                .addPartsOverlayPortalController
                                .hide();
                            repairDetailsController.addParts(
                              partsNames: selectedParts,
                            );
                          },
                          dataSource: repairDetailsController.parts
                              .fold(<String>[], (previousValue, element) {
                            previousValue.add(element.name ?? "");
                            return previousValue;
                          }),
                          addNewOnPress: () {
                            moveToAddServicePartScreen(
                              context: context,
                              isAddingService: false,
                              revertCallback: (_) async {
                                await repairDetailsController.fetchParts();
                                repairDetailsController.update();
                              },
                            );
                          },
                          // addNewActionWidget: PrimaryButton(
                          //   onPress: () {
                          //     moveToAddServicePartScreen(
                          //       context: context,
                          //       isAddingService: false,
                          //       revertCallback: (_) async {
                          //         await repairDetailsController.fetchParts();
                          //         repairDetailsController.update();
                          //       },
                          //     );
                          //   },
                          //   title: "Add New Part",
                          // ),
                        );
                      },
                    ),
                    OverlayPortal(
                      controller: repairDetailsController
                          .addTagsOverlayPortalController,
                      overlayChildBuilder: (BuildContext context) {
                        return CustomOverlay(
                          title: "Select Tags",
                          allowMultiSelection: true,
                          onBackPress: () {
                            repairDetailsController
                                .addTagsOverlayPortalController
                                .hide();
                          },
                          onMultiSelectSubmit: (List<String> selectedTags) {
                            repairDetailsController
                                .addTagsOverlayPortalController
                                .hide();
                            repairDetailsController.addTags(
                              tags: selectedTags,
                            );
                          },
                          dataSource: repairDetailsController.tags,
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
