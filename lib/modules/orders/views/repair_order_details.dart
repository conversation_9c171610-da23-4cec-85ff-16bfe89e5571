import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:easy_stepper/easy_stepper.dart';
import 'package:flutter/material.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:speed_force_franchise/modules/accounts/modules/add_expenses/models/expense_model.dart';
import 'package:speed_force_franchise/modules/orders/controllers/repair_order_details_controller.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/service_part.model.dart';
import 'package:speed_force_franchise/modules/orders/widgets/order_status_card.dart';
import 'package:speed_force_franchise/modules/orders/widgets/progress_checklist.dart';
import 'package:speed_force_franchise/modules/orders/widgets/repair_order_invoice_data.dart';
import 'package:speed_force_franchise/modules/parts/modules/add_service_part/views/add_service_part.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

class RepairOrderDetails extends StatefulWidget {
  const RepairOrderDetails({
    super.key,
    required this.orderId,
  });
  final String orderId;

  @override
  State<RepairOrderDetails> createState() => _RepairOrderDetailsState();
}

class _RepairOrderDetailsState extends State<RepairOrderDetails> {
  bool showInvoice = false;
  final GlobalKey _globalKey = GlobalKey();

  @override
  void initState() {
    Get.delete<RepairOrderDetailsController>();
    Get.put(RepairOrderDetailsController()
      ..fetchRepairOrderDetails(orderId: widget.orderId));
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<RepairOrderDetailsController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<RepairOrderDetailsController>(
        builder: (repairOrderDetailsController) {
      int activeStep = OrderStatus.values.indexOf(
          repairOrderDetailsController.repairOrderModel?.orderStatus ??
              OrderStatus.created);
      // print(
      //     "${repairOrderDetailsController.invoice?.repairDetailsModel?.parts?.length}");
      return Scaffold(
        appBar: AppBar(
          title: const Center(child: Text("Details")),
          elevation: 0.5,
          backgroundColor: colorsConstants.whiteColor,
          shadowColor: colorsConstants.whiteColor,
          surfaceTintColor: colorsConstants.whiteColor,
          actions: [
            InkWell(
                onTap: () async {
                  showModalBottomSheet(
                    context: context,
                    scrollControlDisabledMaxHeightRatio: 0.9,
                    builder: (context) {
                      return RepairOrderInvoiceData(
                        globalKey: _globalKey,
                        repairOrderModel:
                            repairOrderDetailsController.repairOrderModel!,
                        invoiceModel: repairOrderDetailsController.invoice,
                      );
                    },
                  );
                },
                child: const Icon(Icons.share)),
            WidthBox(16.w),
          ],
        ),
        body: repairOrderDetailsController.repairOrderModel != null
            ? SingleChildScrollView(
                child: Column(
                  children: [
                    HeightBox(20.h),
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 5.w,
                                      vertical: 5.h,
                                    ),
                                    decoration: BoxDecoration(
                                      color: repairOrderDetailsController
                                          .repairOrderModel?.orderStatus
                                          ?.getColor(),
                                      // boxShadow: Constants.boxShadow,
                                    ),
                                    child: Container(
                                      alignment: Alignment.center,
                                      child: Text(
                                        repairOrderDetailsController
                                                .repairOrderModel
                                                ?.orderStatus
                                                ?.name ??
                                            "",
                                        style: TextStyle(
                                          fontSize: 10.sp,
                                          color: colorsConstants.whiteColor,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                  if (repairOrderDetailsController
                                          .repairOrderModel?.orderStatus !=
                                      OrderStatus.completed) ...[
                                    WidthBox(2.w),
                                    InkWell(
                                      onTap: () {
                                        showDialog(
                                          context: context,
                                          builder: (context) {
                                            return AlertDialog(
                                              insetPadding: EdgeInsets.zero,
                                              titlePadding:
                                                  EdgeInsets.symmetric(
                                                horizontal: 10.w,
                                                vertical: 10.h,
                                              ),
                                              contentPadding:
                                                  EdgeInsets.symmetric(
                                                horizontal: 10.w,
                                              ),
                                              title:
                                                  const Text("Change Status"),
                                              content: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  const Divider(),
                                                  RowWithRadioButton(
                                                    label: OrderStatus
                                                        .created.name,
                                                    isSelected:
                                                        repairOrderDetailsController
                                                                .repairOrderModel
                                                                ?.orderStatus ==
                                                            OrderStatus.created,
                                                    onTap: () async {
                                                      await updateStatusDialog(
                                                        context,
                                                        OrderStatus.created,
                                                      );
                                                      if (context.mounted) {
                                                        Navigator.pop(context);
                                                      }
                                                    },
                                                  ),
                                                  HeightBox(10.h),
                                                  RowWithRadioButton(
                                                    label: OrderStatus
                                                        .workInProgress.name,
                                                    isSelected:
                                                        repairOrderDetailsController
                                                                .repairOrderModel
                                                                ?.orderStatus ==
                                                            OrderStatus
                                                                .workInProgress,
                                                    onTap: () async {
                                                      await updateStatusDialog(
                                                        context,
                                                        OrderStatus
                                                            .workInProgress,
                                                      );
                                                      if (context.mounted) {
                                                        Navigator.pop(context);
                                                      }
                                                    },
                                                  ),
                                                  HeightBox(10.h),
                                                  RowWithRadioButton(
                                                    label:
                                                        OrderStatus.ready.name,
                                                    isSelected:
                                                        repairOrderDetailsController
                                                                .repairOrderModel
                                                                ?.orderStatus ==
                                                            OrderStatus.ready,
                                                    onTap: () async {
                                                      await updateStatusDialog(
                                                        context,
                                                        OrderStatus.ready,
                                                      );
                                                      if (context.mounted) {
                                                        Navigator.pop(context);
                                                      }
                                                    },
                                                  ),
                                                  HeightBox(16.h),
                                                ],
                                              ),
                                            );
                                          },
                                        );
                                      },
                                      child: Icon(
                                        Icons.edit,
                                        size: 20.sp,
                                      ),
                                    )
                                  ],
                                ],
                              ),
                              HeightBox(5.h),
                              Text(
                                DateFormat("d MMM y").format(
                                  DateTime.parse(repairOrderDetailsController
                                          .repairOrderModel?.createdAt ??
                                      ""),
                                ),
                                style: TextStyle(
                                  fontSize: 10.sp,
                                  color: colorsConstants.hintGrey,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          Column(
                            children: [
                              Text(
                                "Invoice",
                                style: TextStyle(fontSize: 13.sp),
                              ),
                              HeightBox(5.h),
                              Text(
                                repairOrderDetailsController
                                        .repairOrderModel?.jobCardId ??
                                    "",
                                style: TextStyle(
                                  fontSize: 9.sp,
                                  color: colorsConstants.hintGrey,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          Column(
                            children: [
                              Text(
                                "Estimated",
                                style: TextStyle(fontSize: 13.sp),
                              ),
                              HeightBox(5.h),
                              Column(
                                children: [
                                  Text(
                                    "Delivery Date",
                                    style: TextStyle(fontSize: 9.sp),
                                  ),
                                  Text(
                                    DateFormat("d MMM y").format(
                                      DateTime.parse(
                                          repairOrderDetailsController
                                                  .repairOrderModel
                                                  ?.repairDetailsModel
                                                  ?.estimatedDelivery ??
                                              ""),
                                    ),
                                    style: TextStyle(
                                        fontSize: 9.sp,
                                        color: colorsConstants.hintGrey),
                                  ),
                                ],
                              )
                            ],
                          ),
                        ],
                      ),
                    ),
                    HeightBox(20.h),
                    EasyStepper(
                      activeStep: activeStep,
                      activeStepTextColor: Colors.black87,
                      finishedStepTextColor: Colors.black87,
                      internalPadding: 16.w,
                      showLoadingAnimation: false,
                      stepRadius: 8,
                      showStepBorder: false,
                      steps: [
                        EasyStep(
                          customStep: CircleAvatar(
                            radius: 8,
                            backgroundColor: Colors.white,
                            child: CircleAvatar(
                              radius: 7,
                              backgroundColor: activeStep >= 0
                                  ? colorsConstants.primaryRed
                                  : colorsConstants.hintGrey,
                            ),
                          ),
                          customTitle: Center(
                            child: Text(
                              "CREATED",
                              style: TextStyle(fontSize: 8.sp),
                            ),
                          ),
                        ),
                        EasyStep(
                          customStep: CircleAvatar(
                            radius: 8,
                            backgroundColor: Colors.white,
                            child: CircleAvatar(
                              radius: 7,
                              backgroundColor: activeStep >= 1
                                  ? colorsConstants.primaryRed
                                  : colorsConstants.hintGrey,
                            ),
                          ),
                          customTitle: Center(
                            child: Text(
                              "IN PROGRESS",
                              style: TextStyle(fontSize: 8.sp),
                            ),
                          ),
                        ),
                        EasyStep(
                          customStep: CircleAvatar(
                            radius: 8,
                            backgroundColor: Colors.white,
                            child: CircleAvatar(
                              radius: 7,
                              backgroundColor: activeStep >= 2
                                  ? colorsConstants.primaryRed
                                  : colorsConstants.hintGrey,
                            ),
                          ),
                          customTitle: Center(
                            child: Text(
                              "VEHICLE READY",
                              style: TextStyle(fontSize: 8.sp),
                            ),
                          ),
                        ),
                        EasyStep(
                          customStep: CircleAvatar(
                            radius: 8,
                            backgroundColor: Colors.white,
                            child: CircleAvatar(
                              radius: 7,
                              backgroundColor: activeStep >= 3
                                  ? colorsConstants.primaryRed
                                  : colorsConstants.hintGrey,
                            ),
                          ),
                          customTitle: Center(
                            child: Text(
                              "PAYMENT DUE",
                              style: TextStyle(fontSize: 8.sp),
                            ),
                          ),
                        ),
                        EasyStep(
                          customStep: CircleAvatar(
                            radius: 8,
                            backgroundColor: Colors.white,
                            child: CircleAvatar(
                              radius: 7,
                              backgroundColor: activeStep >= 4
                                  ? colorsConstants.primaryRed
                                  : colorsConstants.hintGrey,
                            ),
                          ),
                          customTitle: Center(
                            child: Text(
                              "PAYMENT DONE",
                              style: TextStyle(fontSize: 8.sp),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Text(
                      repairOrderDetailsController.repairOrderModel
                              ?.customerDetailsModel?.username ??
                          "",
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      repairOrderDetailsController
                              .repairOrderModel?.customerDetailsModel?.phone ??
                          "",
                    ),
                    HeightBox(10.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "${repairOrderDetailsController.repairOrderModel?.vehicleDetailsModel?.make} ${repairOrderDetailsController.repairOrderModel?.vehicleDetailsModel?.model}",
                        ),
                        Text(
                          " (${repairOrderDetailsController.repairOrderModel?.vehicleDetailsModel?.registrationNumber})",
                          style: TextStyle(color: colorsConstants.hintGrey),
                        ),
                      ],
                    ),
                    HeightBox(10.h),
                    Divider(
                      color: colorsConstants.slateGrey,
                    ),
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 24.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            children: [
                              Text(
                                "Total",
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: colorsConstants.hintGrey,
                                ),
                              ),
                              Text(
                                "${Constants.rupeeSign}${repairOrderDetailsController.repairOrderModel?.repairDetailsModel?.total}",
                                style: TextStyle(
                                  fontSize: 12.sp,
                                ),
                              )
                            ],
                          ),
                          Column(
                            children: [
                              Text(
                                "Received",
                                style: TextStyle(
                                    color: Colors.green, fontSize: 12.sp),
                              ),
                              Text(
                                "${Constants.rupeeSign}${repairOrderDetailsController.repairOrderModel?.repairDetailsModel?.paymentReceived}",
                                style: TextStyle(
                                  fontSize: 12.sp,
                                ),
                              )
                            ],
                          ),
                          Column(
                            children: [
                              Text(
                                "Due",
                                style: TextStyle(
                                    color: Colors.red, fontSize: 12.sp),
                              ),
                              Text(
                                "${Constants.rupeeSign}${repairOrderDetailsController.repairOrderModel?.repairDetailsModel?.paymentDue}",
                                style: TextStyle(
                                  fontSize: 12.sp,
                                ),
                              )
                            ],
                          ),
                          Column(
                            children: [
                              Text(
                                "Discount",
                                style: TextStyle(
                                  color: Colors.red,
                                  fontSize: 12.sp,
                                ),
                              ),
                              Text(
                                "${Constants.rupeeSign}${repairOrderDetailsController.repairOrderModel?.repairDetailsModel?.discount}",
                                style: TextStyle(
                                  fontSize: 12.sp,
                                ),
                              )
                            ],
                          ),
                        ],
                      ),
                    ),
                    Divider(
                      color: colorsConstants.slateGrey,
                    ),
                    HeightBox(10.h),
                    GetBuilder<RepairOrderDetailsController>(
                        id: "Progress List",
                        builder: (_) {
                          return ProgressCheckList(
                            repairOrderDetailsController:
                                repairOrderDetailsController,
                            title: "SERVICES",
                            isEditing:
                                repairOrderDetailsController.isEditingServices,
                            servicesPartsList: repairOrderDetailsController
                                    .repairOrderModel
                                    ?.repairDetailsModel
                                    ?.services ??
                                [],
                            onToggleEditing: repairOrderDetailsController
                                .toggleEditingServices,
                            onSaveProgress:
                                repairOrderDetailsController.updateRepairOrder,
                            onServicePartToggle: (
                                {required String servicePartId,
                                required bool isCompleted}) {
                              repairOrderDetailsController
                                  .toggleServicePartProgress(
                                      servicePartId: servicePartId,
                                      completed: isCompleted);
                            },
                          );
                        }),
                    HeightBox(10.h),
                    GetBuilder<RepairOrderDetailsController>(
                        id: "Progress List",
                        builder: (_) {
                          return ProgressCheckList(
                            repairOrderDetailsController:
                                repairOrderDetailsController,
                            title: "PARTS",
                            isEditing:
                                repairOrderDetailsController.isEditingParts,
                            servicesPartsList: repairOrderDetailsController
                                    .repairOrderModel
                                    ?.repairDetailsModel
                                    ?.parts ??
                                [],
                            onToggleEditing:
                                repairOrderDetailsController.toggleEditingParts,
                            onSaveProgress:
                                repairOrderDetailsController.updateRepairOrder,
                            onServicePartToggle: (
                                {required String servicePartId,
                                required bool isCompleted}) {
                              repairOrderDetailsController
                                  .toggleServicePartProgress(
                                      servicePartId: servicePartId,
                                      completed: isCompleted);
                            },
                          );
                        }),
                    HeightBox(20.h),
                    if (repairOrderDetailsController
                                .repairOrderModel?.orderStatus ==
                            OrderStatus.created ||
                        repairOrderDetailsController
                                .repairOrderModel?.orderStatus ==
                            OrderStatus.workInProgress) ...[
                      OrderStatusCard(
                        currentStatus: repairOrderDetailsController
                                .repairOrderModel?.orderStatus ??
                            OrderStatus.created,
                        onYesTap: (orderStatus) async {
                          if (orderStatus == OrderStatus.ready &&
                              !repairOrderDetailsController
                                  .validateProgressList()) {
                            Utils.showSnackBar(
                                title:
                                    "All items in Service/Parts list must be checked as completed.");
                            return;
                          }

                          await repairOrderDetailsController.updateOrderStatus(
                              updateStatusTo: orderStatus);
                        },
                      ),
                    ],
                    if (repairOrderDetailsController
                            .repairOrderModel?.orderStatus ==
                        OrderStatus.paymentDue) ...[
                      Container(
                        width: double.maxFinite,
                        margin: EdgeInsets.symmetric(horizontal: 16.w),
                        child: PrimaryButton(
                          onPress: () async {
                            PaymentMode selectedPaymentMode = await showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (context) {
                                return selectPaymentModeDialog(context);
                              },
                            );
                            context.loaderOverlay.show();
                            repairOrderDetailsController.repairOrderModel
                                    ?.repairDetailsModel?.paymentReceived =
                                repairOrderDetailsController.repairOrderModel
                                    ?.repairDetailsModel?.total;
                            repairOrderDetailsController.updateOrderStatus(
                              updateStatusTo: OrderStatus.completed,
                              paymentMode: selectedPaymentMode,
                            );
                            double expenseTotal = 0.0;
                            for (ServicePartModel element
                                in repairOrderDetailsController.repairOrderModel
                                        ?.repairDetailsModel?.parts ??
                                    []) {
                              expenseTotal += element.purchasePrice ?? 0;
                            }
                            // repairOrderDetailsController
                            //     .repairOrderModel?.repairDetailsModel?.parts
                            //     ?.map((e) =>
                            //         expenseTotal += e.purchasePrice ?? 0);
                            await FirebaseFirestore.instance
                                .collection(FirebaseCollections.expenses.name)
                                .add(ExpenseModel(
                                        repairOrderId:
                                            repairOrderDetailsController
                                                .repairOrderModel?.orderId,
                                        title: "Parts",
                                        comment: '-',
                                        paymentDate: DateTime.now().toString(),
                                        franchiseId:
                                            repairOrderDetailsController
                                                .repairOrderModel?.franchiseId,
                                        // paymentMode: selectedPaymentMode,
                                        expenseDate:
                                            repairOrderDetailsController
                                                .repairOrderModel?.createdAt,
                                        // expenseMode: ExpenseMode.paid,
                                        totalAmount: expenseTotal)
                                    .toMap());
                            context.loaderOverlay.hide();
                            showDialog(
                              context: context,
                              builder: (context) {
                                return FutureBuilder(
                                  future:
                                      Future.delayed(const Duration(seconds: 1))
                                          .then((value) => true),
                                  builder: (context, snapshot) {
                                    if (snapshot.hasData) {
                                      Navigator.of(context).pop();
                                    }
                                    return AlertDialog(
                                        backgroundColor: Colors.transparent,
                                        title: SizedBox(
                                          height: 100,
                                          width: 100,
                                          child: Image.asset(
                                            'assets/images/active_15263221-1.png',
                                          ),
                                        ));
                                  },
                                );
                              },
                            );
                          },
                          titleWidget: const Text("Complete Order"),
                        ),
                      )
                    ],
                    if (repairOrderDetailsController
                            .repairOrderModel?.orderStatus ==
                        OrderStatus.ready) ...[
                      Container(
                        width: double.maxFinite,
                        margin: EdgeInsets.symmetric(horizontal: 16.w),
                        child: PrimaryButton(
                          onPress: () {
                            print("From Detail");
                            print(repairOrderDetailsController.repairOrderModel
                                ?.toMap()['gstIncluded']);
                            // repairOrderDetailsController.updateOrderStatus(
                            //     updateStatusTo: OrderStatus.paymentDue);
                            moveToCreateInvoiceScreen(
                                context: context,
                                invoiceModel: repairOrderDetailsController
                                    .repairOrderModel,
                                onInvoiceCreate: () async =>
                                    await repairOrderDetailsController
                                        .updateOrderStatus(
                                            updateStatusTo:
                                                OrderStatus.paymentDue));
                          },
                          titleWidget: const Text("Prepare Invoice"),
                        ),
                      )
                    ],
                    HeightBox(40.h),
                  ],
                ),
              )
            : const SizedBox(),
      );
    });
  }

  Future<dynamic> updateStatusDialog(
      BuildContext context, OrderStatus updateOrderStatusTo) {
    RepairOrderDetailsController repairOrderDetailsController =
        Get.find<RepairOrderDetailsController>();

    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog.adaptive(
          content: Column(
            children: [
              Text(
                "Are you sure you want to change order status?",
                style: TextStyle(
                  fontSize: 16.sp,
                  color: colorsConstants.hintGrey,
                ),
              ),
              HeightBox(20.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Text(
                      "NO",
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: colorsConstants.primaryRed,
                      ),
                    ),
                  ),
                  WidthBox(20.w),
                  GestureDetector(
                    onTap: () async {
                      Navigator.pop(context);

                      if (updateOrderStatusTo == OrderStatus.ready &&
                          !repairOrderDetailsController
                              .validateProgressList()) {
                        Utils.showSnackBar(
                            title:
                                "All items in Service/Parts list must be checked as completed.");
                        return;
                      }
                      await repairOrderDetailsController.updateOrderStatus(
                          updateStatusTo: updateOrderStatusTo);
                    },
                    child: Text(
                      "YES",
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: colorsConstants.primaryRed,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget selectPaymentModeDialog(BuildContext context) {
    return AlertDialog(
      contentPadding: EdgeInsets.zero,
      content: ConstrainedBox(
        constraints: BoxConstraints(maxHeight: 300.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            HeightBox(10.h),
            Text(
              "Payment Mode",
              style: TextStyle(fontSize: 24.sp),
            ),
            const Divider(),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: PaymentMode.values.map(
                    (PaymentMode mode) {
                      return Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10.w),
                        child: InkWell(
                          onTap: () {
                            Navigator.pop(context, mode);
                          },
                          child: Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(8.sp),
                                margin: EdgeInsets.symmetric(vertical: 10.sp),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: colorsConstants.primaryRed,
                                    width: 2.sp,
                                  ),
                                ),
                              ),
                              WidthBox(10.w),
                              Text(mode.name),
                            ],
                          ),
                        ),
                      );
                    },
                  ).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
