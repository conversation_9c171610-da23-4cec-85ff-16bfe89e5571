import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/franchise_details.dart';
import 'package:speed_force_franchise/models/stock_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/orders/controllers/repair_details_controller.dart';
import 'package:speed_force_franchise/modules/orders/controllers/repair_order_details_controller.dart';
import 'package:speed_force_franchise/modules/orders/controllers/view_orders_controller.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/service_part.model.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/widgets/small_primary_button.dart';

class ViewOrders extends StatefulWidget {
  const ViewOrders({
    super.key,
    this.orderStatus,
    this.vehicleId,
  });

  final OrderStatus? orderStatus;
  final String? vehicleId;

  @override
  State<ViewOrders> createState() => _ViewOrdersState();
}

class _ViewOrdersState extends State<ViewOrders> {
  @override
  void initState() {
    super.initState();
    Get.put(ViewOrdersController()).fetchOrders(
        viewOrdersBy: widget.orderStatus != null
            ? ViewOrdersBy.orderStatus
            : ViewOrdersBy.vehicleId,
        orderStatus: widget.orderStatus?.name,
        vehicleId: widget.vehicleId);
  }

  @override
  void dispose() {
    Get.delete<ViewOrdersController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ViewOrdersController>(
      builder: (_) {
        ViewOrdersController viewOrdersController =
            Get.find<ViewOrdersController>();
        return Scaffold(
          appBar: AppBar(
            title: const Text("Orders"),
            actions: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: IconButton(
                  onPressed: () async {
                    DateTime minDateTime = DateTime.utc(1985, 04, 20);
                    DateTime maxDateTime = DateTime.utc(275760, 09, 13);
                    DateTimeRange? selectedDateRange =
                        await showDateRangePicker(
                      context: context,
                      firstDate: minDateTime,
                      lastDate: maxDateTime,
                    );

                    if (selectedDateRange != null) {
                      DateTime startDate = selectedDateRange.start;
                      DateTime endDate = selectedDateRange.end;

                      viewOrdersController.filterOrdersByDates(
                          startDate: startDate, endDate: endDate);
                    }
                  },
                  icon: const Icon(Icons.filter_alt),
                ),
              ),
            ],
            elevation: 0.5,
            backgroundColor: colorsConstants.whiteColor,
            shadowColor: colorsConstants.whiteColor,
            surfaceTintColor: colorsConstants.whiteColor,
          ),
          body: Column(
            children: [
              HeightBox(16.h),
              if (widget.orderStatus != null) ...[
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Row(
                    mainAxisAlignment: viewOrdersController.isFiltered
                        ? MainAxisAlignment.spaceBetween
                        : MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 10.w, vertical: 5.h),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5.r),
                          border: Border.all(
                            width: 2.sp,
                            color: widget.orderStatus?.getColor() ??
                                colorsConstants.hintGrey,
                          ),
                        ),
                        child: Text(
                          widget.orderStatus?.name ?? "",
                          style: TextStyle(fontSize: 10.sp),
                        ),
                      ),
                      if (viewOrdersController.isFiltered) ...[
                        SmallPrimaryButton(
                          title: "X Clear Filters",
                          onPress: () {
                            viewOrdersController.clearFilter();
                          },
                        ),
                      ],
                    ],
                  ),
                ),
                HeightBox(16.h),
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 16.w),
                  decoration: BoxDecoration(
                    boxShadow: Constants.boxShadow,
                  ),
                  child: CustomTextField(
                    prefix: Icon(
                      Icons.search_rounded,
                      size: 22.sp,
                    ),
                    controller:
                        viewOrdersController.searchOrderTextEditingController,
                    hintText: "Customer Name/Phone/Reg Number",
                    contentPadding: EdgeInsets.only(right: 10.w),
                    filled: true,
                    fillColor: colorsConstants.whiteColor,
                    onChange: (value) {
                      viewOrdersController.searchOrders(searchTerm: value);
                    },
                    border: const OutlineInputBorder(
                      borderRadius: BorderRadius.all(
                        Radius.circular(5),
                      ),
                      borderSide: BorderSide.none,
                    ),
                    enabledBorder: const OutlineInputBorder(
                      borderRadius: BorderRadius.all(
                        Radius.circular(5),
                      ),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: const OutlineInputBorder(
                      borderRadius: BorderRadius.all(
                        Radius.circular(5),
                      ),
                      borderSide: BorderSide.none,
                    ),
                  ),
                ),
                HeightBox(16.h),
              ],
              if (viewOrdersController.isLoading) ...[
                const Expanded(
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                )
              ],
              if (!viewOrdersController.isLoading)
                if (viewOrdersController.repairOrderModels.isEmpty ||
                    ((viewOrdersController.searchOrderTextEditingController.text
                                .isNotEmpty ||
                            viewOrdersController.isFiltered) &&
                        viewOrdersController
                            .searchedRepairOrderModels.isEmpty)) ...[
                  Expanded(
                      child: Center(
                    child: Text(
                      "No Orders found",
                      style: TextStyle(fontSize: 20.sp),
                    ),
                  ))
                ],
              if (viewOrdersController
                      .searchOrderTextEditingController.text.isNotEmpty ||
                  viewOrdersController.isFiltered) ...[
                if (viewOrdersController
                    .searchedRepairOrderModels.isNotEmpty) ...[
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children:
                            viewOrdersController.searchedRepairOrderModels.map(
                          (RepairOrderModel openOrder) {
                            return Container(
                              margin: EdgeInsets.symmetric(vertical: 10.h),
                              child: OrderCard(
                                repairOrder: openOrder,
                                franchiseDetails:
                                    Get.find<HomeScreenController>()
                                            .franchiseDetails ??
                                        FranchiseDetails(),
                                onRevertCallBack: () async {
                                  await viewOrdersController.fetchOrders(
                                      viewOrdersBy: widget.orderStatus != null
                                          ? ViewOrdersBy.orderStatus
                                          : ViewOrdersBy.vehicleId,
                                      orderStatus: widget.orderStatus?.name,
                                      vehicleId: widget.vehicleId);
                                },
                              ),
                            );
                          },
                        ).toList(),
                      ),
                    ),
                  ),
                  HeightBox(20.h),
                ],
              ],
              if ((viewOrdersController
                          .searchOrderTextEditingController.text.isEmpty &&
                      !viewOrdersController.isFiltered) &&
                  viewOrdersController.repairOrderModels.isNotEmpty) ...[
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: viewOrdersController.repairOrderModels.map(
                        (RepairOrderModel openOrder) {
                          return Container(
                            margin: EdgeInsets.symmetric(vertical: 10.h),
                            child: OrderCard(
                              repairOrder: openOrder,
                              franchiseDetails: Get.find<HomeScreenController>()
                                      .franchiseDetails ??
                                  FranchiseDetails(),
                              onRevertCallBack: () async {
                                await viewOrdersController.fetchOrders(
                                    viewOrdersBy: widget.orderStatus != null
                                        ? ViewOrdersBy.orderStatus
                                        : ViewOrdersBy.vehicleId,
                                    orderStatus: widget.orderStatus?.name,
                                    vehicleId: widget.vehicleId);
                              },
                            ),
                          );
                        },
                      ).toList(),
                    ),
                  ),
                ),
                HeightBox(20.h),
              ]
            ],
          ),
        );
      },
    );
  }
}

class OrderCard extends StatefulWidget {
  const OrderCard({
    super.key,
    required this.repairOrder,
    required this.franchiseDetails,
    this.onRevertCallBack,
  });

  final RepairOrderModel repairOrder;
  final FranchiseDetails franchiseDetails;
  final void Function()? onRevertCallBack;

  @override
  State<OrderCard> createState() => _OrderCardState();
}

class _OrderCardState extends State<OrderCard> {
  bool partsInUsed = false;
  deleteOrder() async {
    for (ServicePartModel part
        in widget.repairOrder.repairDetailsModel?.parts ?? []) {
      QuerySnapshot<Map<String, dynamic>> partsStockCollection =
          await FirebaseFirestore.instance
              .collection(FirebaseCollections.stock.name)
              .where("partId", isEqualTo: part.id)
              .get();

      for (var stockDoc in partsStockCollection.docs) {
        StockModel stockModel = StockModel.fromMap(stockDoc.data());
        if (part.quantity != null) {
          print("-----dw-d------${part.quantity}");
          stockModel.currentStock =
              stockModel.currentStock! + part.quantity!.toDouble() ?? 0.0;
        }
        print(stockModel.toMap());

        await FirebaseFirestore.instance
            .collection(FirebaseCollections.stock.name)
            .doc(stockModel.stockId)
            .update(stockModel.toMap());
      }
    }
    await FirebaseFirestore.instance
        .collection(FirebaseCollections.orders.name)
        .doc(widget.repairOrder.orderId)
        .delete();
    widget.onRevertCallBack!();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    Get.put(RepairOrderDetailsController()
      ..fetchRepairOrderDetails(orderId: widget.repairOrder.orderId ?? ""));
  }

  @override
  Widget build(BuildContext context) {
    partsInUsed = widget.repairOrder.repairDetailsModel?.parts
                ?.firstWhereOrNull((e) => e.completed ?? false) ==
            null
        ? false
        : true;

    return InkWell(
      onTap: () {
        moveToRepairOrderDetailsScreen(
          context: context,
          orderId: widget.repairOrder.orderId ?? "",
          revertCallback: (p0) {
            widget.onRevertCallBack?.call();
          },
        );
      },
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w),
        decoration: BoxDecoration(
          boxShadow: Constants.boxShadow,
          color: colorsConstants.whiteColor,
        ),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 5.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        if ((widget.repairOrder.orderStatus) ==
                            OrderStatus.paymentDue)
                          SizedBox(
                            height: 20,
                          ),
                        Text(
                          "${Constants.rupeeSign} ${widget.repairOrder.repairDetailsModel?.total?.toString()}",
                          style: TextStyle(
                            fontSize: 18.sp,
                            color: const Color.fromARGB(159, 0, 68, 255),
                          ),
                        ),
                        HeightBox(10.h),
                        if (widget.repairOrder.orderStatus !=
                            OrderStatus.completed) ...[
                          if ((widget.repairOrder.orderStatus) !=
                              OrderStatus.paymentDue)
                            Text(
                              "Due: ${Constants.rupeeSign} ${widget.repairOrder.repairDetailsModel?.paymentDue?.toString()}",
                              style: TextStyle(
                                  fontSize: 12.sp,
                                  color: colorsConstants.primaryRed),
                            ),
                          HeightBox(15.h),
                        ],
                        if (widget.repairOrder.orderStatus ==
                            OrderStatus.completed) ...[
                          Text(
                            "Paid: ${Constants.rupeeSign} ${widget.repairOrder.repairDetailsModel?.paymentReceived?.toString()}",
                            style:
                                TextStyle(fontSize: 12.sp, color: Colors.green),
                          ),
                          HeightBox(15.h),
                        ],
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 5.w,
                            vertical: 10.h,
                          ),
                          decoration: BoxDecoration(
                            color: widget.repairOrder.orderStatus?.getColor(),
                            boxShadow: Constants.boxShadow,
                          ),
                          child: Container(
                            width: double.maxFinite,
                            alignment: Alignment.center,
                            child: Text(
                              widget.repairOrder.orderStatus?.name ?? "",
                              style: TextStyle(
                                fontSize: 10.sp,
                                color: colorsConstants.whiteColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                WidthBox(10.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.repairOrder.customerDetailsModel?.username ?? "",
                      ),
                      HeightBox(10.h),
                      Text(
                        widget.repairOrder.customerDetailsModel?.phone ?? "",
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: colorsConstants.hintGrey,
                        ),
                      ),
                      HeightBox(10.h),
                      Text(
                        "${widget.repairOrder.vehicleDetailsModel?.make ?? ""} ${widget.repairOrder.vehicleDetailsModel?.model ?? ""}",
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: colorsConstants.hintGrey,
                        ),
                      ),
                      HeightBox(10.h),
                      Text(
                        "Odo Meter: ${widget.repairOrder.additionalInformationModel?.odometer ?? ""} KMs",
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: colorsConstants.hintGrey,
                        ),
                      ),
                      HeightBox(10.h),
                      Text(
                        widget.repairOrder.vehicleDetailsModel
                                ?.registrationNumber ??
                            "",
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: colorsConstants.hintGrey,
                        ),
                      ),
                      HeightBox(5.h),
                    ],
                  ),
                ),
                if (widget.repairOrder.orderStatus == OrderStatus.created ||
                    ((widget.repairOrder.orderStatus ==
                            OrderStatus.workInProgress) &&
                        !partsInUsed))
                  IconButton(
                      onPressed: () async {
                        bool loggingOut = false;
                        showDialog(
                          context: context,
                          builder: (context) {
                            return StatefulBuilder(
                                builder: (context, setState2) {
                              return AlertDialog(
                                backgroundColor: Colors.white,
                                surfaceTintColor: Colors.white,
                                title: const Text(
                                  "Delete",
                                  style: TextStyle(
                                      fontSize: 25, color: Colors.black),
                                ),
                                content: const Text(
                                  'Are you sure you want to delete?',
                                  style: TextStyle(
                                      fontSize: 15, color: Colors.black),
                                ),
                                // actionsPadding: EdgeInsets.all(0),
                                actionsAlignment: MainAxisAlignment.center,
                                actions: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      loggingOut
                                          ? SizedBox(
                                              height: 40,
                                              width: 40,
                                              child:
                                                  CircularProgressIndicator())
                                          : ElevatedButton(
                                              onPressed: () async {
                                                loggingOut = true;
                                                setState2(() {});
                                                await deleteOrder();
                                                Navigator.of(context).pop();
                                                loggingOut = false;
                                                setState2(() {});
                                              },
                                              style: ElevatedButton.styleFrom(
                                                  elevation: 0,
                                                  foregroundColor: Colors.white,
                                                  backgroundColor:
                                                      Colors.redAccent),
                                              child: const Text(
                                                "Yes",
                                                style: TextStyle(fontSize: 16),
                                              )),
                                      const SizedBox(width: 10),
                                      ElevatedButton(
                                          onPressed: () {
                                            Navigator.of(context).pop();
                                          },
                                          style: ElevatedButton.styleFrom(
                                              elevation: 0,
                                              foregroundColor: Colors.white,
                                              backgroundColor: Colors.green),
                                          child: const Text(
                                            "No",
                                            style: TextStyle(fontSize: 16),
                                          )),
                                    ],
                                  ),
                                ],
                              );
                            });
                          },
                        );
                      },
                      icon: Icon(
                        CupertinoIcons.delete,
                        size: 20,
                        color: Colors.red,
                      ))
              ],
            ),
            Container(
              padding: EdgeInsets.symmetric(vertical: 10.h),
              decoration: BoxDecoration(
                  color: colorsConstants.successGreen.withOpacity(0.1)),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          "Invoice",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: colorsConstants.hintGrey,
                          ),
                        ),
                        Text(
                          widget.repairOrder.jobCardId ?? "",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: colorsConstants.hintGrey,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          "Created By",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: colorsConstants.hintGrey,
                          ),
                        ),
                        Text(
                          widget.franchiseDetails.ownerName ?? "",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: colorsConstants.hintGrey,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          "Completion Date",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: colorsConstants.hintGrey,
                          ),
                        ),
                        Text(
                          widget.repairOrder.completionDate != null
                              ? DateFormat("MMM d, y").format(
                                  widget.repairOrder.completionDate?.toDate() ??
                                      DateTime.now())
                              : "-",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: colorsConstants.hintGrey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
