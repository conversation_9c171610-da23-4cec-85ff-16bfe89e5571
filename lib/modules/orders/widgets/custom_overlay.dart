import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/add_make_models/models/make_models_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/orders/controllers/create_repair_order_controller.dart';
import 'package:speed_force_franchise/modules/orders/controllers/repair_details_controller.dart';
import 'package:speed_force_franchise/modules/services/controllers/services_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

class CustomOverlay extends StatefulWidget {
  CustomOverlay({
    super.key,
    this.title,
    required this.dataSource,
    this.onBackPress,
    this.onSelected,
    this.allowMultiSelection,
    this.onMultiSelectSubmit,
    // this.addNewActionWidget,
    this.preSelectedValues,
    this.addNewOnPress,
    this.dataSource2,
  });
  final String? title;
  List<String> dataSource = [];
  final List<MakeModelsModel>? dataSource2;
  final List<String>? preSelectedValues;

  final void Function()? onBackPress;
  final void Function(String)? onSelected;
  final void Function(List<String>)? onMultiSelectSubmit;
  // final Widget? addNewActionWidget;
  final void Function()? addNewOnPress;

  final bool? allowMultiSelection;

  @override
  State<CustomOverlay> createState() => _CustomOverlayState();
}

class _CustomOverlayState extends State<CustomOverlay> {
  List<String> selectedData = [];

  List<String> searchedData = [];

  TextEditingController searchTextEditingController = TextEditingController();
  bool loading = false;

  @override
  void initState() {
    super.initState();
    if (widget.allowMultiSelection != null &&
        widget.allowMultiSelection! &&
        widget.preSelectedValues != null) {
      for (String preSelectedValue in widget.preSelectedValues ?? []) {
        String? preSelectedData = widget.dataSource.firstWhereOrNull((value) {
          return value.toLowerCase() == preSelectedValue.toLowerCase();
        });
        if (preSelectedData != null) {
          selectedData.add(preSelectedData);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        margin: EdgeInsets.only(top: MediaQuery.paddingOf(context).top + 28),
        child: Scaffold(
          appBar: AppBar(
            title: Text(widget.title ?? ""),
            elevation: 1,
            backgroundColor: colorsConstants.whiteColor,
            shadowColor: colorsConstants.whiteColor,
            surfaceTintColor: colorsConstants.whiteColor,
            automaticallyImplyLeading: false,
            leading: IconButton(
              onPressed: () {
                widget.onBackPress?.call();
              },
              icon: const Icon(Icons.arrow_back_ios_new_rounded),
            ),
            actions: widget.addNewOnPress != null
                ? [
                    GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        widget.addNewOnPress?.call();
                      },
                      child: Container(
                        decoration: BoxDecoration(
                            color: colorsConstants.primaryRed,
                            boxShadow: Constants.boxShadow,
                            borderRadius: BorderRadius.circular(5.r)),
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 2.h,
                        ),
                        child: Icon(
                          Icons.add,
                          color: colorsConstants.whiteColor,
                        ),
                      ),
                    ),
                    WidthBox(20.w),
                  ]
                : null,
            // actions:
            // widget.allowMultiSelection != null &&
            //         widget.allowMultiSelection == true
            //     ? [
            //         InkWell(
            //           onTap: () {
            //             widget.onMultiSelectSubmit?.call(selectedData);
            //           },
            //           child: Container(
            //             padding: EdgeInsets.symmetric(horizontal: 5.w),
            //             decoration: BoxDecoration(
            //               border: Border.all(
            //                 color: colorsConstants.primaryRed,
            //                 width: 1.5.sp,
            //               ),
            //             ),
            //             child: Row(
            //               children: [
            //                 Text(
            //                   selectedData.length.toString(),
            //                   style: TextStyle(
            //                     fontSize: 20.sp,
            //                     color: colorsConstants.primaryRed,
            //                   ),
            //                 ),
            //                 WidthBox(2.w),
            //                 Icon(
            //                   Icons.arrow_forward,
            //                   color: colorsConstants.primaryRed,
            //                 ),
            //               ],
            //             ),
            //           ),
            //         ),
            //         WidthBox(10.w),
            //       ]
            //     : null,
          ),
          body: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              children: [
                HeightBox(10.h),
                Container(
                  decoration: BoxDecoration(boxShadow: Constants.boxShadow),
                  child: CustomTextField(
                    controller: searchTextEditingController,
                    prefix: Icon(
                      Icons.search_rounded,
                      size: 22.sp,
                    ),
                    // hintText: "Name/jobcard/phone/email",
                    onChange: (String searchedValue) {
                      if (searchedValue.isNotEmpty) {
                        searchedData =
                            widget.dataSource.where((String element) {
                          return element
                              .toLowerCase()
                              .contains(searchedValue.toLowerCase());
                        }).toList();
                      } else {
                        searchedData = [];
                      }
                      setState(() {});
                    },
                    hintText: "Search here...",
                    contentPadding: EdgeInsets.zero,
                    filled: true,
                    fillColor: colorsConstants.whiteColor,
                    textInputAction: TextInputAction.done,
                    border: const OutlineInputBorder(
                      borderRadius: BorderRadius.all(
                        Radius.circular(5),
                      ),
                      borderSide: BorderSide.none,
                    ),
                    enabledBorder: const OutlineInputBorder(
                      borderRadius: BorderRadius.all(
                        Radius.circular(5),
                      ),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: const OutlineInputBorder(
                      borderRadius: BorderRadius.all(
                        Radius.circular(5),
                      ),
                      borderSide: BorderSide.none,
                    ),
                  ),
                ),
                HeightBox(10.h),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        HeightBox(5.h),
                        if (searchTextEditingController.text.isNotEmpty &&
                            searchedData.isNotEmpty) ...[
                          ...searchedData.map((String element) {
                            return InkWell(
                              onTap: () {
                                FocusManager.instance.primaryFocus?.unfocus();
                                widget.onSelected?.call(element);
                              },
                              child: Row(
                                children: [
                                  if (widget.allowMultiSelection != null &&
                                      widget.allowMultiSelection!) ...[
                                    Expanded(
                                      child: CheckboxListTile(
                                        value: selectedData.contains(element),
                                        onChanged: (bool? isSelected) {
                                          if (isSelected ?? false) {
                                            selectedData.add(element);
                                          } else {
                                            selectedData.remove(element);
                                          }
                                          setState(() {});
                                        },
                                        title: Container(
                                          padding: EdgeInsets.symmetric(
                                              vertical: 5.h),
                                          width: double.maxFinite,
                                          decoration: BoxDecoration(
                                            border: Border(
                                              bottom: BorderSide(
                                                color:
                                                    colorsConstants.slateGrey,
                                                width: 1.5.sp,
                                              ),
                                            ),
                                          ),
                                          child: Text(
                                            element,
                                            style: TextStyle(
                                              fontSize: 20.sp,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                  if (widget.allowMultiSelection == null ||
                                      widget.allowMultiSelection == false) ...[
                                    Expanded(
                                      child: Container(
                                        padding:
                                            EdgeInsets.symmetric(vertical: 5.h),
                                        width: double.maxFinite,
                                        decoration: BoxDecoration(
                                          border: Border(
                                            bottom: BorderSide(
                                              color: colorsConstants.slateGrey,
                                              width: 1.5.sp,
                                            ),
                                          ),
                                        ),
                                        child: Text(
                                          element,
                                          style: TextStyle(
                                            fontSize: 20.sp,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ]
                                ],
                              ),
                            );
                          }),
                        ],
                        if (searchTextEditingController.text.isEmpty &&
                            widget.dataSource.isNotEmpty) ...[
                          ...List.generate(widget.dataSource.length, (index) {
                            return InkWell(
                              // onLongPress: () {
                              //   print("object");
                              // },
                              onTap: () {
                                FocusManager.instance.primaryFocus?.unfocus();
                                widget.onSelected
                                    ?.call(widget.dataSource[index]);
                              },
                              child: Row(
                                children: [
                                  if (widget.allowMultiSelection != null &&
                                      widget.allowMultiSelection!) ...[
                                    Expanded(
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: CheckboxListTile(
                                              contentPadding:
                                                  EdgeInsets.only(left: 5.h),
                                              value: selectedData.contains(
                                                  widget.dataSource[index]),
                                              onChanged: (bool? isSelected) {
                                                if (isSelected ?? false) {
                                                  selectedData.add(
                                                      widget.dataSource[index]);
                                                } else {
                                                  selectedData.remove(
                                                      widget.dataSource[index]);
                                                }
                                                setState(() {});
                                              },
                                              title: Container(
                                                padding:
                                                    EdgeInsets.only(left: 5.h),
                                                width: double.maxFinite,
                                                decoration: BoxDecoration(
                                                  border: Border(
                                                    bottom: BorderSide(
                                                      color: colorsConstants
                                                          .slateGrey,
                                                      width: 1.5.sp,
                                                    ),
                                                  ),
                                                ),
                                                child: Text(
                                                  widget.dataSource[index],
                                                  style: TextStyle(
                                                    fontSize: 20.sp,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          IconButton(
                                              padding: const EdgeInsets.all(0),
                                              onPressed: () async {
                                                await onDelete(
                                                    widget.title ==
                                                            "Choose Services"
                                                        ? true
                                                        : false,
                                                    widget.dataSource[index],
                                                    index);
                                              },
                                              icon: const Icon(
                                                  CupertinoIcons.delete)),
                                        ],
                                      ),
                                    ),
                                  ],
                                  if (widget.allowMultiSelection == null ||
                                      widget.allowMultiSelection == false) ...[
                                    Expanded(
                                      child: Container(
                                        padding:
                                            EdgeInsets.symmetric(vertical: 5.h),
                                        width: double.maxFinite,
                                        decoration: BoxDecoration(
                                          border: Border(
                                            bottom: BorderSide(
                                              color: colorsConstants.slateGrey,
                                              width: 1.5.sp,
                                            ),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              widget.dataSource[index],
                                              style: TextStyle(
                                                fontSize: 20.sp,
                                              ),
                                            ),
                                            if ((widget.title ==
                                                    "Select Make") &&
                                                ((widget.dataSource2?[index]
                                                        .franchiseId) !=
                                                    null))
                                              IconButton(
                                                onPressed: () async {
                                                  showDialog(
                                                    context: context,
                                                    builder: (context) {
                                                      return StatefulBuilder(
                                                          builder: (context,
                                                              setState2) {
                                                        return AlertDialog(
                                                          title: const Text(
                                                            "Delete!!",
                                                            style: TextStyle(
                                                                color: Color
                                                                    .fromARGB(
                                                                        255,
                                                                        195,
                                                                        51,
                                                                        41)),
                                                          ),
                                                          content: const Text(
                                                              "Are you sure you want to delete?"),
                                                          actions: loading
                                                              ? [
                                                                  const SizedBox(
                                                                      height:
                                                                          25,
                                                                      width: 25,
                                                                      child:
                                                                          CircularProgressIndicator(
                                                                        strokeWidth:
                                                                            3,
                                                                      ))
                                                                ]
                                                              : [
                                                                  TextButton(
                                                                      onPressed:
                                                                          () async {
                                                                        loading =
                                                                            true;
                                                                        setState2(
                                                                            () {});
                                                                        if (widget.title ==
                                                                            "Select Make") {
                                                                          final id = widget
                                                                              .dataSource2
                                                                              ?.firstWhereOrNull((e) => e.company == widget.dataSource[index])
                                                                              ?.makeModelId;
                                                                          if (id !=
                                                                              null) {
                                                                            print(id);
                                                                            await FirebaseFirestore.instance.collection(FirebaseCollections.vehiclesCompanies.name).doc(id).delete();
                                                                            await Get.put(CreateRepairOrderController()).fetchVehicles();
                                                                            Get.put(CreateRepairOrderController()).update();
                                                                            setState(() {});

                                                                            loading =
                                                                                false;
                                                                            setState2(() {});
                                                                            Navigator.pop(context);
                                                                          }
                                                                        }
                                                                      },
                                                                      child: const Text(
                                                                          'Yes')),
                                                                  TextButton(
                                                                      onPressed:
                                                                          () async {
                                                                        Navigator.pop(
                                                                            context);
                                                                      },
                                                                      child: const Text(
                                                                          'No'))
                                                                ],
                                                        );
                                                      });
                                                    },
                                                  );
                                                },
                                                icon: const Icon(
                                                    CupertinoIcons.delete),
                                                iconSize: 20,
                                              )
                                          ],
                                        ),
                                      ),
                                    ),
                                  ]
                                ],
                              ),
                            );
                          }),
                        ],
                        if ((searchTextEditingController.text.isNotEmpty &&
                                searchedData.isEmpty) ||
                            widget.dataSource.isEmpty) ...[
                          SizedBox(
                            height:
                                (MediaQuery.of(context).size.height / 1.8).sp,
                            child: Center(
                              child: Text(
                                "No data found",
                                style: TextStyle(fontSize: 20.sp),
                              ),
                            ),
                          )
                        ],
                      ],
                    ),
                  ),
                ),
                if (widget.allowMultiSelection != null) ...[
                  // Container(
                  //   padding: EdgeInsets.symmetric(vertical: 20.h),
                  //   width: double.maxFinite,
                  //   child: widget.trailingWidget!,
                  // ),
                  Container(
                    width: double.maxFinite,
                    margin: EdgeInsets.symmetric(horizontal: 16.w),
                    child: PrimaryButton(
                      onPress: () {
                        widget.onMultiSelectSubmit?.call(selectedData);
                      },
                      title: "Comfirm",
                    ),
                  ),
                  HeightBox(20.h),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  onDelete(bool fromService, String data, int index) async {
    bool loggingOut = false;

    await showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            title: const Text(
              "Delete",
              style: TextStyle(fontSize: 25, color: Colors.black),
            ),
            content: const Text(
              'Are you sure you want to delete?',
              style: TextStyle(fontSize: 15, color: Colors.black),
            ),
            // actionsPadding: EdgeInsets.all(0),
            actionsAlignment: MainAxisAlignment.center,
            actions: [
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  loggingOut
                      ? const SizedBox(
                          height: 40,
                          width: 40,
                          child: CircularProgressIndicator())
                      : ElevatedButton(
                          onPressed: () async {
                            if (loggingOut) {
                              return;
                            }
                            loggingOut = true;
                            setState2(() {});

                            if (fromService) {
                              final docId = Get.find<RepairDetailsController>()
                                  .services
                                  .firstWhereOrNull(
                                      (element) => element.name == data)
                                  ?.serviceId;
                              if (docId != null) {
                                await FirebaseFirestore.instance
                                    .collection(
                                        FirebaseCollections.services.name)
                                    .doc(docId)
                                    .delete();
                                widget.dataSource.removeWhere((element) =>
                                    element == widget.dataSource[index]);
                                await Get.find<RepairDetailsController>()
                                    .fetchServices();
                                setState(() {});
                              }
                            } else {
                              final part = Get.find<RepairDetailsController>()
                                  .parts
                                  .firstWhereOrNull(
                                      (element) => element.name == data);
                              if (part != null) {
                                final id = await FirebaseFirestore.instance
                                    .collection(FirebaseCollections.stock.name)
                                    .where('partId', isEqualTo: part.partId)
                                    .get();

                                if (id.docs.isNotEmpty) {
                                  await FirebaseFirestore.instance
                                      .collection(
                                          FirebaseCollections.stock.name)
                                      .doc(id.docs.first.id)
                                      .delete();
                                }
                                await FirebaseFirestore.instance
                                    .collection(FirebaseCollections.parts.name)
                                    .doc(part.partId)
                                    .delete();
                                widget.dataSource.removeWhere((element) =>
                                    element == widget.dataSource[index]);
                                await Get.find<RepairDetailsController>()
                                    .fetchParts();
                                setState(() {});
                              }
                            }
                            Navigator.of(context).pop();
                            loggingOut = false;
                            setState2(() {});
                          },
                          style: ElevatedButton.styleFrom(
                              elevation: 0,
                              foregroundColor: Colors.white,
                              backgroundColor: Colors.redAccent),
                          child: const Text(
                            "Yes",
                            style: TextStyle(fontSize: 16),
                          )),
                  const SizedBox(width: 10),
                  ElevatedButton(
                      onPressed: () {
                        if (loggingOut) {
                          return;
                        }
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                          elevation: 0,
                          foregroundColor: Colors.white,
                          backgroundColor: Colors.green),
                      child: const Text(
                        "No",
                        style: TextStyle(fontSize: 16),
                      )),
                ],
              ),
            ],
          );
        });
      },
    );
  }
}
