import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:speed_force_franchise/modules/orders/controllers/create_repair_order_controller.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/modules/orders/models/details_card_model.dart';
import 'package:speed_force_franchise/modules/orders/widgets/details_card.dart';
import 'package:speed_force_franchise/modules/orders/widgets/insurance_expiry_date_container.dart';
import 'package:speed_force_franchise/modules/orders/widgets/insurance_provider_dialog.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

import '../../../utils/common_exports.dart';

class CustomerDetailsContainer extends StatefulWidget {
  const CustomerDetailsContainer({
    super.key,
    required this.repairDetailsModel,
  });

  final RepairOrderModel repairDetailsModel;

  @override
  State<CustomerDetailsContainer> createState() =>
      _CustomerDetailsContainerState();
}

class _CustomerDetailsContainerState extends State<CustomerDetailsContainer> {
  TextEditingController userNameTextEditingController = TextEditingController();
  TextEditingController phoneNumberTextEditingController =
      TextEditingController();
  TextEditingController emailTextEditingController = TextEditingController();
  TextEditingController addressTextEditingController = TextEditingController();
  TextEditingController gstinTextEditingController = TextEditingController();

  TextEditingController odometerEditingController = TextEditingController();

  TextEditingController makeTextEditingController = TextEditingController();
  TextEditingController modelTextEditingController = TextEditingController();
  TextEditingController purchaseDateEditingController = TextEditingController();
  DateFormat purchaseDateFormat = DateFormat("MMMM-y");

  TextEditingController engineTextEditingController = TextEditingController();
  TextEditingController vinTextEditingController = TextEditingController();
  TextEditingController insuranceProviderEditingController =
      TextEditingController();
  TextEditingController insurerGSTINEditingController = TextEditingController();
  TextEditingController insurerAddressTextEditingController =
      TextEditingController();
  TextEditingController policyNumberEditingController = TextEditingController();

  TextEditingController insuranceExpiryDateEditingController =
      TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: 10.h,
      ),
      decoration: BoxDecoration(
        boxShadow: Constants.boxShadow,
        borderRadius: BorderRadius.circular(5.r),
        color: Colors.white,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                customerDetailsRow(
                  details: widget
                          .repairDetailsModel.customerDetailsModel?.username ??
                      "",
                  onEditPress: () {
                    showDialog(
                      context: context,
                      builder: (context) {
                        return editCustomerDetailsDialog(context);
                      },
                    );
                  },
                ),
                HeightBox(10.h),
                customerDetailsRow(
                    details:
                        widget.repairDetailsModel.customerDetailsModel?.phone ??
                            "",
                    detailColor: colorsConstants.hintGrey),
                HeightBox(10.h),
                customerDetailsRow(
                    details:
                        widget.repairDetailsModel.customerDetailsModel?.email ??
                            "",
                    detailColor: colorsConstants.hintGrey),
              ],
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                customerDetailsRow(
                  details:
                      widget.repairDetailsModel.vehicleDetailsModel?.model ??
                          "",
                  onEditPress: () {
                    showDialog(
                      context: context,
                      builder: (context) {
                        return editVehicleDetailsDialog(context);
                      },
                    );
                  },
                ),
                HeightBox(10.h),
                customerDetailsRow(
                  details: widget.repairDetailsModel.vehicleDetailsModel
                          ?.registrationNumber ??
                      "",
                  detailColor: colorsConstants.hintGrey,
                ),
                HeightBox(10.h),
                customerDetailsRow(
                  details:
                      "Odometer: ${widget.repairDetailsModel.additionalInformationModel?.odometer ?? ""} KMs",
                  onEditPress: () {
                    showDialog(
                      context: context,
                      builder: (context) {
                        return editAditionalInformationDialog(context);
                      },
                    );
                  },
                  detailColor: colorsConstants.hintGrey,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget customerDetailsRow(
      {required String details,
      Color? detailColor,
      void Function()? onEditPress}) {
    if (details.isEmpty) {
      return const SizedBox.shrink();
    }
    return Row(
      children: [
        Expanded(
          child: Text(
            details,
            style: TextStyle(
              fontSize: 12.sp,
              color: detailColor ?? colorsConstants.blackColor.withOpacity(0.8),
              overflow: TextOverflow.ellipsis,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
        ),
        // onEditPress != null
        //     ? IconButton(
        //         onPressed: onEditPress,
        //         icon: Icon(
        //           Icons.edit,
        //           size: 16.sp,
        //         ),
        //       )
        //     : const SizedBox()
      ],
    );
  }

  AlertDialog editCustomerDetailsDialog(BuildContext context) {
    return AlertDialog(
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          DetailsCard(
            cardTitle: "Personal Details",
            detailsCardTextsFieldData: [
              DetailsCardModel(
                hintText: "User Name*",
                textEditingController: userNameTextEditingController
                  ..text = widget
                          .repairDetailsModel.customerDetailsModel?.username ??
                      "",
                prefixIcon: Icon(
                  CupertinoIcons.person_alt,
                  size: 24.sp,
                ),
              ),
              DetailsCardModel(
                hintText: "Phone Number*",
                keyboardType: TextInputType.phone,
                inputFormatters: [
                  LengthLimitingTextInputFormatter(10),
                ],
                textEditingController: phoneNumberTextEditingController
                  ..text =
                      widget.repairDetailsModel.customerDetailsModel?.phone ??
                          "",
                prefixIcon: Icon(
                  CupertinoIcons.phone_fill,
                  size: 24.sp,
                ),
                suffixIcon: IconButton(
                  icon: Icon(
                    Icons.contact_phone_outlined,
                    size: 24.sp,
                  ),
                  onPressed: () async {
                    if (await FlutterContacts.requestPermission()) {
                      Contact? contact =
                          await FlutterContacts.openExternalPick();

                      if (contact != null) {
                        phoneNumberTextEditingController.text =
                            contact.phones[0].number;
                      }
                    }
                  },
                ),
              ),
              DetailsCardModel(
                hintText: "Email Address",
                textEditingController: emailTextEditingController
                  ..text =
                      widget.repairDetailsModel.customerDetailsModel?.email ??
                          "",
                prefixIcon: Icon(
                  CupertinoIcons.mail_solid,
                  size: 24.sp,
                ),
              ),
              DetailsCardModel(
                hintText: "Address",
                textEditingController: addressTextEditingController
                  ..text =
                      widget.repairDetailsModel.customerDetailsModel?.address ??
                          "",
                prefixIcon: Icon(
                  Icons.location_pin,
                  size: 24.sp,
                ),
              ),
              DetailsCardModel(
                hintText: "GSTIN",
                textEditingController: gstinTextEditingController
                  ..text =
                      widget.repairDetailsModel.customerDetailsModel?.gstin ??
                          "",
                prefixIcon: Icon(
                  CupertinoIcons.doc_text_fill,
                  size: 24.sp,
                ),
              ),
            ],
          ),
          HeightBox(16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: PrimaryButton(
                  title: "Close",
                  backgroundColor: colorsConstants.hintGrey,
                  onPress: () {
                    Navigator.pop(context);
                  },
                ),
              ),
              WidthBox(10.w),
              Expanded(
                child: PrimaryButton(
                  title: "Save",
                  onPress: () {
                    widget.repairDetailsModel.customerDetailsModel?.username =
                        userNameTextEditingController.text;

                    widget.repairDetailsModel.customerDetailsModel?.phone =
                        phoneNumberTextEditingController.text;

                    widget.repairDetailsModel.customerDetailsModel?.email =
                        emailTextEditingController.text;

                    widget.repairDetailsModel.customerDetailsModel?.address =
                        addressTextEditingController.text;

                    widget.repairDetailsModel.customerDetailsModel?.gstin =
                        gstinTextEditingController.text;

                    Navigator.pop(context);

                    setState(() {});
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  AlertDialog editAditionalInformationDialog(BuildContext context) {
    return AlertDialog(
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          DetailsCard(
            cardTitle: "Additional Information",
            detailsCardTextsFieldData: [
              DetailsCardModel(
                keyboardType: TextInputType.number,
                textEditingController: odometerEditingController
                  ..text = widget.repairDetailsModel.additionalInformationModel
                          ?.odometer ??
                      "",
                hintText: "Odometer (in KMs)",
                prefixIcon: Icon(
                  Icons.speed_outlined,
                  size: 24.sp,
                ),
              ),
            ],
          ),
          HeightBox(16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: PrimaryButton(
                  title: "Close",
                  backgroundColor: colorsConstants.hintGrey,
                  onPress: () {
                    Navigator.pop(context);
                  },
                ),
              ),
              WidthBox(10.w),
              Expanded(
                child: PrimaryButton(
                  title: "Save",
                  onPress: () {
                    widget.repairDetailsModel.additionalInformationModel
                        ?.odometer = odometerEditingController.text;

                    Navigator.pop(context);

                    setState(() {});
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  AlertDialog editVehicleDetailsDialog(BuildContext context) {
    return AlertDialog(
      insetPadding: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: 30.h,
      ),
      contentPadding: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: 30.w,
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  DetailsCard(
                    cardTitle: "Vehicle Details",
                    detailsCardTextsFieldData: [
                      DetailsCardModel(
                        readOnly: true,
                        textEditingController: makeTextEditingController
                          ..text = widget.repairDetailsModel.vehicleDetailsModel
                                  ?.make ??
                              "",
                        onTap: () {
                          // createRepairOrderController.overlayDataSource =
                          //     createRepairOrderController.makeDataSource;
                          // createRepairOrderController
                          //     .makeOverlayPortalController
                          //     .show();
                        },
                        hintText: "Make*",
                        prefixIcon: Icon(
                          Icons.factory_rounded,
                          size: 24.sp,
                        ),
                      ),
                      DetailsCardModel(
                        hintText: "Model*",
                        readOnly: true,
                        textEditingController: modelTextEditingController
                          ..text = widget.repairDetailsModel.vehicleDetailsModel
                                  ?.model ??
                              "",
                        prefixIcon: Icon(
                          Icons.motorcycle_rounded,
                          size: 24.sp,
                        ),
                        onTap: () {
                          // createRepairOrderController.overlayDataSource =
                          //     createRepairOrderController.modelDataSource;
                          // createRepairOrderController
                          //     .modelOverlayPortalController
                          //     .show();
                        },
                      ),
                      DetailsCardModel(
                        hintText: "Purchase Date",
                        readOnly: true,
                        onTap: () {
                          showCupertinoModalPopup(
                            context: context,
                            builder: (context) {
                              return Container(
                                height: 300.h,
                                color: Colors.white,
                                child: CupertinoDatePicker(
                                  mode: CupertinoDatePickerMode.monthYear,
                                  onDateTimeChanged: (DateTime value) {
                                    debugPrint(value.toString());
                                    // createRepairOrderController
                                    //         .purchaseDateEditingController
                                    //         .text =
                                    //     createRepairOrderController
                                    //         .purchaseDateFormat
                                    //         .format(value);
                                  },
                                ),
                              );
                            },
                          );
                        },
                        textEditingController: purchaseDateEditingController
                          ..text = widget.repairDetailsModel.vehicleDetailsModel
                                  ?.purchaseDate ??
                              "",
                        prefixIcon: Icon(
                          CupertinoIcons.calendar,
                          size: 24.sp,
                        ),
                      ),
                      DetailsCardModel(
                        hintText: "Engine Number",
                        textEditingController: engineTextEditingController
                          ..text = widget.repairDetailsModel.vehicleDetailsModel
                                  ?.engineNumber ??
                              "",
                        prefixIcon: Icon(
                          Icons.settings_rounded,
                          size: 24.sp,
                        ),
                      ),
                      DetailsCardModel(
                        hintText: "VIN (Chasis Number)",
                        textEditingController: vinTextEditingController
                          ..text = widget.repairDetailsModel.vehicleDetailsModel
                                  ?.chasisNumber ??
                              "",
                        prefixIcon: Icon(
                          CupertinoIcons.barcode,
                          size: 24.sp,
                        ),
                      ),
                      DetailsCardModel(
                        hintText: "Insurance Provider",
                        readOnly: true,
                        textEditingController:
                            insuranceProviderEditingController
                              ..text = widget
                                      .repairDetailsModel
                                      .vehicleDetailsModel
                                      ?.insuranceProviderModel
                                      ?.companyName ??
                                  "",
                        prefixIcon: Icon(
                          Icons.medical_information_rounded,
                          size: 24.sp,
                        ),
                        onTap: () {
                          showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return const InsuranceProviderDialog();
                            },
                          );
                        },
                      ),
                      DetailsCardModel(
                        hintText: "Insurer GSTIN",
                        textEditingController: insurerGSTINEditingController
                          ..text = widget.repairDetailsModel.vehicleDetailsModel
                                  ?.insurerGSTIN ??
                              "",
                        prefixIcon: Icon(
                          CupertinoIcons.doc_text_fill,
                          size: 24.sp,
                        ),
                      ),
                      DetailsCardModel(
                        hintText: "Insurer Address",
                        textEditingController:
                            insurerAddressTextEditingController
                              ..text = widget.repairDetailsModel
                                      .vehicleDetailsModel?.insurerAddress ??
                                  "",
                        prefixIcon: Icon(
                          Icons.badge_rounded,
                          size: 24.sp,
                        ),
                      ),
                      DetailsCardModel(
                        hintText: "Policy Number",
                        textEditingController: policyNumberEditingController
                          ..text = widget.repairDetailsModel.vehicleDetailsModel
                                  ?.policyNumber ??
                              "",
                        prefixIcon: Icon(
                          CupertinoIcons.doc_text_fill,
                          size: 24.sp,
                        ),
                      ),
                    ],
                    trailing: InsuranceExpiryDateContainer(
                      createRepairOrderController:
                          Get.find<CreateRepairOrderController>(),
                    ),
                  ),
                  HeightBox(16.h),
                ],
              ),
            ),
          ),
          HeightBox(16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: PrimaryButton(
                  title: "Close",
                  backgroundColor: colorsConstants.hintGrey,
                  onPress: () {
                    Navigator.pop(context);
                  },
                ),
              ),
              WidthBox(10.w),
              Expanded(
                child: PrimaryButton(
                  title: "Save",
                  onPress: () {
                    Navigator.pop(context);

                    setState(() {});
                  },
                ),
              ),
            ],
          ),
          HeightBox(10.h),
        ],
      ),
    );
  }
}
