import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/orders/controllers/repair_details_controller.dart';
import 'package:speed_force_franchise/widgets/small_primary_button.dart';

import '../../../utils/common_exports.dart';

class CustomerRemarksContainer extends StatelessWidget {
  const CustomerRemarksContainer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    RepairDetailsController repairDetailsController =
        Get.find<RepairDetailsController>();
    return GetBuilder<RepairDetailsController>(
        id: "customerRemarks",
        builder: (context) {
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            decoration: BoxDecoration(
              color: colorsConstants.whiteColor,
              boxShadow: Constants.boxShadow,
              borderRadius: BorderRadius.circular(5.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
                  child: Row(
                    children: [
                      Text(
                        "CUSTOMER REMARKS",
                        style: TextStyle(
                          color: colorsConstants.blackColor.withOpacity(0.8),
                        ),
                      ),
                      const Spacer(),
                      SmallPrimaryButton(
                        title: "ADD",
                        icon: CupertinoIcons.add_circled_solid,
                        onPress: () {
                          repairDetailsController.addRemark();
                        },
                      ),
                    ],
                  ),
                ),
                HeightBox(5.h),
                Container(
                  color: colorsConstants.slateGrey,
                  padding: repairDetailsController.customerRemarks.isNotEmpty
                      ? EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h)
                      : null,
                  child: SingleChildScrollView(
                      child: Column(
                    children: repairDetailsController.customerRemarks
                        .mapIndexed((index, customerRemark) {
                      return Container(
                        margin: EdgeInsets.only(bottom: 10.h),
                        child: Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller:
                                    TextEditingController(text: customerRemark),
                                onChanged: (value) {
                                  repairDetailsController.updateRemarkAt(
                                      remark: value, index: index);
                                },
                                decoration: InputDecoration(
                                  filled: true,
                                  fillColor: colorsConstants.whiteColor,
                                  hintText: "Customer Remark",
                                  contentPadding: EdgeInsets.only(left: 5.w),
                                  enabledBorder: const OutlineInputBorder(
                                    borderSide: BorderSide.none,
                                  ),
                                ),
                              ),
                            ),
                            IconButton(
                              onPressed: () {
                                repairDetailsController.removeRemark(
                                    index: index);
                              },
                              icon: const Icon(Icons.close),
                            )
                          ],
                        ),
                      );
                    }).toList(),
                  )),
                ),
              ],
            ),
          );
        });
  }
}
