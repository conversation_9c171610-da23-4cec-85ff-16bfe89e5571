import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/orders/models/details_card_model.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class DetailsCard extends StatefulWidget {
  const DetailsCard({
    super.key,
    required this.cardTitle,
    required this.detailsCardTextsFieldData,
    this.trailing,
    this.extraFields,
  });
  final String cardTitle;
  final List<DetailsCardModel>? detailsCardTextsFieldData;
  final List<Widget>? extraFields;
  final Widget? trailing;

  @override
  State<DetailsCard> createState() => _DetailsCardState();
}

class _DetailsCardState extends State<DetailsCard> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        boxShadow: Constants.boxShadow,
        borderRadius: BorderRadius.circular(5.r),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            decoration: BoxDecoration(
              color: colorsConstants.slateGrey,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(5.r),
                topRight: Radius.circular(5.r),
              ),
            ),
            width: double.maxFinite,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
              child: Text(widget.cardTitle),
            ),
          ),
          if (widget.detailsCardTextsFieldData != null) ...[
            ...widget.detailsCardTextsFieldData!.map(
              (DetailsCardModel detailsCardTextFieldData) {
                return Column(
                  children: [
                    Divider(
                      height: 2.h,
                      color: colorsConstants.slateGrey,
                    ),
                    CustomTextField(
                      controller:
                          detailsCardTextFieldData.textEditingController,
                      keyboardType: detailsCardTextFieldData.keyboardType,
                      inputFormatters: detailsCardTextFieldData.inputFormatters,
                      readOnly: detailsCardTextFieldData.readOnly,
                      onTap: detailsCardTextFieldData.onTap,
                      hintText: detailsCardTextFieldData.hintText,
                      textInputAction: TextInputAction.next,
                      filled: true,
                      fillColor: colorsConstants.whiteColor,
                      prefix: detailsCardTextFieldData.prefixIcon,
                      suffix: detailsCardTextFieldData.suffixIcon,
                      border: const OutlineInputBorder(
                        borderSide: BorderSide.none,
                        borderRadius: BorderRadius.zero,
                      ),
                      enabledBorder: const OutlineInputBorder(
                        borderSide: BorderSide.none,
                        borderRadius: BorderRadius.zero,
                      ),
                      disabledBorder: const OutlineInputBorder(
                        borderSide: BorderSide.none,
                        borderRadius: BorderRadius.zero,
                      ),
                      focusedBorder: const OutlineInputBorder(
                        borderSide: BorderSide.none,
                        borderRadius: BorderRadius.zero,
                      ),
                    ),
                  ],
                );
              },
            ),
            ...widget.extraFields ?? [],
            if (widget.trailing != null) ...[
              Divider(
                height: 2.h,
                color: colorsConstants.slateGrey,
              ),
              widget.trailing!,
            ],
          ]
        ],
      ),
    );
  }
}
