import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/orders/controllers/repair_details_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

class EstimatedDeliveryContainer extends StatelessWidget {
  EstimatedDeliveryContainer({
    super.key,
  });

  DateTime? estimatedDeliveryDate;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<RepairDetailsController>(
      id: "estimatedDelivery",
      builder: (_) {
        RepairDetailsController repairDetailsController =
            Get.find<RepairDetailsController>();
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
          margin: EdgeInsets.symmetric(horizontal: 16.w),
          decoration: BoxDecoration(
            color: colorsConstants.whiteColor,
            boxShadow: Constants.boxShadow,
            borderRadius: BorderRadius.circular(5.r),
          ),
          child: InkWell(
            onTap: () async {
              showCupertinoModalPopup(
                context: context,
                builder: (context) {
                  return Container(
                    height: 300.h,
                    color: Colors.white,
                    child: Column(
                      children: [
                        Expanded(
                          child: CupertinoDatePicker(
                            mode: CupertinoDatePickerMode.dateAndTime,
                            initialDateTime: repairDetailsController
                                .estimatedDeliveryDateTime,
                            onDateTimeChanged: (DateTime value) {
                              estimatedDeliveryDate = value;
                            },
                          ),
                        ),
                        HeightBox(10.h),
                        Container(
                          margin: EdgeInsets.symmetric(horizontal: 20.w),
                          width: double.maxFinite,
                          child: PrimaryButton(
                              onPress: () {
                                if (estimatedDeliveryDate != null) {
                                  repairDetailsController
                                          .estimatedDeliveryDateTime =
                                      estimatedDeliveryDate!;

                                  repairDetailsController
                                      .update(["estimatedDelivery"]);
                                }

                                Navigator.pop(context);
                              },
                              title: "Add"),
                        ),
                        HeightBox(20.h),
                      ],
                    ),
                  );
                },
              );
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Row(
                  children: [
                    Text("Estimated Delivery"),
                    Spacer(),
                    Icon(Icons.arrow_drop_down),
                  ],
                ),
                HeightBox(10.h),
                Text(
                  DateFormat.d().add_M().add_y().add_jm().format(
                        repairDetailsController.estimatedDeliveryDateTime,
                      ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
