import 'package:flutter/material.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class FuelLevelSlider extends StatefulWidget {
  const FuelLevelSlider({
    super.key,
    this.onChange,
  });

  final void Function(double)? onChange;

  @override
  State<FuelLevelSlider> createState() => _FuelLevelSliderState();
}

class _FuelLevelSliderState extends State<FuelLevelSlider> {
  double _currentSliderValue = 10;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            WidthBox(12.w),
            Icon(
              Icons.thermostat_rounded,
              size: 20.sp,
            ),
            Expanded(
              child: Slider(
                value: _currentSliderValue,
                max: 10,
                divisions: 10,
                label: _currentSliderValue.round().toString(),
                onChanged: (double value) {
                  setState(() {
                    _currentSliderValue = value;
                    widget.onChange?.call(_currentSliderValue);
                  });
                },
              ),
            ),
            Icon(
              Icons.thermostat_rounded,
              size: 24.sp,
            ),
            WidthBox(12.w),
          ],
        ),
        Text("Fuel Level: $_currentSliderValue"),
        HeightBox(10.h),
      ],
    );
  }
}
