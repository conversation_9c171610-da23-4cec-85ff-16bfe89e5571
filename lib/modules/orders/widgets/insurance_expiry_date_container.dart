import 'package:flutter/material.dart';
import 'package:speed_force_franchise/models/selected_images_model.dart';
import 'package:speed_force_franchise/modules/orders/controllers/create_repair_order_controller.dart';
import 'package:speed_force_franchise/modules/orders/widgets/load_media_container.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class InsuranceExpiryDateContainer extends StatelessWidget {
  const InsuranceExpiryDateContainer({
    super.key,
    required this.createRepairOrderController,
  });

  final CreateRepairOrderController createRepairOrderController;

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      margin: EdgeInsets.symmetric(horizontal: 10.w),
      child: Column(
        children: [
          HeightBox(10.h),
          Text(
            "Insurance Expiry Date",
            style: TextStyle(
              color: colorsConstants.hintGrey,
            ),
          ),
          HeightBox(10.h),
          CustomTextField(
            readOnly: true,
            onTap: () async {
              DateTime minDateTime = DateTime.utc(1985, 04, 20);
              DateTime maxDateTime = DateTime.utc(275760, 09, 13);
              DateTime? pickedDateTime = await showDatePicker(
                context: context,
                firstDate: minDateTime,
                lastDate: maxDateTime,
              );

              if (pickedDateTime != null) {
                createRepairOrderController.insuranceExpiryDate =
                    pickedDateTime;
                createRepairOrderController
                        .insuranceExpiryDateEditingController.text =
                    createRepairOrderController.insuranceExpiryDateFormat
                        .format(pickedDateTime);
              }
            },
            contentPadding: EdgeInsets.symmetric(horizontal: 10.w),
            controller: createRepairOrderController
                .insuranceExpiryDateEditingController,
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: colorsConstants.blackColor.withOpacity(0.3),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: colorsConstants.blackColor.withOpacity(0.3),
              ),
            ),
            disabledBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: colorsConstants.blackColor.withOpacity(0.3),
              ),
            ),
            suffix: Icon(
              Icons.calendar_month_outlined,
              size: 24.sp,
            ),
            inputTextStyle: TextStyle(
              color: colorsConstants.blackColor.withOpacity(0.7),
            ),
          ),
          HeightBox(16.h),
          LoadMediaContainer(
            title: "Registration\nCertificate",
            onMediaLoaded: (SelectedImageModel pickedMedia) {
              createRepairOrderController.registrationCertificatedImages
                  .add(pickedMedia);

              if (createRepairOrderController.createRepairOrderModel != null) {
                if (createRepairOrderController
                        .createRepairOrderModel?.vehicleDetailsModel !=
                    null) {
                  createRepairOrderController
                          .createRepairOrderModel
                          ?.vehicleDetailsModel
                          ?.registrationCertificatesImages =
                      createRepairOrderController
                          .registrationCertificatedImages;
                }
              }
              createRepairOrderController.update();
            },
            onRemoveMedia: (SelectedImageModel removedMedia) {
              createRepairOrderController.registrationCertificatedImages
                  .remove(removedMedia);

              if (createRepairOrderController.createRepairOrderModel != null) {
                if (createRepairOrderController
                        .createRepairOrderModel?.vehicleDetailsModel !=
                    null) {
                  createRepairOrderController
                          .createRepairOrderModel
                          ?.vehicleDetailsModel
                          ?.registrationCertificatesImages =
                      createRepairOrderController
                          .registrationCertificatedImages;
                }
              }

              createRepairOrderController.update();
            },
          ),
          HeightBox(16.h),
          LoadMediaContainer(
            title: "Insurance",
            onMediaLoaded: (SelectedImageModel pickedMedia) {
              createRepairOrderController.insuranceImages.add(pickedMedia);

              if (createRepairOrderController.createRepairOrderModel != null) {
                if (createRepairOrderController
                        .createRepairOrderModel?.vehicleDetailsModel !=
                    null) {
                  createRepairOrderController.createRepairOrderModel
                          ?.vehicleDetailsModel?.insuranceImages =
                      createRepairOrderController.insuranceImages;
                }
              }

              createRepairOrderController.update();
            },
            onRemoveMedia: (SelectedImageModel removedMedia) {
              createRepairOrderController.insuranceImages.remove(removedMedia);

              if (createRepairOrderController.createRepairOrderModel != null) {
                if (createRepairOrderController
                        .createRepairOrderModel?.vehicleDetailsModel !=
                    null) {
                  createRepairOrderController.createRepairOrderModel
                          ?.vehicleDetailsModel?.insuranceImages =
                      createRepairOrderController.insuranceImages;
                }
              }

              createRepairOrderController.update();
            },
          ),
          HeightBox(16.h),
        ],
      ),
    );
  }
}
