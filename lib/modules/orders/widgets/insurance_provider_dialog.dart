import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/orders/controllers/create_repair_order_controller.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/insurance_provider_model.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class InsuranceProviderDialog extends StatelessWidget {
  const InsuranceProviderDialog({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    CreateRepairOrderController createRepairOrderController =
        Get.find<CreateRepairOrderController>();

    return AlertDialog(
      content: ConstrainedBox(
        constraints: BoxConstraints(maxHeight: 300.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Insurance Provider',
                    style: TextStyle(fontSize: 20.sp),
                  ),
                ),
                IconButton(
                  onPressed: () {
                    moveToAddInsuranceProviderScreen(context: context);
                  },
                  icon: Icon(
                    CupertinoIcons.add,
                    size: 23.sp,
                  ),
                ),
              ],
            ),
            SingleChildScrollView(
              child: Column(
                children: [
                  ...createRepairOrderController.customersInsuranceProviders
                      .map(
                    (InsuranceProviderModel insuranceProvider) {
                      return Container(
                        margin: EdgeInsets.symmetric(vertical: 5.h),
                        child: InkWell(
                          onTap: () {
                            createRepairOrderController
                                .insuranceProviderEditingController
                                .text = insuranceProvider.companyName ?? "";
                            Navigator.pop(context);
                          },
                          child: Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(8.sp),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(),
                                ),
                              ),
                              WidthBox(10.w),
                              SizedBox(
                                width: 200.w,
                                child: Text(
                                  insuranceProvider.companyName ?? "",
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              )
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
            HeightBox(10.h),
            ElevatedButton(
              onPressed: () {
                moveToAddInsuranceProviderScreen(context: context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: colorsConstants.primaryRed,
                foregroundColor: colorsConstants.whiteColor,
                shape: ContinuousRectangleBorder(
                  borderRadius: BorderRadius.circular(5.r),
                ),
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text("Add New"),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
