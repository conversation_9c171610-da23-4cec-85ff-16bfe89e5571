import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/models/selected_images_model.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/widgets/small_primary_button.dart';

class LoadMediaContainer extends StatefulWidget {
  const LoadMediaContainer({
    super.key,
    required this.title,
    required this.onMediaLoaded,
    required this.onRemoveMedia,
  });

  final String title;
  final void Function(SelectedImageModel pickedMedia) onMediaLoaded;
  final void Function(SelectedImageModel removedMedia) onRemoveMedia;

  @override
  State<LoadMediaContainer> createState() => _LoadMediaContainerState();
}

class _LoadMediaContainerState extends State<LoadMediaContainer> {
  List<SelectedImageModel> pickedMediaFiles = [];

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Text(widget.title),
            const Spacer(),
            SmallPrimaryButton(
              title: "Add",
              icon: CupertinoIcons.camera_fill,
              onPress: () {
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  builder: (BuildContext context) {
                    return Container(
                      color: colorsConstants.whiteColor,
                      padding: EdgeInsets.symmetric(
                        horizontal: 16.w,
                      ),
                      width: double.maxFinite,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          HeightBox(10.h),
                          Text(
                            "Select Image Source",
                            style: TextStyle(
                              fontSize: 16.sp,
                              color:
                                  colorsConstants.blackColor.withOpacity(0.5),
                            ),
                          ),
                          HeightBox(16.h),
                          InkWell(
                            onTap: () async {
                              Navigator.pop(context);

                              SelectedImageModel? pickedMedia =
                                  await MediaPickerService.pickImage();

                              if (pickedMedia != null) {
                                pickedMediaFiles.add(pickedMedia);
                                widget.onMediaLoaded.call(pickedMedia);
                              }
                            },
                            child: Container(
                              width: double.maxFinite,
                              padding: EdgeInsets.symmetric(vertical: 10.h),
                              child: Text(
                                "Load From Library",
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  color: colorsConstants.blackColor,
                                ),
                              ),
                            ),
                          ),
                          HeightBox(10.h),
                          InkWell(
                            onTap: () async {
                              Navigator.pop(context);

                              SelectedImageModel? pickedMedia =
                                  await MediaPickerService.captureImage();

                              if (pickedMedia != null) {
                                pickedMediaFiles.add(pickedMedia);
                                widget.onMediaLoaded.call(pickedMedia);
                              }
                            },
                            child: Container(
                              width: double.maxFinite,
                              padding: EdgeInsets.symmetric(vertical: 10.h),
                              child: Text(
                                "Use Camera",
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  color: colorsConstants.blackColor,
                                ),
                              ),
                            ),
                          ),
                          HeightBox(50.h),
                        ],
                      ),
                    );
                  },
                );
              },
            ),
          ],
        ),
        if (pickedMediaFiles.isNotEmpty) ...[
          HeightBox(10.h),
          const Divider(),
          Container(
            padding: EdgeInsets.symmetric(vertical: 10.h),
            width: double.maxFinite,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              physics: const AlwaysScrollableScrollPhysics(),
              child: Row(
                children: [
                  WidthBox(10.w),
                  ...pickedMediaFiles.map(
                    (SelectedImageModel mediaFile) {
                      return Container(
                        color: colorsConstants.lightWhite,
                        margin: EdgeInsets.symmetric(
                            horizontal: 10.w, vertical: 10.h),
                        child: Badge(
                          offset: Offset(0.sp, -10.sp),
                          padding: EdgeInsets.zero,
                          alignment: Alignment.topRight,
                          largeSize: 22.sp,
                          backgroundColor: colorsConstants.transparent,
                          label: InkWell(
                            onTap: () {
                              pickedMediaFiles.remove(mediaFile);
                              widget.onRemoveMedia.call(mediaFile);
                            },
                            child: CircleAvatar(
                              backgroundColor:
                                  colorsConstants.blackColor.withOpacity(0.8),
                              child: Icon(
                                CupertinoIcons.xmark,
                                size: 15.sp,
                                color: colorsConstants.whiteColor,
                              ),
                            ),
                          ),
                          child: ClipRRect(
                              borderRadius: BorderRadius.circular(5.r),
                              child: Image.memory(
                                mediaFile.uInt8List,
                                height: 80.sp,
                                width: 80.sp,
                                fit: BoxFit.cover,
                              )
                              // .file(
                              //   File(
                              //     mediaFile.path,
                              //   ),
                              //   height: 80.sp,
                              //   width: 80.sp,
                              //   fit: BoxFit.cover,
                              // ),
                              ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ]
      ],
    );
  }
}
