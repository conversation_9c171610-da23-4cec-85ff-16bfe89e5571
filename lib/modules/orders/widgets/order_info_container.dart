import 'package:flutter/material.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class OrderInfoModel {
  OrderInfoModel({
    required this.header,
    required this.columnData,
  });

  String header;
  List<String> columnData;
}

class OrderInfoContainer extends StatefulWidget {
  const OrderInfoContainer({
    super.key,
    required this.orderInfoModels,
    this.totalTitle,
    this.totalAmount,
    this.showBorderInEveryColumnData,
    this.gstIncluded,
    this.cgst,
    this.sgst,
    this.igst,
  });

  final List<OrderInfoModel> orderInfoModels;
  final String? totalTitle;
  final String? cgst;
  final String? sgst;
  final String? igst;
  final String? totalAmount;
  final bool? showBorderInEveryColumnData;
  final List<bool>? gstIncluded;

  @override
  State<OrderInfoContainer> createState() => _OrderInfoContainerState();
}

class _OrderInfoContainerState extends State<OrderInfoContainer> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          color: colorsConstants.lightBlue,
          padding: EdgeInsets.symmetric(horizontal: 2.w),
          child: Row(
            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: widget.orderInfoModels
                .mapIndexed((int index, OrderInfoModel orderInfoModel) {
              return Expanded(
                child: Text(
                  orderInfoModel.header,
                  style: TextStyle(fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                  textAlign: index == 0
                      ? TextAlign.start
                      : index == widget.orderInfoModels.length - 1
                          ? TextAlign.end
                          : TextAlign.center,
                ),
              );
            }).toList(),
          ),
        ),
        HeightBox(5.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 5.w),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children:
                widget.orderInfoModels.mapIndexed((index, orderInfoModel) {
              return Expanded(
                child: Column(
                  crossAxisAlignment: index == 0
                      ? CrossAxisAlignment.start
                      : index == widget.orderInfoModels.length - 1
                          ? CrossAxisAlignment.end
                          : CrossAxisAlignment.center,
                  children: [
                    ...orderInfoModel.columnData.map((element) {
                      return Column(
                        children: [
                          Text(
                            element,
                            style: TextStyle(
                                fontSize: element == "SUB TOTAL :" ? 16 : 14,
                                color: colorsConstants.secondaryBlack),
                            maxLines: 4,
                            overflow: TextOverflow.ellipsis,
                            textAlign: index == 0
                                ? TextAlign.start
                                : index == widget.orderInfoModels.length - 1
                                    ? TextAlign.end
                                    : TextAlign.center,
                          ),
                          HeightBox(5.h),
                        ],
                      );
                    }),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
        if (widget.totalAmount != null) ...[
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 5.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                if (((widget.gstIncluded?[0] ?? false) == true) &&
                    ((widget.gstIncluded?[1] ?? false) == false))
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      const Spacer(),
                      Text(
                        "CGST :",
                        style: TextStyle(color: colorsConstants.secondaryBlack),
                      ),
                      const Spacer(),
                      Text(
                        "${Constants.rupeeSign} ${widget.cgst}",
                        style: TextStyle(color: colorsConstants.secondaryBlack),
                      ),
                      HeightBox(5.h),
                    ],
                  ),
                HeightBox(3.h),
                if (((widget.gstIncluded?[0] ?? false) == true) &&
                    ((widget.gstIncluded?[1] ?? false) == false))
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      const Spacer(),
                      Text(
                        "SGST :",
                        style: TextStyle(color: colorsConstants.secondaryBlack),
                      ),
                      const Spacer(),
                      Text(
                        "${Constants.rupeeSign} ${widget.sgst}",
                        style: TextStyle(color: colorsConstants.secondaryBlack),
                      ),
                    ],
                  ),
                HeightBox(3.h),
                if (((widget.gstIncluded?[0] ?? false) == true) &&
                    ((widget.gstIncluded?[1] ?? false) == true))
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      const Spacer(),
                      Text(
                        "IGST :",
                        style: TextStyle(color: colorsConstants.secondaryBlack),
                      ),
                      const Spacer(),
                      Text(
                        "${Constants.rupeeSign} ${widget.igst}",
                        style: TextStyle(color: colorsConstants.secondaryBlack),
                      ),
                    ],
                  ),
                const Divider(
                  height: 1,
                ),
                HeightBox(3.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    const Spacer(),
                    Text(
                      "${widget.totalTitle} :",
                      style: TextStyle(color: colorsConstants.secondaryBlack),
                    ),
                    const Spacer(),
                    Text(
                      "${Constants.rupeeSign} ${widget.totalAmount}",
                      style: TextStyle(color: colorsConstants.secondaryBlack),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ]
      ],
    );
  }
}
