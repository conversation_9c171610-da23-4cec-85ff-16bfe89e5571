import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';

import '../../../utils/common_exports.dart';

class OrderStatusCard extends StatelessWidget {
  const OrderStatusCard({
    super.key,
    required this.currentStatus,
    this.onYesTap,
  });

  final OrderStatus currentStatus;
  final void Function(OrderStatus orderStatus)? onYesTap;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        boxShadow: Constants.boxShadow,
        color: colorsConstants.whiteColor,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: InkWell(
              onTap: () {
                updateStatusDialog(context, OrderStatus.created);
              },
              child: Container(
                color: currentStatus == OrderStatus.created
                    ? colorsConstants.primaryRed
                    : colorsConstants.whiteColor,
                padding: EdgeInsets.symmetric(horizontal: 5.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    HeightBox(5.h),
                    Text(
                      "OPEN",
                      style: TextStyle(
                        color: currentStatus == OrderStatus.created
                            ? colorsConstants.whiteColor
                            : null,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                    HeightBox(2.h),
                    Text(
                      "Vehicle Received",
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: currentStatus == OrderStatus.created
                            ? colorsConstants.whiteColor
                            : null,
                      ),
                    ),
                    HeightBox(5.h),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: InkWell(
              onTap: () {
                updateStatusDialog(context, OrderStatus.workInProgress);
              },
              child: Container(
                color: currentStatus == OrderStatus.workInProgress
                    ? colorsConstants.primaryRed
                    : colorsConstants.whiteColor,
                padding: EdgeInsets.symmetric(horizontal: 5.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    HeightBox(5.h),
                    Text(
                      "WIP",
                      style: TextStyle(
                        color: currentStatus == OrderStatus.workInProgress
                            ? colorsConstants.whiteColor
                            : null,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                    HeightBox(2.h),
                    Text(
                      "Work in progress",
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: currentStatus == OrderStatus.workInProgress
                            ? colorsConstants.whiteColor
                            : null,
                      ),
                    ),
                    HeightBox(5.h),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: InkWell(
              onTap: () async {
                updateStatusDialog(context, OrderStatus.ready);
              },
              child: Container(
                color: currentStatus == OrderStatus.ready
                    ? colorsConstants.primaryRed
                    : colorsConstants.whiteColor,
                padding: EdgeInsets.symmetric(horizontal: 5.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    HeightBox(5.h),
                    Text(
                      "DONE",
                      style: TextStyle(
                        color: currentStatus == OrderStatus.ready
                            ? colorsConstants.whiteColor
                            : null,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                    HeightBox(2.h),
                    Text(
                      "Vehicle is ready",
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: currentStatus == OrderStatus.ready
                            ? colorsConstants.whiteColor
                            : null,
                      ),
                    ),
                    HeightBox(5.h),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<dynamic> updateStatusDialog(
      BuildContext context, OrderStatus updateOrderStatusTo) {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "Are you sure you want to change order status?",
                style: TextStyle(
                  fontSize: 16.sp,
                  color: colorsConstants.hintGrey,
                ),
              ),
              HeightBox(20.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Text(
                      "NO",
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: colorsConstants.primaryRed,
                      ),
                    ),
                  ),
                  WidthBox(20.w),
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                      onYesTap?.call(updateOrderStatusTo);
                    },
                    child: Text(
                      "YES",
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: colorsConstants.primaryRed,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
