import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/service_model.dart';
import 'package:speed_force_franchise/models/stock_model.dart';
import 'package:speed_force_franchise/modules/orders/controllers/repair_details_controller.dart';
import 'package:speed_force_franchise/modules/orders/controllers/repair_order_details_controller.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/service_part.model.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/modules/orders/widgets/custom_overlay.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import '../../../utils/common_exports.dart';

class ProgressCheckList extends StatefulWidget {
  const ProgressCheckList({
    super.key,
    required this.title,
    required this.isEditing,
    required this.servicesPartsList,
    this.onSaveProgress,
    this.onToggleEditing,
    this.onServicePartToggle,
    required this.repairOrderDetailsController,
  });

  final String title;
  final RepairOrderDetailsController repairOrderDetailsController;
  final bool isEditing;
  final List<ServicePartModel> servicesPartsList;
  final void Function(
      {required String servicePartId,
      required bool isCompleted})? onServicePartToggle;

  final void Function()? onSaveProgress;
  final void Function()? onToggleEditing;

  @override
  State<ProgressCheckList> createState() => _ProgressCheckListState();
}

class _ProgressCheckListState extends State<ProgressCheckList> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    Get.put(RepairDetailsController(
      createRepairOrderModel:
          widget.repairOrderDetailsController.repairOrderModel ??
              RepairOrderModel(),
    )
      ..fetchServices()
      ..fetchParts());
  }

  // @override
  // void dispose() {
  //   // TODO: implement dispose
  //   super.dispose();
  //   Get.delete<RepairDetailsController>();
  // }
  bool submitted = false;
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        boxShadow: Constants.boxShadow,
        color: colorsConstants.whiteColor,
      ),
      child: Column(
        children: [
          Container(
            color: colorsConstants.slateGrey,
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
            child: Row(
              children: [
                Text(
                  widget.title,
                  style: TextStyle(
                    color: colorsConstants.hintGrey,
                  ),
                ),
                const Spacer(),
                if ((widget.repairOrderDetailsController.repairOrderModel
                        ?.orderStatus) ==
                    OrderStatus.workInProgress)
                  InkWell(
                    onTap: () {
                      if (widget.isEditing) {
                        widget.onSaveProgress?.call();
                      }
                      widget.onToggleEditing?.call();
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 10.w,
                        vertical: 5.h,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                          width: 1.sp,
                          color: colorsConstants.primaryRed,
                        ),
                      ),
                      child: Row(
                        children: [
                          if (!widget.isEditing) ...[
                            Icon(
                              Icons.edit,
                              size: 14.sp,
                              color: colorsConstants.primaryRed,
                            ),
                            WidthBox(5.w),
                          ],
                          Text(
                            !widget.isEditing ? "Edit" : "Save",
                            style: TextStyle(
                              fontSize: 10.sp,
                              color: colorsConstants.primaryRed,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                SizedBox(
                  width: 8,
                ),
                if (widget.isEditing)
                  InkWell(
                    onTap: () async {
                      showDialog(
                          context: context,
                          builder: (context) {
                            return StatefulBuilder(
                                builder: (context, setState2) {
                              return Padding(
                                padding: const EdgeInsets.all(20.0),
                                child: submitted
                                    ? Center(
                                        child: SizedBox(
                                            height: 50,
                                            width: 50,
                                            child: CircularProgressIndicator()))
                                    : CustomOverlay(
                                        title: widget.title == "PARTS"
                                            ? "Choose Parts"
                                            : "Choose Service",
                                        allowMultiSelection: true,
                                        onBackPress: () {
                                          Navigator.pop(context);
                                        },
                                        onMultiSelectSubmit:
                                            (List<String> selectedParts) async {
                                          if (submitted) {
                                            return;
                                          }
                                          submitted = true;
                                          setState2(() {});

                                          for (String partsTitle
                                              in selectedParts) {
                                            PartModel? partModel;
                                            ServiceModel? serviceModel;
                                            if (widget.title == "PARTS") {
                                              await partsAdd(
                                                  partModel, partsTitle);
                                            } else {
                                              await serviceAdd(
                                                serviceModel,
                                                partsTitle,
                                              );
                                            }
                                            print(widget
                                                .repairOrderDetailsController
                                                .repairOrderModel
                                                ?.repairDetailsModel
                                                ?.toMap());
                                            await widget
                                                .repairOrderDetailsController
                                                .updateRepairOrder();
                                            // print(widget.repairOrderDetailsController
                                            //     .repairOrderModel?.repairDetailsModel
                                            //     ?.toInvoiceMap());
                                            if (widget
                                                    .repairOrderDetailsController
                                                    .invoice
                                                    ?.invoiceId !=
                                                null) {
                                              await FirebaseFirestore.instance
                                                  .collection(
                                                      FirebaseCollections
                                                          .invoices.name)
                                                  .doc(widget
                                                          .repairOrderDetailsController
                                                          .invoice
                                                          ?.invoiceId ??
                                                      "")
                                                  .update({
                                                'repairDetailsModel': widget
                                                    .repairOrderDetailsController
                                                    .repairOrderModel
                                                    ?.repairDetailsModel
                                                    ?.toInvoiceMap()
                                              });
                                            }
                                          }
                                          await widget
                                              .repairOrderDetailsController
                                              .fetchRepairOrderDetails(
                                                  orderId: widget
                                                          .repairOrderDetailsController
                                                          .repairOrderModel
                                                          ?.orderId ??
                                                      "");

                                          Get.find<RepairOrderDetailsController>()
                                                  .repairOrderModel =
                                              widget
                                                  .repairOrderDetailsController
                                                  .repairOrderModel;
                                          await Future.delayed(
                                              const Duration(milliseconds: 100),
                                              () => {
                                                    widget
                                                        .repairOrderDetailsController
                                                        .update()
                                                  });

                                          Navigator.pop(context);
                                          setState2(() {});
                                          submitted = false;
                                        },
                                        dataSource: widget.title == "PARTS"
                                            ? Get.find<
                                                    RepairDetailsController>()
                                                .parts
                                                .fold(<String>[],
                                                    (previousValue, element) {
                                                previousValue
                                                    .add(element.name ?? "");
                                                return previousValue;
                                              })
                                            : Get.find<
                                                    RepairDetailsController>()
                                                .services
                                                .fold(<String>[],
                                                    (previousValue, element) {
                                                previousValue
                                                    .add(element.name ?? "");
                                                return previousValue;
                                              }),
                                        addNewOnPress: () {
                                          widget.title == "PARTS"
                                              ? moveToAddServicePartScreen(
                                                  context: context,
                                                  isAddingService: false,
                                                  revertCallback: (_) async {
                                                    await Get.find<
                                                            RepairDetailsController>()
                                                        .fetchParts();
                                                    Get.find<
                                                            RepairDetailsController>()
                                                        .update();
                                                  },
                                                )
                                              : moveToAddServicePartScreen(
                                                  context: context,
                                                  isAddingService: true,
                                                  revertCallback: (_) async {
                                                    await Get.find<
                                                            RepairDetailsController>()
                                                        .fetchServices();
                                                    Get.find<
                                                            RepairDetailsController>()
                                                        .update();
                                                  },
                                                );
                                          ;
                                        },
                                      ),
                              );
                            });
                          });
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 10.w,
                        vertical: 5.h,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                          width: 1.sp,
                          color: colorsConstants.primaryRed,
                        ),
                      ),
                      child: Row(
                        children: [
                          if (!widget.isEditing) ...[
                            Icon(
                              Icons.edit,
                              size: 14.sp,
                              color: colorsConstants.primaryRed,
                            ),
                            WidthBox(5.w),
                          ],
                          Text(
                            "Add",
                            style: TextStyle(
                              fontSize: 10.sp,
                              color: colorsConstants.primaryRed,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                SizedBox(
                  width: 8,
                )
              ],
            ),
          ),
          HeightBox(10.h),
          Container(
            margin: EdgeInsets.symmetric(horizontal: 10.w),
            child: Column(
              children: [
                ...widget.servicesPartsList.map((servicePart) {
                  return Row(
                    children: [
                      Expanded(
                        child: Text('- ${servicePart.title}'),
                      ),
                      Row(
                        children: [
                          Checkbox(
                            value: servicePart.completed,
                            activeColor: colorsConstants.primaryRed
                                .withOpacity(widget.isEditing ? 1 : 0.5),
                            side: BorderSide(
                              color: colorsConstants.hintGrey
                                  .withOpacity(widget.isEditing ? 1 : 0.5),
                              width: 1.sp,
                            ),
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                            onChanged: (value) {
                              if (!widget.isEditing) {
                                return;
                              }
                              widget.onServicePartToggle?.call(
                                servicePartId: servicePart.id ?? "",
                                isCompleted: value ?? false,
                              );
                            },
                          ),
                          IconButton(
                            onPressed: () async {
                              if ((!(servicePart.completed ?? false)) &&
                                  (widget.isEditing)) {
                                deleteServicePart(servicePart);
                              }
                            },
                            icon: Icon(CupertinoIcons.delete),
                            iconSize: 18,
                          )
                        ],
                      ),
                    ],
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  deleteServicePart(servicePart) async {
    bool loggingOut = false;
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            title: const Text(
              "Delete",
              style: TextStyle(fontSize: 25, color: Colors.black),
            ),
            content: const Text(
              'Are you sure you want to delete?',
              style: TextStyle(fontSize: 15, color: Colors.black),
            ),
            // actionsPadding: EdgeInsets.all(0),
            actionsAlignment: MainAxisAlignment.center,
            actions: [
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  loggingOut
                      ? SizedBox(
                          height: 40,
                          width: 40,
                          child: CircularProgressIndicator())
                      : ElevatedButton(
                          onPressed: () async {
                            loggingOut = true;
                            setState2(() {});
                            if (widget.title == "PARTS") {
                              QuerySnapshot<Map<String, dynamic>>
                                  partsStockCollection = await FirebaseFirestore
                                      .instance
                                      .collection(
                                          FirebaseCollections.stock.name)
                                      .where("partId",
                                          isEqualTo: servicePart.id)
                                      .get();

                              for (var stockDoc in partsStockCollection.docs) {
                                StockModel stockModel =
                                    StockModel.fromMap(stockDoc.data());
                                if (servicePart.quantity != null) {
                                  stockModel.currentStock =
                                      stockModel.currentStock! +
                                          servicePart.quantity!.toDouble();
                                }

                                await FirebaseFirestore.instance
                                    .collection(FirebaseCollections.stock.name)
                                    .doc(stockModel.stockId)
                                    .update(stockModel.toMap());
                              }
                              widget.repairOrderDetailsController
                                  .repairOrderModel?.repairDetailsModel?.parts
                                  ?.removeWhere((element) =>
                                      element.id == servicePart.id);
                            } else {
                              widget
                                  .repairOrderDetailsController
                                  .repairOrderModel
                                  ?.repairDetailsModel
                                  ?.services
                                  ?.removeWhere((element) =>
                                      element.id == servicePart.id);
                            }

                            widget.repairOrderDetailsController.update();
                            widget.repairOrderDetailsController.invoice
                                    ?.repairDetailsModel =
                                widget.repairOrderDetailsController
                                    .repairOrderModel?.repairDetailsModel;
                            await widget.repairOrderDetailsController
                                .updateRepairOrder();
                            // print(widget.repairOrderDetailsController
                            //     .repairOrderModel?.repairDetailsModel
                            //     ?.toInvoiceMap());
                            await FirebaseFirestore.instance
                                .collection(FirebaseCollections.invoices.name)
                                .doc(widget.repairOrderDetailsController.invoice
                                        ?.invoiceId ??
                                    "")
                                .update({
                              'repairDetailsModel': widget
                                  .repairOrderDetailsController
                                  .repairOrderModel
                                  ?.repairDetailsModel
                                  ?.toInvoiceMap()
                            });

                            await widget.repairOrderDetailsController
                                .fetchRepairOrderDetails(
                                    orderId: widget.repairOrderDetailsController
                                            .repairOrderModel?.orderId ??
                                        "");

                            Get.find<RepairOrderDetailsController>()
                                    .repairOrderModel =
                                widget.repairOrderDetailsController
                                    .repairOrderModel;
                            await Future.delayed(
                                const Duration(milliseconds: 100),
                                () => {
                                      widget.repairOrderDetailsController
                                          .update()
                                    });

                            Navigator.of(context).pop();
                            loggingOut = false;
                            setState2(() {});
                          },
                          style: ElevatedButton.styleFrom(
                              elevation: 0,
                              foregroundColor: Colors.white,
                              backgroundColor: Colors.redAccent),
                          child: const Text(
                            "Yes",
                            style: TextStyle(fontSize: 16),
                          )),
                  const SizedBox(width: 10),
                  ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                          elevation: 0,
                          foregroundColor: Colors.white,
                          backgroundColor: Colors.green),
                      child: const Text(
                        "No",
                        style: TextStyle(fontSize: 16),
                      )),
                ],
              ),
            ],
          );
        });
      },
    );
  }

  partsAdd(PartModel? partModel, String partsTitle) async {
    partModel =
        Get.find<RepairDetailsController>().parts.firstWhereOrNull((el) {
      return (el.name?.toLowerCase() ?? "") == partsTitle.toLowerCase();
    });
    num amount = partModel?.mrp ?? 0;
    final calpartGst = ((partModel?.gstRate ?? 0) * amount) / 100;

    ServicePartModel servicePartModel = ServicePartModel(
      partsGst: calpartGst,
      id: partModel?.partId,
      title: partsTitle,
      partServiceNumber: partModel?.partNumber,
      quantity: 1,
      rate: partModel?.mrp ?? 0,
      purchasePrice: partModel?.purchasePrice ?? 0,
      isOil: partModel?.isOil ?? false,
      amount: partModel?.mrp ?? 0,
      discount: 0,
      completed: false,
      gstRate: partModel?.gstRate ?? 0,
    );

    if (widget.repairOrderDetailsController.repairOrderModel?.repairDetailsModel
            ?.parts
            ?.firstWhereOrNull((element) =>
                element.partServiceNumber ==
                servicePartModel.partServiceNumber) ==
        null) {
      widget.repairOrderDetailsController.repairOrderModel?.repairDetailsModel
          ?.parts
          ?.add(servicePartModel);
    } else {
      ServicePartModel? partsUpdate = widget.repairOrderDetailsController
          .repairOrderModel?.repairDetailsModel?.parts
          ?.firstWhereOrNull((element) =>
              element.partServiceNumber == servicePartModel.partServiceNumber);
      if (partsUpdate != null) {
        widget.repairOrderDetailsController.repairOrderModel?.repairDetailsModel
            ?.parts
            ?.firstWhereOrNull((element) =>
                element.partServiceNumber == servicePartModel.partServiceNumber)
            ?.amount = partsUpdate.amount! * 2;
        widget.repairOrderDetailsController.repairOrderModel?.repairDetailsModel
            ?.parts
            ?.firstWhereOrNull((element) =>
                element.partServiceNumber == servicePartModel.partServiceNumber)
            ?.quantity = partsUpdate.quantity! + 1;
        widget.repairOrderDetailsController.repairOrderModel?.repairDetailsModel
            ?.parts
            ?.firstWhereOrNull((element) =>
                element.partServiceNumber == servicePartModel.partServiceNumber)
            ?.partsGst = partsUpdate.partsGst! * 2;
      }
    }

    double partsTotal = 0.0;
    for (ServicePartModel element in widget.repairOrderDetailsController
            .repairOrderModel?.repairDetailsModel?.parts ??
        []) {
      partsTotal += (element.amount ?? 0) +
          ((widget.repairOrderDetailsController.repairOrderModel?.gstIncluded ??
                  false)
              ? (element.partsGst ?? 0)
              : 0);
    }
    double total = (widget.repairOrderDetailsController.repairOrderModel
                ?.repairDetailsModel?.servicesTotal ??
            0) +
        partsTotal;
    total = total.roundToDouble();
    widget.repairOrderDetailsController.repairOrderModel?.repairDetailsModel
        ?.paymentDue = total;
    widget.repairOrderDetailsController.repairOrderModel?.repairDetailsModel
        ?.partsTotal = partsTotal;
    widget.repairOrderDetailsController.repairOrderModel?.repairDetailsModel
        ?.total = total;
    widget.repairOrderDetailsController.repairOrderModel?.repairDetailsModel
        ?.paymentDue = total;

    Get.find<RepairDetailsController>()
        .createRepairOrderModel
        .repairDetailsModel
        ?.partsTotal = partsTotal;
    widget.repairOrderDetailsController.invoice?.repairDetailsModel = widget
        .repairOrderDetailsController.repairOrderModel?.repairDetailsModel;
// Get.find<RepairDetailsController>().createRepairOrderModel.repairDetailsModel?.=partsTotal;
    for (ServicePartModel part in widget.repairOrderDetailsController
            .repairOrderModel?.repairDetailsModel?.parts ??
        []) {
      print("efefefefef---efefef---1");
      QuerySnapshot<Map<String, dynamic>> partsStockCollection =
          await FirebaseFirestore.instance
              .collection(FirebaseCollections.stock.name)
              .where("partId", isEqualTo: part.id)
              .get();
      print("efefefefef---efefef---2");
      for (var stockDoc in partsStockCollection.docs) {
        StockModel stockModel = StockModel.fromMap(stockDoc.data());
        if (part.quantity != null) {
          print(part.title);
          print(part.id);
          stockModel.currentStock =
              (stockModel.currentStock ?? 0) - part.quantity!.toDouble();
        }
        print("efefefefef---efefef---3");

        await FirebaseFirestore.instance
            .collection(FirebaseCollections.stock.name)
            .doc(stockModel.stockId)
            .update(stockModel.toMap());
        print("efefefefef---efefef---4");
      }
    }

    widget.repairOrderDetailsController.update();
  }

  serviceAdd(
    ServiceModel? serviceModel,
    String partsTitle,
  ) async {
    serviceModel =
        Get.find<RepairDetailsController>().services.firstWhereOrNull((el) {
      return el.name?.toLowerCase() == partsTitle.toLowerCase();
    });

    num amount = serviceModel?.price ?? 0;
    final calservicesGst = ((serviceModel?.gstRate ?? 0) * amount) / 100;

    ServicePartModel servicePartModel = ServicePartModel(
      servicesGst: calservicesGst,
      id: serviceModel?.serviceId ?? "",
      title: serviceModel?.name ?? "",
      // partServiceNumber: serviceModel?.serviceNumber,
      quantity: 1,
      rate: serviceModel?.price ?? 0,
      amount: serviceModel?.price ?? 0,
      discount: 0,
      completed: false,
      gstRate: serviceModel?.gstRate ?? 0,
    );
    if (widget.repairOrderDetailsController.repairOrderModel?.repairDetailsModel
            ?.services
            ?.firstWhereOrNull((element) =>
                element.title?.toLowerCase().trim() ==
                servicePartModel.title?.toLowerCase().trim()) ==
        null) {
      widget.repairOrderDetailsController.repairOrderModel?.repairDetailsModel
          ?.services
          ?.add(servicePartModel);
      double serviceTotal = 0.0;
      for (ServicePartModel element in widget.repairOrderDetailsController
              .repairOrderModel?.repairDetailsModel?.services ??
          []) {
        serviceTotal += (element.amount ?? 0) +
            ((widget.repairOrderDetailsController.repairOrderModel
                        ?.gstIncluded ??
                    false)
                ? (element.servicesGst ?? 0)
                : 0);
      }
      double total = serviceTotal +
          (widget.repairOrderDetailsController.repairOrderModel
                  ?.repairDetailsModel?.partsTotal ??
              0);
      widget.repairOrderDetailsController.repairOrderModel?.repairDetailsModel
          ?.paymentDue = total;
      widget.repairOrderDetailsController.repairOrderModel?.repairDetailsModel
          ?.servicesTotal = serviceTotal;
      widget.repairOrderDetailsController.repairOrderModel?.repairDetailsModel
          ?.total = total;

      Get.find<RepairDetailsController>()
          .createRepairOrderModel
          .repairDetailsModel
          ?.servicesTotal = serviceTotal;
      widget.repairOrderDetailsController.invoice?.repairDetailsModel = widget
          .repairOrderDetailsController.repairOrderModel?.repairDetailsModel;

      widget.repairOrderDetailsController.update();
    } else {
      Utils.showSnackBar(
        title: "${servicePartModel.title} Already Added",
      );
    }
  }
}
