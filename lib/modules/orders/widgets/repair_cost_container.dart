import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/orders/controllers/repair_details_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class RepairCostContainer extends StatelessWidget {
  const RepairCostContainer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<RepairDetailsController>(
      id: "costContainer",
      builder: (context) {
        RepairDetailsController repairDetailsController =
            Get.find<RepairDetailsController>();
        print("=-=-${repairDetailsController.selectedGsttype}");

        return Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
          margin: EdgeInsets.symmetric(horizontal: 16.w),
          decoration: BoxDecoration(
            color: colorsConstants.slateGrey,
            boxShadow: Constants.boxShadow,
            borderRadius: BorderRadius.circular(5.r),
          ),
          child: Column(
            children: [
              HeightBox(16.h),
              Row(
                // mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  ...List.generate(
                    repairDetailsController.gstTypes.length,
                    (index) {
                      return Row(
                        children: [
                          // Radio(
                          //   value: repairDetailsController.gstTypes[index],
                          //   groupValue: (repairDetailsController
                          //               .createRepairOrderModel.gstIncluded ??
                          //           false)
                          //       ? "With GST"
                          //       : "Without GST",
                          //   onChanged: (value) {
                          //     repairDetailsController.selectedGsttype = value;
                          //     repairDetailsController
                          //             .createRepairOrderModel.gstIncluded =
                          //         value == "Without GST" ? false : true;

                          //     repairDetailsController.update(['costContainer']);
                          //   },
                          // ),
                          Radio(
                            value: repairDetailsController.gstTypes[index],
                            groupValue: repairDetailsController.selectedGsttype,
                            onChanged: (value) {
                              repairDetailsController.selectedGsttype = value;
                              repairDetailsController
                                      .createRepairOrderModel.gstIncluded =
                                  value == "Without GST" ? false : true;

                              repairDetailsController.update(['costContainer']);
                            },
                          ),
                          Text(repairDetailsController.gstTypes[index])
                        ],
                      );
                    },
                  ),
                ],
              ),
              if (repairDetailsController.selectedGsttype ==
                  repairDetailsController.gstTypes[1])
                Row(
                  children: [
                    Checkbox(
                      value: repairDetailsController.igst,
                      onChanged: (value) {
                        repairDetailsController.igst = value!;
                        repairDetailsController.createRepairOrderModel.isigst =
                            value;
                        repairDetailsController.update(['costContainer']);
                      },
                    ),
                    const Text("IGST")
                  ],
                ),
              Row(
                children: [
                  const Text("Services Total"),
                  Text(
                    " (Including Discount)",
                    style: TextStyle(fontSize: 8.sp),
                  ),
                  const Spacer(),
                  Text(Constants.rupeeSign),
                  WidthBox(10.w),
                  // Container(
                  //   color: colorsConstants.whiteColor,
                  //   padding:
                  //       EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
                  //   child: Text(
                  //     repairDetailsController.laborTotal.toString(),
                  //   ),
                  // )
                  Text(
                    repairDetailsController.laborTotal.toStringAsFixed(2),
                  ),
                ],
              ),
              if (repairDetailsController.selectedGsttype ==
                  repairDetailsController.gstTypes[1]) ...[
                HeightBox(16.h),
                Row(
                  children: [
                    const Text("Services GST"),
                    // Text(
                    //   " (Including Discount)",
                    //   style: TextStyle(fontSize: 8.sp),
                    // ),
                    const Spacer(),
                    Text(Constants.rupeeSign),
                    WidthBox(10.w),
                    // Container(
                    //   color: colorsConstants.whiteColor,
                    //   padding: EdgeInsets.symmetric(
                    //       horizontal: 16.w, vertical: 10.h),
                    //   child: Text(
                    //     repairDetailsController.servicesGst.toString(),
                    //   ),
                    // )
                    Text(
                      repairDetailsController.servicesGst.toStringAsFixed(2),
                    ),
                  ],
                ),
              ],
              HeightBox(16.h),
              Row(
                children: [
                  const Text("Parts Total"),
                  Text(
                    " (Including Discount)",
                    style: TextStyle(fontSize: 8.sp),
                  ),
                  const Spacer(),
                  Text(Constants.rupeeSign),
                  WidthBox(10.w),
                  // Container(
                  //   color: colorsConstants.whiteColor,
                  //   padding:
                  //       EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
                  //   child: Text(
                  //     repairDetailsController.partsTotal.toString(),
                  //   ),
                  // ),
                  Text(
                    repairDetailsController.partsTotal.toStringAsFixed(2),
                  ),
                ],
              ),
              if (repairDetailsController.selectedGsttype ==
                  repairDetailsController.gstTypes[1]) ...[
                HeightBox(16.h),
                Row(
                  children: [
                    const Text("Parts GST"),
                    // Text(
                    //   " (Including Discount)",
                    //   style: TextStyle(fontSize: 8.sp),
                    // ),
                    const Spacer(),
                    Text(Constants.rupeeSign),
                    WidthBox(10.w),
                    // Container(
                    //   color: colorsConstants.whiteColor,
                    //   padding: EdgeInsets.symmetric(
                    //       horizontal: 16.w, vertical: 10.h),
                    //   child: Text(
                    //     repairDetailsController.partsTotal.toString(),
                    //   ),
                    // )
                    Text(
                      repairDetailsController.partsGst.toStringAsFixed(2),
                    ),
                  ],
                ),
              ],
              HeightBox(16.h),
              Row(
                children: [
                  const Text("Sub Total"),
                  Text(
                    " (Including Discount)",
                    style: TextStyle(fontSize: 8.sp),
                  ),
                  const Spacer(),
                  Text(Constants.rupeeSign),
                  WidthBox(10.w),
                  // Container(
                  //   color: colorsConstants.whiteColor,
                  //   padding:
                  //       EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
                  //   child: Text(
                  //     (repairDetailsController.laborTotal +
                  //             repairDetailsController.partsTotal)
                  //         .toString(),
                  //   ),
                  // )
                  repairDetailsController.selectedGsttype ==
                          repairDetailsController.gstTypes[1]
                      ? Text(
                          (repairDetailsController.laborTotal +
                                  repairDetailsController.partsTotal +
                                  repairDetailsController.servicesGst +
                                  repairDetailsController.partsGst)
                              .toStringAsFixed(2),
                        )
                      : Text(
                          (repairDetailsController.laborTotal +
                                  repairDetailsController.partsTotal)
                              .toStringAsFixed(2),
                        )
                ],
              ),
              HeightBox(16.h),
              Row(
                children: [
                  Text(
                    "You saved ",
                    style: TextStyle(
                      color: colorsConstants.blackColor.withOpacity(0.8),
                    ),
                  ),
                  // const Spacer(),
                  Text(
                    Constants.rupeeSign,
                    style: TextStyle(
                      color: colorsConstants.blackColor.withOpacity(0.8),
                    ),
                  ),
                  // WidthBox(10.w),
                  // Container(
                  //   color: colorsConstants.whiteColor,
                  //   padding:
                  //       EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
                  //   child: Text(repairDetailsController.discountTotal
                  //       .toPrecision(2)
                  //       .toString()),
                  // )
                  Text(
                    " ${repairDetailsController.discountTotal.toPrecision(2).toString()}",
                    style: TextStyle(
                      color: colorsConstants.blackColor.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
