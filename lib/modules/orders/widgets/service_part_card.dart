import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/orders/controllers/repair_details_controller.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/service_part.model.dart';
import 'package:speed_force_franchise/widgets/small_text_field.dart';

import '../../../utils/common_exports.dart';

class ServicePartCard extends StatefulWidget {
  ServicePartCard(
      {super.key,
      required this.servicePartModel,
      this.alreadyAppliedDiscount,
      this.showQtyField = true}) {
    if (!showQtyField) {
      quantityTextEditingController.text = '1.0';
    }
    rateTextEditingController.text = servicePartModel.rate.toString();
  }
  final bool showQtyField;
  final ServicePartModel servicePartModel;

  final TextEditingController discountTextEditingController =
      TextEditingController();
  final TextEditingController quantityTextEditingController =
      TextEditingController();
  final TextEditingController rateTextEditingController =
      TextEditingController();

  final double? alreadyAppliedDiscount;

  @override
  State<ServicePartCard> createState() => _ServicePartCardState();
}

class _ServicePartCardState extends State<ServicePartCard> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void didUpdateWidget(covariant ServicePartCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.servicePartModel.discount != null) {
      widget.discountTextEditingController.text =
          widget.servicePartModel.discount!.toString();
    }
    if (widget.servicePartModel.quantity != null) {
      widget.quantityTextEditingController.text =
          widget.servicePartModel.quantity!.toString();
    }
    if (widget.servicePartModel.rate != null) {
      widget.rateTextEditingController.text =
          widget.servicePartModel.rate!.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    RepairDetailsController repairDetailsController =
        Get.find<RepairDetailsController>();
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 10.h),
      margin: EdgeInsets.only(bottom: 5.h),
      decoration: BoxDecoration(
        color: colorsConstants.blackColor.withOpacity(0.03),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.servicePartModel.title ?? "",
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: colorsConstants.blackColor.withOpacity(0.8),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              if (widget.alreadyAppliedDiscount == null) ...[
                Text(
                  "Discount %",
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: colorsConstants.blackColor.withOpacity(0.8),
                  ),
                ),
                WidthBox(10.w),
                SmallTextField(
                  controller: widget.discountTextEditingController,
                  onChanged: (String discount) {
                    widget.servicePartModel.discount =
                        double.tryParse(discount);
                    repairDetailsController.calculateServicePartAmount(
                      servicePartModel: widget.servicePartModel,
                      alreadyAppliedDiscount: widget.alreadyAppliedDiscount,
                    );
                  },
                ),
                WidthBox(10.w),
              ],
              IconButton(
                onPressed: () {
                  repairDetailsController.removeServicePart(
                      servicePart: widget.servicePartModel);
                  repairDetailsController.calculateServicePartAmount(
                      servicePartModel: widget.servicePartModel,
                      alreadyAppliedDiscount: widget.servicePartModel.discount);
                },
                icon: Icon(
                  Icons.remove_circle,
                  color: colorsConstants.primaryRed,
                ),
              ),
            ],
          ),
          HeightBox(5.h),
          Row(
            children: [
              if (widget.showQtyField) ...[
                Column(
                  children: [
                    Text(
                      "Qty.",
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: colorsConstants.blackColor.withOpacity(0.8),
                      ),
                    ),
                    HeightBox(2.h),
                    SmallTextField(
                      controller: widget.quantityTextEditingController,
                      onChanged: (String quantity) {
                        widget.servicePartModel.quantity =
                            int.tryParse(quantity);
                        repairDetailsController.calculateServicePartAmount(
                          servicePartModel: widget.servicePartModel,
                          alreadyAppliedDiscount: widget.alreadyAppliedDiscount,
                        );
                      },
                    ),
                  ],
                ),
                WidthBox(10.w),
              ],
              Column(
                children: [
                  Text(
                    "Rate (${widget.servicePartModel.purchasePrice ?? 0.0})",
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: colorsConstants.blackColor.withOpacity(0.8),
                    ),
                  ),
                  HeightBox(2.h),
                  SmallTextField(
                    controller: widget.rateTextEditingController,
                    onChanged: (String rate) {
                      widget.servicePartModel.rate = double.tryParse(rate);

                      repairDetailsController.calculateServicePartAmount(
                        servicePartModel: widget.servicePartModel,
                        alreadyAppliedDiscount: widget.alreadyAppliedDiscount,
                      );
                    },
                  ),
                ],
              ),
              WidthBox(12.w),
              Column(
                children: [
                  Text(
                    "Amount",
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: colorsConstants.blackColor.withOpacity(0.8),
                    ),
                  ),
                  HeightBox(2.h),
                  GetBuilder<RepairDetailsController>(
                      id: widget.servicePartModel.id,
                      builder: (context) {
                        return Text(
                          "${Constants.rupeeSign} ${widget.showQtyField ? widget.servicePartModel.amount ?? "0.00" : widget.servicePartModel.amount}",
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: colorsConstants.blackColor.withOpacity(0.8),
                          ),
                        );
                      }),
                ],
              ),
            ],
          )
        ],
      ),
    );
  }
}
