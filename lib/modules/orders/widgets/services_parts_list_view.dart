import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/orders/controllers/repair_details_controller.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/service_part.model.dart';
import 'package:speed_force_franchise/modules/orders/widgets/service_part_card.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/widgets/small_primary_button.dart';
import 'package:speed_force_franchise/widgets/small_text_field.dart';

class ServicesPartsListView extends StatefulWidget {
  const ServicesPartsListView({
    super.key,
    required this.title,
    required this.servicePartList,
    this.onAdd,
  });

  final String title;

  final void Function()? onAdd;
  final List<ServicePartModel> servicePartList;

  @override
  State<ServicesPartsListView> createState() => _ServicesPartsListViewState();
}

class _ServicesPartsListViewState extends State<ServicesPartsListView> {
  bool applyDiscountToAll = true;
  TextEditingController allDiscountTextEditingController =
      TextEditingController()..text = "0.0";

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        boxShadow: Constants.boxShadow,
        borderRadius: BorderRadius.circular(5.r),
        color: Colors.white,
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            decoration: BoxDecoration(
              color: colorsConstants.primaryRed.withOpacity(0.3),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(5.r),
                topRight: Radius.circular(5.r),
              ),
            ),
            child: Row(
              children: [
                Text(
                  widget.title,
                  style: TextStyle(
                    color: colorsConstants.blackColor.withOpacity(0.8),
                  ),
                ),
                const Spacer(),
                SmallPrimaryButton(
                  title: "ADD",
                  icon: CupertinoIcons.add_circled_solid,
                  onPress: () {
                    widget.onAdd?.call();
                  },
                ),
              ],
            ),
          ),
          Row(
            children: [
              WidthBox(16.w),
              Text(
                "Apply discount to all",
                style: TextStyle(
                  fontSize: 12.sp,
                  color: colorsConstants.hintGrey,
                ),
              ),
              Text(
                " (%)",
                style: TextStyle(
                  fontSize: 10.sp,
                  color: colorsConstants.hintGrey,
                ),
              ),
              // WidthBox(8.7.w),
              const Spacer(),
              Transform.scale(
                scale: .8,
                child: Switch(
                  value: applyDiscountToAll,
                  onChanged: (bool value) {
                    applyDiscountToAll = value;
                    if (applyDiscountToAll) {
                      allDiscountTextEditingController.text = "0.0";
                    } else {
                      allDiscountTextEditingController.text = "null";
                    }

                    RepairDetailsController repairDetailsController =
                        Get.find<RepairDetailsController>();
                    for (ServicePartModel servicePartModel
                        in widget.servicePartList) {
                      repairDetailsController.calculateServicePartAmount(
                        servicePartModel: servicePartModel,
                        alreadyAppliedDiscount: double.tryParse(
                          allDiscountTextEditingController.text,
                        ),
                      );
                    }

                    setState(() {});
                  },
                ),
              ),
              if (applyDiscountToAll) ...[
                const Spacer(),
                SmallTextField(
                  height: 20.h,
                  width: 80.w,
                  cursorHeight: 20.h,
                  controller: allDiscountTextEditingController,
                  onChanged: (String value) {
                    if ((num.tryParse(value) ?? 0) <= 100) {
                      RepairDetailsController repairDetailsController =
                          Get.find<RepairDetailsController>();
                      for (ServicePartModel servicePartModel
                          in widget.servicePartList) {
                        repairDetailsController.calculateServicePartAmount(
                          servicePartModel: servicePartModel,
                          alreadyAppliedDiscount: double.tryParse(
                            value,
                          ),
                        );
                      }
                      setState(() {});
                    }
                  },
                ),
                WidthBox(16.w),
              ],
            ],
          ),
          ...widget.servicePartList.map(
            (ServicePartModel servicePart) {
              return ServicePartCard(
                servicePartModel: servicePart,
                alreadyAppliedDiscount: applyDiscountToAll
                    ? double.tryParse(allDiscountTextEditingController.text) ??
                        0.0
                    : null,
                showQtyField: !(widget.title.toLowerCase() == "services"),
              );
            },
          ),
        ],
      ),
    );
  }
}
