import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/orders/controllers/repair_details_controller.dart';
import 'package:speed_force_franchise/widgets/small_primary_button.dart';

import '../../../utils/common_exports.dart';

class TagsContainer extends StatefulWidget {
  const TagsContainer({
    super.key,
  });

  @override
  State<TagsContainer> createState() => _TagsContainerState();
}

class _TagsContainerState extends State<TagsContainer> {
  @override
  Widget build(BuildContext context) {
    RepairDetailsController repairDetailsController =
        Get.find<RepairDetailsController>();
    return GetBuilder<RepairDetailsController>(
        id: "tagsContainer",
        builder: (context) {
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            decoration: BoxDecoration(
              color: colorsConstants.whiteColor,
              boxShadow: Constants.boxShadow,
              borderRadius: BorderRadius.circular(5.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      "TAGS",
                      style: TextStyle(
                        color: colorsConstants.blackColor.withOpacity(0.8),
                      ),
                    ),
                    const Spacer(),
                    SmallPrimaryButton(
                      title: "ADD",
                      icon: CupertinoIcons.add_circled_solid,
                      onPress: () {
                        repairDetailsController.addTagsOverlayPortalController
                            .show();
                      },
                    ),
                  ],
                ),
                if (repairDetailsController.selectedTags.isNotEmpty) ...[
                  HeightBox(10.h),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: repairDetailsController.selectedTags
                          .mapIndexed((index, tag) {
                        return Container(
                          margin: EdgeInsets.symmetric(horizontal: 5.w),
                          padding: EdgeInsets.symmetric(
                              horizontal: 5.sp, vertical: 2.sp),
                          decoration: BoxDecoration(
                            border:
                                Border.all(color: colorsConstants.primaryRed),
                            borderRadius: BorderRadius.circular(5.r),
                          ),
                          child: Row(
                            children: [
                              Text(tag),
                              WidthBox(5.w),
                              InkWell(
                                onTap: () {
                                  repairDetailsController.removeTag(
                                      index: index);
                                },
                                child: Icon(
                                  CupertinoIcons.clear_circled_solid,
                                  color: colorsConstants.primaryRed,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ],
            ),
          );
        });
  }
}
