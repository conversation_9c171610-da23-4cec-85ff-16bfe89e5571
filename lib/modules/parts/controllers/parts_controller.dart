import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:csv/csv.dart';
import 'package:file_saver/file_saver.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:printing/printing.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/stock_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';

class PartsController extends GetxController {
  PartsController({required this.homeScreenController});

  bool isLoading = false;

  final HomeScreenController homeScreenController;

  List<(PartModel, StockModel)> stockRecords = [];

  Future<void> fetchStock() async {
    stockRecords.clear();
    isLoading = true;
    update();
    QuerySnapshot<Map<String, dynamic>> stockCollection =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.stock.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .get();

    for (var stock in stockCollection.docs) {
      StockModel stockModel = StockModel.fromMap(stock.data());

      DocumentSnapshot<Map<String, dynamic>> part = await FirebaseFirestore
          .instance
          .collection(FirebaseCollections.parts.name)
          .doc(stockModel.partId)
          .get();

      Map<String, dynamic>? partModelMap = part.data();

      if (partModelMap != null) {
        stockRecords.add((PartModel.fromMap(partModelMap), stockModel));
      }
    }
    isLoading = false;
    update();
  }

  Future<void> currentStockdownlaodCSVFile() async {
    List<List<String>> rows = [];
    rows.clear();
    rows.insert(0, [
      "PART_DESCRIPTION",
      "PART_NUMBER",
      "MANUFACTURER",
      "MOQ",
      "CURRENT_STOCK",
      "GST_RATE",
      "MRP",
      "PURCHASE_PRICE",
      "VENDOR"
      // "PART_NAME",
      // "PART_NUMBER",
      // "MANUFACTURER",
      // "CATEGORY",
      // "MAX_STOCK",
      // "MIN_STOCK",
      // "CURRENT_STOCK",
      // "MRP",
      // "PURCHASE_PRICE",
      // "RACK_ID",
      // "VENDOR",
      // "APPLICABLE_BRANDS",
      // "APPLICABLE_MODELS",
    ]);
    for (var stockRecord in stockRecords) {
      rows.add([
        stockRecord.$1.name ?? "",
        stockRecord.$1.partNumber ?? "",
        stockRecord.$1.manufacturer ?? "",
        // stockRecord.$1.category ?? "",
        // (stockRecord.$2.maxStock ?? 0).toString(),
        (stockRecord.$2.minStock ?? 0).toString(),
        (stockRecord.$2.currentStock ?? 0).toString(),
        (stockRecord.$1.gstRate ?? 0).toString(),
        (stockRecord.$2.mrp ?? 0).toString(),
        (stockRecord.$2.purchasePrice ?? 0).toString(),
        // stockRecord.$2.rackId ?? "",
        Get.find<HomeScreenController>()
                .vendors
                .firstWhereOrNull(
                    (element) => element.vendorId == (stockRecord.$2.vendorId))
                ?.name ??
            "",
        // stockRecord.$2.vendorId ?? "",
        // stockRecord.$1.applicableBrands!.join(", "),
        // stockRecord.$1.applicableModels!.join(", "),
      ]);
    }

    String csv = const ListToCsvConverter().convert(rows);
    try {
      // Load the file from assets
      final Uint8List data = utf8.encode(csv);
      final buffer = data.buffer.asUint8List();

      // if (Platform.isAndroid) {
      await Printing.sharePdf(
          bytes: buffer, filename: 'current_stock_details.csv');
      // await FileSaver.instance.saveFile(
      //   name: 'current_stock_details',
      //   ext: 'csv',
      //   bytes: buffer,
      //   mimeType: MimeType.csv,
      // );

      // Utils.showSnackBar(title: "File downloaded");
      // } else {
      //   await Utils.requestPermissions(permissionsList: [
      //     Platform.isIOS ? Permission.storage : Permission.manageExternalStorage
      //   ]);
      //   Directory? directory;

      //   directory = await getApplicationDocumentsDirectory();
      //   final path = '${directory.path}/current_stock_details.csv';

      //   // Write the file to local storage
      //   final file = File(path);
      //   await file.writeAsBytes(buffer);

      //   Utils.showSnackBar(title: "File downloaded to $path");

      //   await Printing.sharePdf(
      //       bytes: await file.readAsBytes(),
      //       filename: "current_stock_details.csv");
      // }
    } catch (e) {
      Utils.showSnackBar(title: "Error $e");
    }
  }
}
