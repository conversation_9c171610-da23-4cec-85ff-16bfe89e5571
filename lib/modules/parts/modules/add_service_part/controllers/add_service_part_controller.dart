import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/service_model.dart';
import 'package:speed_force_franchise/models/stock_model.dart';
import 'package:speed_force_franchise/modules/add_make_models/models/make_models_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/parts/controllers/parts_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

enum PartType {
  generic,
  specific,
}

class AddServicePartController extends GetxController {
  TextEditingController partNameEditingController = TextEditingController();
  TextEditingController mrpEditingController = TextEditingController();
  TextEditingController purchasePriceEditingController =
      TextEditingController();
  TextEditingController partNoEditingController = TextEditingController();
  TextEditingController partCategoryEditingController = TextEditingController();
  TextEditingController manufacturerEditingController = TextEditingController();

  TextEditingController rackIdEditingController = TextEditingController();
  TextEditingController currentStockEditingController = TextEditingController();
  TextEditingController minStockEditingController = TextEditingController();
  TextEditingController maxStockEditingController = TextEditingController();
  TextEditingController vendorEditingController = TextEditingController();

  TextEditingController applicableBrandsEditingController =
      TextEditingController();
  TextEditingController applicableModelsEditingController =
      TextEditingController();

  TextEditingController serviceNameEditingController = TextEditingController();
  TextEditingController serviceNumberEditingController =
      TextEditingController();
  TextEditingController serviceCategoryEditingController =
      TextEditingController();

  List<String> selectedApplicableBrands = [];
  List<String> selectedApplicableModels = [];

  List<String> servicePartsgstRates = ['0', '5', '12', '18', '28'];
  String? selectedservicePartsGstRate;

  PartType selectedPartType = PartType.generic;
  bool isManageInventory = true;
  bool isLoading = false;
  bool isOil = false;

  OverlayPortalController categoriesOverlayPortalController =
      OverlayPortalController();
  OverlayPortalController manufacturerOverlayPortalController =
      OverlayPortalController();
  OverlayPortalController makeOverlayPortalController =
      OverlayPortalController();
  OverlayPortalController modelOverlayPortalController =
      OverlayPortalController();

  List<String> categories = [];
  List<String> manufacturers = [];

  List<MakeModelsModel> vehiclesDataSource = [];

  void toggleIsOil({required value}) {
    isOil = value;
    update();
  }

  Future<void> fetchCategories() async {
    categories.clear();
    isLoading = true;
    update();
    DocumentSnapshot<Map<String, dynamic>> categoriesDocument =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.categories.name)
            .doc(FirebaseAuth.instance.currentUser?.uid)
            .get();

    categories.addAll(
        List<String>.from(categoriesDocument.data()?["categories"] ?? []));
    isLoading = false;
    update();
  }

  Future<void> fetchManufacturers() async {
    manufacturers.clear();
    isLoading = true;
    update();
    DocumentSnapshot<Map<String, dynamic>> manufecturersDocument =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.manufacturers.name)
            .doc(FirebaseAuth.instance.currentUser?.uid)
            .get();

    manufacturers.addAll(List<String>.from(
        manufecturersDocument.data()?["manufacturers"] ?? []));
    isLoading = false;
    update();
  }

  Future<void> fetchVehicles() async {
    QuerySnapshot<Map<String, dynamic>> masterVehiclesCollection =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.vehiclesCompanies.name)
            .where("franchiseId", isNull: true)
            .get();

    for (var element in masterVehiclesCollection.docs) {
      Map<String, dynamic> elementData = element.data();

      MakeModelsModel makeModelsModel = MakeModelsModel.fromMap(elementData);

      vehiclesDataSource.add(makeModelsModel);
    }

    QuerySnapshot<Map<String, dynamic>> vehiclesCollection =
        await FirebaseFirestore
            .instance
            .collection(FirebaseCollections.vehiclesCompanies.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .get();

    for (var element in vehiclesCollection.docs) {
      Map<String, dynamic> elementData = element.data();

      MakeModelsModel makeModelsModel = MakeModelsModel.fromMap(elementData);

      vehiclesDataSource.add(makeModelsModel);
    }
  }

  Future<void> addNewCategoryInDatabase({required String categoryName}) async {
    await FirebaseFirestore.instance
        .collection(FirebaseCollections.categories.name)
        .doc(FirebaseAuth.instance.currentUser?.uid)
        .set({
      "franchiseId": FirebaseAuth.instance.currentUser?.uid,
      "categories": [...categories, categoryName],
    });

    await fetchCategories();

    update();
  }

  Future<void> addNewManufacturerInDatabase(
      {required String manufacturerName}) async {
    await FirebaseFirestore.instance
        .collection(FirebaseCollections.manufacturers.name)
        .doc(FirebaseAuth.instance.currentUser?.uid)
        .set({
      "franchiseId": FirebaseAuth.instance.currentUser?.uid,
      "manufacturers": [...manufacturers, manufacturerName],
    });

    await fetchManufacturers();

    update();
  }

  void togglePartType({required PartType partType}) {
    selectedPartType = partType;
    selectedApplicableBrands.clear();
    selectedApplicableModels.clear();
    update();
  }

  void toggleManageInventory() {
    isManageInventory = !isManageInventory;
    rackIdEditingController.clear();
    currentStockEditingController.clear();
    minStockEditingController.clear();
    maxStockEditingController.clear();
    update();
  }

  Future<void> addServicePartInDatabase(BuildContext context,
      {bool isAddingService = false}) async {
    if (isAddingService) {
      ServiceModel serviceModel = ServiceModel(
        franchiseId: FirebaseAuth.instance.currentUser?.uid,
        name: serviceNameEditingController.text,
        price: double.tryParse(mrpEditingController.text) ?? 0.0,
        serviceNumber: serviceNumberEditingController.text,
        category: serviceCategoryEditingController.text,
        applicableBrands: selectedApplicableBrands,
        applicableModels: selectedApplicableModels,
        gstRate: num.tryParse(selectedservicePartsGstRate ?? "0"),
      );

      CollectionReference<Map<String, dynamic>> partsCollection =
          FirebaseFirestore.instance
              .collection(FirebaseCollections.services.name);

      DocumentReference<Map<String, dynamic>> newPart =
          await partsCollection.add(serviceModel.toMap());
      serviceModel.serviceId = newPart.id;

      await partsCollection
          .doc(serviceModel.serviceId)
          .set(serviceModel.toMap());
    } else {
      await Get.put(PartsController(
              homeScreenController: Get.find<HomeScreenController>()))
          .fetchStock();

      bool oldPart = false;
      final vendorslist = Get.find<HomeScreenController>()
          .vendors
          .where((element) =>
              element.franchiseId == FirebaseAuth.instance.currentUser?.uid)
          .toList();

      final partFromDb = await FirebaseFirestore.instance
          .collection(FirebaseCollections.parts.name)
          .where('franchiseId',
              isEqualTo: FirebaseAuth.instance.currentUser?.uid)
          .get();
      final list =
          partFromDb.docs.map((e) => PartModel.fromMap(e.data())).toList();
      final existingParts = list
          .where((element) =>
              (element.name?.toLowerCase().trim() ==
                  partNameEditingController.text.toLowerCase().trim()) &&
              (element.partNumber?.toLowerCase().trim() ==
                  partNoEditingController.text.toLowerCase().trim()))
          .toList();

      StockModel? stockExist;

      for (var existingPart in existingParts) {
        stockExist = Get.find<PartsController>()
            .stockRecords
            .firstWhereOrNull(
                (element) => element.$2.partId == existingPart.partId)
            ?.$2;
        final oldvendor = vendorslist.firstWhereOrNull(
            (element) => element.vendorId == stockExist?.vendorId);
        print("${oldvendor?.name}   -- ${vendorEditingController.text}");
        if (oldvendor?.name?.toLowerCase().trim() ==
            vendorEditingController.text.toLowerCase().trim()) {
          oldPart = true;

          break;
        }
      }
      print("${oldPart}");
      if (oldPart) {
        int quantity = 1;
        bool saved = false;

        await showDialog(
            context: context,
            builder: (context) =>
                StatefulBuilder(builder: (context, setState2) {
                  print(":wdwdwdwdwdwdwd");
                  void increment() {
                    setState2(() {
                      quantity++;
                    });
                  }

                  void decrement() {
                    if (quantity > 1) {
                      setState2(() {
                        quantity--;
                      });
                    }
                  }

                  return AlertDialog(
                    title: Text('Part Exists'),
                    content: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text("Do you want to add in Quantity"),
                        SizedBox(
                          height: 10.h,
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              onPressed: decrement,
                              icon: const Icon(Icons.remove),
                              color: Colors.red,
                            ),
                            Text(
                              '$quantity',
                              style: const TextStyle(
                                  fontSize: 18, fontWeight: FontWeight.bold),
                            ),
                            IconButton(
                              onPressed: increment,
                              icon: const Icon(Icons.add),
                              color: Colors.green,
                            ),
                          ],
                        )
                      ],
                    ),
                    actions: [
                      ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              elevation: 0,
                              foregroundColor: Colors.white,
                              backgroundColor: Colors.green),
                          onPressed: () async {
                            if (saved) {
                              return;
                            }
                            saved = true;
                            await FirebaseFirestore.instance
                                .collection(FirebaseCollections.stock.name)
                                .doc(stockExist?.stockId)
                                .update({
                              'currentStock': FieldValue.increment(quantity)
                            });
                            if (context.mounted) {
                              Navigator.pop(context);
                            }
                            if (context.mounted) {
                              Navigator.pop(context);
                            }
                            saved = false;
                          },
                          child: Text("Save")),
                      ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              elevation: 0,
                              foregroundColor: Colors.white,
                              backgroundColor: Colors.redAccent),
                          onPressed: () async {
                            if (context.mounted) {
                              Navigator.pop(context);
                            }
                          },
                          child: Text("Cancel"))
                    ],
                  );
                }));
      } else {
        PartModel partModel = PartModel(
            franchiseId: FirebaseAuth.instance.currentUser?.uid,
            name: partNameEditingController.text,
            partNumber: partNoEditingController.text,
            category: partCategoryEditingController.text,
            isOil: isOil,
            manufacturer: manufacturerEditingController.text,
            applicableBrands: selectedApplicableBrands,
            applicableModels: selectedApplicableModels,
            mrp: double.parse(mrpEditingController.text),
            purchasePrice: purchasePriceEditingController.text.isNotEmpty
                ? double.parse(purchasePriceEditingController.text)
                : null,
            gstRate: num.tryParse(selectedservicePartsGstRate ?? "0"));

        CollectionReference<Map<String, dynamic>> partsCollection =
            FirebaseFirestore.instance
                .collection(FirebaseCollections.parts.name);

        DocumentReference<Map<String, dynamic>> newPart =
            await partsCollection.add(partModel.toMap());
        partModel.partId = newPart.id;

        await partsCollection.doc(partModel.partId).set(partModel.toMap());

        StockModel stockModel = StockModel(
          franchiseId: FirebaseAuth.instance.currentUser?.uid,
          partId: partModel.partId,
          minStock: double.tryParse(minStockEditingController.text),
          maxStock: double.tryParse(maxStockEditingController.text),
          currentStock: double.tryParse(currentStockEditingController.text),
          mrp: double.tryParse(mrpEditingController.text),
          purchasePrice: double.tryParse(purchasePriceEditingController.text),
          rackId: rackIdEditingController.text,
        );

        CollectionReference<Map<String, dynamic>> stockCollection =
            FirebaseFirestore.instance
                .collection(FirebaseCollections.stock.name);

        DocumentReference<Map<String, dynamic>> newStock =
            await stockCollection.add(stockModel.toMap());
        stockModel.stockId = newStock.id;

        await stockCollection.doc(stockModel.stockId).set(stockModel.toMap());
        if (context.mounted) {
          Navigator.pop(context);
        }
      }
    }
  }
}
