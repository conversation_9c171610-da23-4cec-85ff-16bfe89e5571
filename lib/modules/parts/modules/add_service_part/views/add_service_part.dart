import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/orders/controllers/repair_details_controller.dart';
import 'package:speed_force_franchise/modules/parts/modules/add_service_part/controllers/add_service_part_controller.dart';
import 'package:speed_force_franchise/modules/orders/widgets/custom_overlay.dart';
import 'package:speed_force_franchise/modules/services/controllers/services_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

class AddServicePart extends StatefulWidget {
  const AddServicePart({super.key, required this.isAddingService});

  final bool isAddingService;

  @override
  State<AddServicePart> createState() => _AddServicePartState();
}

class _AddServicePartState extends State<AddServicePart> {
  bool submitted = false;
  @override
  void initState() {
    super.initState();
    Get.put(
      AddServicePartController()
        ..fetchCategories()
        ..fetchManufacturers()
        ..fetchVehicles(),
    );
  }

  @override
  void dispose() {
    Get.delete<AddServicePartController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AddServicePartController>(
        builder: (addServicePartController) {
      bool isAddingService = widget.isAddingService;
      return GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Scaffold(
          appBar: AppBar(
            title: Text(isAddingService ? "Add New Service" : "Add New Part"),
            elevation: 0.5,
            backgroundColor: colorsConstants.whiteColor,
            shadowColor: colorsConstants.whiteColor,
            surfaceTintColor: colorsConstants.whiteColor,
          ),
          body: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: isAddingService
                        ? Column(
                            children: [
                              HeightBox(20.h),
                              CustomTextField(
                                hintText: "Service Name*",
                                contentPadding: EdgeInsets.all(5.sp),
                                controller: addServicePartController
                                    .serviceNameEditingController,
                                filled: true,
                                fillColor: colorsConstants.whiteColor,
                                textInputAction: TextInputAction.next,
                                border: const UnderlineInputBorder(
                                  borderSide: BorderSide.none,
                                ),
                                enabledBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                    color: colorsConstants.slateGrey,
                                    width: 1.0,
                                  ),
                                ),
                                focusedBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                      color: colorsConstants.primaryRed,
                                      width: 2.0),
                                ),
                              ),
                              HeightBox(20.h),
                              CustomTextField(
                                hintText: "Price (MRP)*",
                                contentPadding: EdgeInsets.all(5.sp),
                                controller: addServicePartController
                                    .mrpEditingController,
                                filled: true,
                                keyboardType: TextInputType.number,
                                textInputAction: TextInputAction.next,
                                fillColor: colorsConstants.whiteColor,
                                border: const UnderlineInputBorder(
                                  borderSide: BorderSide.none,
                                ),
                                enabledBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                    color: colorsConstants.slateGrey,
                                    width: 1.0,
                                  ),
                                ),
                                focusedBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                      color: colorsConstants.primaryRed,
                                      width: 2.0),
                                ),
                              ),
                              HeightBox(20.h),
                              // CustomTextField(
                              //   hintText: "Service Number",
                              //   contentPadding: EdgeInsets.all(5.sp),
                              //   controller: addServicePartController
                              //       .serviceNumberEditingController,
                              //   filled: true,
                              //   fillColor: colorsConstants.whiteColor,
                              //   textInputAction: TextInputAction.next,
                              //   border: const UnderlineInputBorder(
                              //     borderSide: BorderSide.none,
                              //   ),
                              //   enabledBorder: UnderlineInputBorder(
                              //     borderSide: BorderSide(
                              //       color: colorsConstants.slateGrey,
                              //       width: 1.0,
                              //     ),
                              //   ),
                              //   focusedBorder: UnderlineInputBorder(
                              //     borderSide: BorderSide(
                              //         color: colorsConstants.primaryRed,
                              //         width: 2.0),
                              //   ),
                              // ),
                              // HeightBox(20.h),
                              // CustomTextField(
                              //   hintText: "Service Category",
                              //   contentPadding: EdgeInsets.all(5.sp),
                              //   controller: addServicePartController
                              //       .serviceCategoryEditingController,
                              //   readOnly: true,
                              //   onTap: () {
                              //     addServicePartController
                              //         .categoriesOverlayPortalController
                              //         .show();
                              //   },
                              //   filled: true,
                              //   fillColor: colorsConstants.whiteColor,
                              //   border: const UnderlineInputBorder(
                              //     borderSide: BorderSide.none,
                              //   ),
                              //   enabledBorder: UnderlineInputBorder(
                              //     borderSide: BorderSide(
                              //       color: colorsConstants.slateGrey,
                              //       width: 1.0,
                              //     ),
                              //   ),
                              //   focusedBorder: UnderlineInputBorder(
                              //     borderSide: BorderSide(
                              //         color: colorsConstants.primaryRed,
                              //         width: 2.0),
                              //   ),
                              // ),
                              // HeightBox(20.h),
                              DropdownButtonHideUnderline(
                                child: DropdownButtonFormField(
                                  decoration: InputDecoration(
                                    hintText: 'Gst Rate (in %)*',
                                    filled: true,
                                    fillColor: colorsConstants.whiteColor,
                                    border: const UnderlineInputBorder(
                                      borderSide: BorderSide.none,
                                    ),
                                    enabledBorder: UnderlineInputBorder(
                                      borderSide: BorderSide(
                                        color: colorsConstants.slateGrey,
                                        width: 1.0,
                                      ),
                                    ),
                                    focusedBorder: UnderlineInputBorder(
                                      borderSide: BorderSide(
                                          color: colorsConstants.primaryRed,
                                          width: 2.0),
                                    ),
                                  ),
                                  items: [
                                    ...List.generate(
                                      addServicePartController
                                          .servicePartsgstRates.length,
                                      (index) {
                                        return DropdownMenuItem(
                                            value: addServicePartController
                                                .servicePartsgstRates[index],
                                            child: Text(addServicePartController
                                                .servicePartsgstRates[index]));
                                      },
                                    )
                                  ],
                                  onChanged: (value) {
                                    addServicePartController
                                        .selectedservicePartsGstRate = value;
                                    addServicePartController.update();
                                  },
                                ),
                              ),
                              if (!isAddingService) ...[
                                HeightBox(20.h),
                                RowWithRadioButton(
                                  label:
                                      "Generic Part (Applicable for all Models)",
                                  isSelected: addServicePartController
                                          .selectedPartType ==
                                      PartType.generic,
                                  onTap: () {
                                    addServicePartController.togglePartType(
                                        partType: PartType.generic);
                                  },
                                ),
                                HeightBox(20.h),
                                RowWithRadioButton(
                                  label:
                                      "Specific Part for Vehicle Brand(s) and Model(s)",
                                  isSelected: addServicePartController
                                          .selectedPartType ==
                                      PartType.specific,
                                  onTap: () {
                                    addServicePartController.togglePartType(
                                        partType: PartType.specific);
                                  },
                                ),
                              ],

                              if (addServicePartController.selectedPartType ==
                                  PartType.specific) ...[
                                HeightBox(20.h),
                                Row(
                                  children: [
                                    Expanded(
                                      flex: 2,
                                      child: Text(
                                        "Choose Applicable Brands",
                                        style: TextStyle(
                                            color: colorsConstants.hintGrey,
                                            fontSize: 16.sp),
                                      ),
                                    ),
                                    WidthBox(5.w),
                                    Expanded(
                                      child: CustomTextField(
                                        hintText: "",
                                        contentPadding: EdgeInsets.all(5.sp),
                                        controller: addServicePartController
                                            .applicableBrandsEditingController,
                                        readOnly: true,
                                        onTap: () {
                                          addServicePartController
                                              .makeOverlayPortalController
                                              .show();
                                        },
                                        filled: true,
                                        fillColor: colorsConstants.whiteColor,
                                        border: const UnderlineInputBorder(
                                          borderSide: BorderSide.none,
                                        ),
                                        enabledBorder: UnderlineInputBorder(
                                          borderSide: BorderSide(
                                            color: colorsConstants.slateGrey,
                                            width: 1.0,
                                          ),
                                        ),
                                        focusedBorder: UnderlineInputBorder(
                                          borderSide: BorderSide(
                                              color: colorsConstants.primaryRed,
                                              width: 2.0),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                HeightBox(20.h),
                                Row(
                                  children: [
                                    Expanded(
                                      flex: 2,
                                      child: Text(
                                        "Choose Applicable Models",
                                        style: TextStyle(
                                            color: colorsConstants.hintGrey,
                                            fontSize: 16.sp),
                                      ),
                                    ),
                                    WidthBox(5.w),
                                    Expanded(
                                      child: CustomTextField(
                                        hintText: "",
                                        contentPadding: EdgeInsets.all(5.sp),
                                        controller: addServicePartController
                                            .applicableModelsEditingController,
                                        readOnly: true,
                                        onTap: () {
                                          addServicePartController
                                              .modelOverlayPortalController
                                              .show();
                                        },
                                        filled: true,
                                        fillColor: colorsConstants.whiteColor,
                                        border: const UnderlineInputBorder(
                                          borderSide: BorderSide.none,
                                        ),
                                        enabledBorder: UnderlineInputBorder(
                                          borderSide: BorderSide(
                                            color: colorsConstants.slateGrey,
                                            width: 1.0,
                                          ),
                                        ),
                                        focusedBorder: UnderlineInputBorder(
                                          borderSide: BorderSide(
                                            color: colorsConstants.primaryRed,
                                            width: 1.0,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ]
                            ],
                          )
                        : Column(
                            children: [
                              HeightBox(20.h),
                              CustomTextField(
                                hintText: "Part Name*",
                                contentPadding: EdgeInsets.all(5.sp),
                                controller: addServicePartController
                                    .partNameEditingController,
                                filled: true,
                                fillColor: colorsConstants.whiteColor,
                                textInputAction: TextInputAction.next,
                                border: const UnderlineInputBorder(
                                  borderSide: BorderSide.none,
                                ),
                                enabledBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                    color: colorsConstants.slateGrey,
                                    width: 1.0,
                                  ),
                                ),
                                focusedBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                      color: colorsConstants.primaryRed,
                                      width: 2.0),
                                ),
                              ),
                              HeightBox(20.h),
                              CustomTextField(
                                hintText: "MRP*",
                                contentPadding: EdgeInsets.all(5.sp),
                                controller: addServicePartController
                                    .mrpEditingController,
                                filled: true,
                                keyboardType: TextInputType.number,
                                textInputAction: TextInputAction.next,
                                fillColor: colorsConstants.whiteColor,
                                border: const UnderlineInputBorder(
                                  borderSide: BorderSide.none,
                                ),
                                enabledBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                    color: colorsConstants.slateGrey,
                                    width: 1.0,
                                  ),
                                ),
                                focusedBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                      color: colorsConstants.primaryRed,
                                      width: 2.0),
                                ),
                              ),
                              HeightBox(20.h),
                              CustomTextField(
                                hintText: "Purchase price*",
                                contentPadding: EdgeInsets.all(5.sp),
                                controller: addServicePartController
                                    .purchasePriceEditingController,
                                filled: true,
                                fillColor: colorsConstants.whiteColor,
                                keyboardType: TextInputType.number,
                                textInputAction: TextInputAction.next,
                                border: const UnderlineInputBorder(
                                  borderSide: BorderSide.none,
                                ),
                                enabledBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                    color: colorsConstants.slateGrey,
                                    width: 1.0,
                                  ),
                                ),
                                focusedBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                      color: colorsConstants.primaryRed,
                                      width: 2.0),
                                ),
                              ),
                              HeightBox(20.h),
                              DropdownButtonHideUnderline(
                                child: DropdownButtonFormField(
                                  decoration: InputDecoration(
                                    hintText: 'Gst Rate (in %)*',
                                    filled: true,
                                    fillColor: colorsConstants.whiteColor,
                                    border: const UnderlineInputBorder(
                                      borderSide: BorderSide.none,
                                    ),
                                    enabledBorder: UnderlineInputBorder(
                                      borderSide: BorderSide(
                                        color: colorsConstants.slateGrey,
                                        width: 1.0,
                                      ),
                                    ),
                                    focusedBorder: UnderlineInputBorder(
                                      borderSide: BorderSide(
                                          color: colorsConstants.primaryRed,
                                          width: 2.0),
                                    ),
                                  ),
                                  items: [
                                    ...List.generate(
                                      addServicePartController
                                          .servicePartsgstRates.length,
                                      (index) {
                                        return DropdownMenuItem(
                                            value: addServicePartController
                                                .servicePartsgstRates[index],
                                            child: Text(addServicePartController
                                                .servicePartsgstRates[index]));
                                      },
                                    )
                                  ],
                                  onChanged: (value) {
                                    addServicePartController
                                        .selectedservicePartsGstRate = value;
                                    addServicePartController.update();
                                  },
                                ),
                              ),
                              HeightBox(20.h),
                              CustomTextField(
                                hintText: "Part Number*",
                                contentPadding: EdgeInsets.all(5.sp),
                                controller: addServicePartController
                                    .partNoEditingController,
                                textInputAction: TextInputAction.next,
                                filled: true,
                                fillColor: colorsConstants.whiteColor,
                                border: const UnderlineInputBorder(
                                  borderSide: BorderSide.none,
                                ),
                                enabledBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                    color: colorsConstants.slateGrey,
                                    width: 1.0,
                                  ),
                                ),
                                focusedBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                      color: colorsConstants.primaryRed,
                                      width: 2.0),
                                ),
                              ),
                              HeightBox(20.h),
                              CustomTextField(
                                hintText: "Vendor Name*",
                                contentPadding: EdgeInsets.all(5.sp),
                                controller: addServicePartController
                                    .vendorEditingController,
                                textInputAction: TextInputAction.next,
                                filled: true,
                                fillColor: colorsConstants.whiteColor,
                                border: const UnderlineInputBorder(
                                  borderSide: BorderSide.none,
                                ),
                                enabledBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                    color: colorsConstants.slateGrey,
                                    width: 1.0,
                                  ),
                                ),
                                focusedBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                      color: colorsConstants.primaryRed,
                                      width: 2.0),
                                ),
                              ),
                              HeightBox(20.h),
                              CustomTextField(
                                hintText: "Part Category",
                                contentPadding: EdgeInsets.all(5.sp),
                                controller: addServicePartController
                                    .partCategoryEditingController,
                                readOnly: true,
                                onTap: () {
                                  addServicePartController
                                      .categoriesOverlayPortalController
                                      .show();
                                },
                                filled: true,
                                fillColor: colorsConstants.whiteColor,
                                border: const UnderlineInputBorder(
                                  borderSide: BorderSide.none,
                                ),
                                enabledBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                    color: colorsConstants.slateGrey,
                                    width: 1.0,
                                  ),
                                ),
                                focusedBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                      color: colorsConstants.primaryRed,
                                      width: 2.0),
                                ),
                              ),
                              HeightBox(20.h),
                              CustomTextField(
                                hintText: "Select Manufacturer",
                                contentPadding: EdgeInsets.all(5.sp),
                                controller: addServicePartController
                                    .manufacturerEditingController,
                                readOnly: true,
                                onTap: () {
                                  addServicePartController
                                      .manufacturerOverlayPortalController
                                      .show();
                                },
                                filled: true,
                                fillColor: colorsConstants.whiteColor,
                                border: const UnderlineInputBorder(
                                  borderSide: BorderSide.none,
                                ),
                                enabledBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                    color: colorsConstants.slateGrey,
                                    width: 1.0,
                                  ),
                                ),
                                focusedBorder: UnderlineInputBorder(
                                  borderSide: BorderSide(
                                      color: colorsConstants.primaryRed,
                                      width: 2.0),
                                ),
                              ),
                              HeightBox(10.h),
                              Row(
                                children: [
                                  Checkbox(
                                    value: addServicePartController.isOil,
                                    activeColor: colorsConstants.primaryRed,
                                    materialTapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                    onChanged: (value) {
                                      addServicePartController.toggleIsOil(
                                          value: value);
                                    },
                                  ),
                                  const Text("Is Oil?")
                                ],
                              ),
                              HeightBox(20.h),
                              RowWithRadioButton(
                                label:
                                    "Generic Part (Applicable for all Models)",
                                isSelected:
                                    addServicePartController.selectedPartType ==
                                        PartType.generic,
                                onTap: () {
                                  addServicePartController.togglePartType(
                                      partType: PartType.generic);
                                },
                              ),
                              HeightBox(20.h),
                              RowWithRadioButton(
                                label:
                                    "Specific Part for Vehicle Brand(s) and Model(s)",
                                isSelected:
                                    addServicePartController.selectedPartType ==
                                        PartType.specific,
                                onTap: () {
                                  addServicePartController.togglePartType(
                                      partType: PartType.specific);
                                },
                              ),
                              if (addServicePartController.selectedPartType ==
                                  PartType.specific) ...[
                                HeightBox(20.h),
                                Row(
                                  children: [
                                    Expanded(
                                      flex: 2,
                                      child: Text(
                                        "Choose Applicable Brands",
                                        style: TextStyle(
                                            color: colorsConstants.hintGrey,
                                            fontSize: 16.sp),
                                      ),
                                    ),
                                    WidthBox(5.w),
                                    Expanded(
                                      child: CustomTextField(
                                        hintText: "",
                                        contentPadding: EdgeInsets.all(5.sp),
                                        controller: addServicePartController
                                            .applicableBrandsEditingController,
                                        readOnly: true,
                                        onTap: () {
                                          addServicePartController
                                              .makeOverlayPortalController
                                              .show();
                                        },
                                        filled: true,
                                        fillColor: colorsConstants.whiteColor,
                                        border: const UnderlineInputBorder(
                                          borderSide: BorderSide.none,
                                        ),
                                        enabledBorder: UnderlineInputBorder(
                                          borderSide: BorderSide(
                                            color: colorsConstants.slateGrey,
                                            width: 1.0,
                                          ),
                                        ),
                                        focusedBorder: UnderlineInputBorder(
                                          borderSide: BorderSide(
                                              color: colorsConstants.primaryRed,
                                              width: 2.0),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                HeightBox(20.h),
                                Row(
                                  children: [
                                    Expanded(
                                      flex: 2,
                                      child: Text(
                                        "Choose Applicable Models",
                                        style: TextStyle(
                                            color: colorsConstants.hintGrey,
                                            fontSize: 16.sp),
                                      ),
                                    ),
                                    WidthBox(5.w),
                                    Expanded(
                                      child: CustomTextField(
                                        hintText: "",
                                        contentPadding: EdgeInsets.all(5.sp),
                                        controller: addServicePartController
                                            .applicableModelsEditingController,
                                        readOnly: true,
                                        onTap: () {
                                          addServicePartController
                                              .modelOverlayPortalController
                                              .show();
                                        },
                                        filled: true,
                                        fillColor: colorsConstants.whiteColor,
                                        border: const UnderlineInputBorder(
                                          borderSide: BorderSide.none,
                                        ),
                                        enabledBorder: UnderlineInputBorder(
                                          borderSide: BorderSide(
                                            color: colorsConstants.slateGrey,
                                            width: 1.0,
                                          ),
                                        ),
                                        focusedBorder: UnderlineInputBorder(
                                          borderSide: BorderSide(
                                            color: colorsConstants.primaryRed,
                                            width: 1.0,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                              HeightBox(20.h),
                              Row(
                                children: [
                                  Checkbox(
                                    value: addServicePartController
                                        .isManageInventory,
                                    activeColor: colorsConstants.primaryRed,
                                    materialTapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                    onChanged: (value) {
                                      addServicePartController
                                          .toggleManageInventory();
                                    },
                                  ),
                                  const Text("Manage Inventory")
                                ],
                              ),
                              if (addServicePartController
                                  .isManageInventory) ...[
                                HeightBox(20.h),
                                CustomTextField(
                                  hintText: "Rack Id",
                                  contentPadding: EdgeInsets.all(5.sp),
                                  controller: addServicePartController
                                      .rackIdEditingController,
                                  filled: true,
                                  fillColor: colorsConstants.whiteColor,
                                  textInputAction: TextInputAction.next,
                                  border: const UnderlineInputBorder(
                                    borderSide: BorderSide.none,
                                  ),
                                  enabledBorder: UnderlineInputBorder(
                                    borderSide: BorderSide(
                                      color: colorsConstants.slateGrey,
                                      width: 1.0,
                                    ),
                                  ),
                                  focusedBorder: UnderlineInputBorder(
                                    borderSide: BorderSide(
                                      color: colorsConstants.primaryRed,
                                      width: 1.0,
                                    ),
                                  ),
                                ),
                                HeightBox(20.h),
                                Row(
                                  children: [
                                    Expanded(
                                      child: CustomTextField(
                                        hintText: "Current Stock",
                                        textInputAction: TextInputAction.next,
                                        contentPadding: EdgeInsets.all(5.sp),
                                        controller: addServicePartController
                                            .currentStockEditingController,
                                        filled: true,
                                        fillColor: colorsConstants.whiteColor,
                                        keyboardType: TextInputType.number,
                                        border: const UnderlineInputBorder(
                                          borderSide: BorderSide.none,
                                        ),
                                        enabledBorder: UnderlineInputBorder(
                                          borderSide: BorderSide(
                                            color: colorsConstants.slateGrey,
                                            width: 1.0,
                                          ),
                                        ),
                                        focusedBorder: UnderlineInputBorder(
                                          borderSide: BorderSide(
                                            color: colorsConstants.primaryRed,
                                            width: 1.0,
                                          ),
                                        ),
                                      ),
                                    ),
                                    WidthBox(5.w),
                                    Expanded(
                                      child: CustomTextField(
                                        hintText: "Min Stock",
                                        textInputAction: TextInputAction.next,
                                        contentPadding: EdgeInsets.all(5.sp),
                                        keyboardType: TextInputType.number,
                                        controller: addServicePartController
                                            .minStockEditingController,
                                        filled: true,
                                        fillColor: colorsConstants.whiteColor,
                                        border: const UnderlineInputBorder(
                                          borderSide: BorderSide.none,
                                        ),
                                        enabledBorder: UnderlineInputBorder(
                                          borderSide: BorderSide(
                                            color: colorsConstants.slateGrey,
                                            width: 1.0,
                                          ),
                                        ),
                                        focusedBorder: UnderlineInputBorder(
                                          borderSide: BorderSide(
                                            color: colorsConstants.primaryRed,
                                            width: 1.0,
                                          ),
                                        ),
                                      ),
                                    ),
                                    WidthBox(5.w),
                                    Expanded(
                                      child: CustomTextField(
                                        hintText: "Max Stock",
                                        textInputAction: TextInputAction.next,
                                        contentPadding: EdgeInsets.all(5.sp),
                                        keyboardType: TextInputType.number,
                                        controller: addServicePartController
                                            .maxStockEditingController,
                                        filled: true,
                                        fillColor: colorsConstants.whiteColor,
                                        border: const UnderlineInputBorder(
                                          borderSide: BorderSide.none,
                                        ),
                                        enabledBorder: UnderlineInputBorder(
                                          borderSide: BorderSide(
                                            color: colorsConstants.slateGrey,
                                            width: 1.0,
                                          ),
                                        ),
                                        focusedBorder: UnderlineInputBorder(
                                          borderSide: BorderSide(
                                            color: colorsConstants.primaryRed,
                                            width: 1.0,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                              HeightBox(50.h),
                            ],
                          ),
                  ),
                ),
                Container(
                    padding: EdgeInsets.symmetric(vertical: 20.h),
                    width: double.maxFinite,
                    child: Container(
                      decoration: BoxDecoration(boxShadow: Constants.boxShadow),
                      child: ElevatedButton(
                        onPressed: () async {
                          if (submitted) {
                            return;
                          }
                          submitted = true;
                          setState(() {});
                          if (isAddingService) {
                            if (addServicePartController
                                    .serviceNameEditingController
                                    .text
                                    .isEmpty ||
                                (addServicePartController
                                    .mrpEditingController.text.isEmpty) ||
                                (addServicePartController
                                        .selectedservicePartsGstRate?.isEmpty ??
                                    true)) {
                              Utils.showSnackBar(
                                  title: "Fields with \"*\" are mandatory");
                              submitted = false;
                              return;
                            }
                            if (Get.find<RepairDetailsController>()
                                    .services
                                    .firstWhereOrNull((element) =>
                                        element.name?.toLowerCase().trim() ==
                                        addServicePartController
                                            .serviceNameEditingController.text
                                            .toLowerCase()
                                            .trim()) !=
                                null) {
                              Utils.showSnackBar(
                                  title: "Service With the same name exists!!");
                              submitted = false;
                              return;
                            }
                          } else {
                            if (addServicePartController
                                    .partNameEditingController.text.isEmpty ||
                                addServicePartController
                                    .purchasePriceEditingController
                                    .text
                                    .isEmpty ||
                                addServicePartController
                                    .partNoEditingController.text.isEmpty ||
                                addServicePartController
                                    .vendorEditingController.text.isEmpty ||
                                addServicePartController
                                    .mrpEditingController.text.isEmpty ||
                                (addServicePartController
                                        .selectedservicePartsGstRate?.isEmpty ??
                                    true)) {
                              Utils.showSnackBar(
                                  title: "Fields with \"*\" are mandatory");
                              submitted = false;
                              return;
                            }
                          }
                          await addServicePartController
                              .addServicePartInDatabase(
                            context,
                            isAddingService: isAddingService,
                          );

                          if (context.mounted) Navigator.pop(context);

                          submitted = false;
                          setState(() {});
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: colorsConstants.primaryRed,
                          foregroundColor: colorsConstants.whiteColor,
                          shape: ContinuousRectangleBorder(
                            borderRadius: BorderRadius.circular(5.r),
                          ),
                        ),
                        child: submitted
                            ? CircularProgressIndicator(
                                color: Colors.white,
                              )
                            : Text(
                                isAddingService ? "Add Service" : "Add Part"),
                      ),
                    )),
                OverlayPortal(
                  controller: addServicePartController
                      .categoriesOverlayPortalController,
                  overlayChildBuilder: (BuildContext context) {
                    return CustomOverlay(
                      title: "Select Category",
                      dataSource: addServicePartController.categories,
                      onBackPress: () {
                        addServicePartController
                            .categoriesOverlayPortalController
                            .hide();
                      },
                      onSelected: (String selectedValue) {
                        addServicePartController
                            .categoriesOverlayPortalController
                            .hide();
                        if (isAddingService) {
                          addServicePartController
                              .serviceCategoryEditingController
                              .text = selectedValue;
                        } else {
                          addServicePartController.partCategoryEditingController
                              .text = selectedValue;
                        }
                      },
                      addNewOnPress: () {
                        showDialog(
                          context: context,
                          builder: (context) {
                            TextEditingController controller =
                                TextEditingController();

                            return AlertDialog(
                              content: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    "Add Category",
                                    style: TextStyle(fontSize: 20.sp),
                                  ),
                                  HeightBox(10.h),
                                  CustomTextField(
                                    hintText: "Category Name",
                                    controller: controller,
                                  ),
                                  HeightBox(20.h),
                                  PrimaryButton(
                                      onPress: () async {
                                        await addServicePartController
                                            .addNewCategoryInDatabase(
                                          categoryName: controller.text,
                                        );
                                        if (context.mounted) {
                                          Navigator.pop(context);
                                        }
                                      },
                                      title: "Add"),
                                ],
                              ),
                            );
                          },
                        );
                      },
                      // addNewActionWidget: PrimaryButton(
                      //   onPress: () {
                      //     showDialog(
                      //       context: context,
                      //       builder: (context) {
                      //         TextEditingController controller =
                      //             TextEditingController();

                      //         return AlertDialog(
                      //           content: Column(
                      //             mainAxisSize: MainAxisSize.min,
                      //             children: [
                      //               Text(
                      //                 "Add Category",
                      //                 style: TextStyle(fontSize: 20.sp),
                      //               ),
                      //               HeightBox(10.h),
                      //               CustomTextField(
                      //                 hintText: "Category Name",
                      //                 controller: controller,
                      //               ),
                      //               HeightBox(20.h),
                      //               PrimaryButton(
                      //                   onPress: () async {
                      //                     await addServicePartController
                      //                         .addNewCategoryInDatabase(
                      //                       categoryName: controller.text,
                      //                     );
                      //                     if (context.mounted) {
                      //                       Navigator.pop(context);
                      //                     }
                      //                   },
                      //                   title: "Add"),
                      //             ],
                      //           ),
                      //         );
                      //       },
                      //     );
                      //   },
                      //   title: "Add Category",
                      // ),
                    );
                  },
                ),
                OverlayPortal(
                  controller: addServicePartController
                      .manufacturerOverlayPortalController,
                  overlayChildBuilder: (BuildContext context) {
                    return CustomOverlay(
                      title: "Select Manufacturer",
                      dataSource: addServicePartController.manufacturers,
                      onBackPress: () {
                        addServicePartController
                            .manufacturerOverlayPortalController
                            .hide();
                      },
                      onSelected: (String selectedValue) {
                        addServicePartController
                            .manufacturerOverlayPortalController
                            .hide();
                        addServicePartController
                            .manufacturerEditingController.text = selectedValue;
                      },
                      addNewOnPress: () {
                        showDialog(
                          context: context,
                          builder: (context) {
                            TextEditingController controller =
                                TextEditingController();

                            return AlertDialog(
                              content: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    "Add Manufacturer",
                                    style: TextStyle(fontSize: 20.sp),
                                  ),
                                  HeightBox(10.h),
                                  CustomTextField(
                                    hintText: "Manufacturer Name",
                                    controller: controller,
                                  ),
                                  HeightBox(20.h),
                                  PrimaryButton(
                                      onPress: () async {
                                        await addServicePartController
                                            .addNewManufacturerInDatabase(
                                          manufacturerName: controller.text,
                                        );
                                        if (context.mounted) {
                                          Navigator.pop(context);
                                        }
                                      },
                                      title: "Add"),
                                ],
                              ),
                            );
                          },
                        );
                      },
                      // addNewActionWidget: PrimaryButton(
                      //   onPress: () {
                      //     showDialog(
                      //       context: context,
                      //       builder: (context) {
                      //         TextEditingController controller =
                      //             TextEditingController();

                      //         return AlertDialog(
                      //           content: Column(
                      //             mainAxisSize: MainAxisSize.min,
                      //             children: [
                      //               Text(
                      //                 "Add Manufacturer",
                      //                 style: TextStyle(fontSize: 20.sp),
                      //               ),
                      //               HeightBox(10.h),
                      //               CustomTextField(
                      //                 hintText: "Manufacturer Name",
                      //                 controller: controller,
                      //               ),
                      //               HeightBox(20.h),
                      //               PrimaryButton(
                      //                   onPress: () async {
                      //                     await addServicePartController
                      //                         .addNewManufacturerInDatabase(
                      //                       manufacturerName: controller.text,
                      //                     );
                      //                     if (context.mounted) {
                      //                       Navigator.pop(context);
                      //                     }
                      //                   },
                      //                   title: "Add"),
                      //             ],
                      //           ),
                      //         );
                      //       },
                      //     );
                      //   },
                      //   title: "Add Manufacturer",
                      // ),
                    );
                  },
                ),
                OverlayPortal(
                  controller:
                      addServicePartController.makeOverlayPortalController,
                  overlayChildBuilder: (BuildContext context) {
                    return CustomOverlay(
                      allowMultiSelection: true,
                      onBackPress: () {
                        addServicePartController.makeOverlayPortalController
                            .hide();
                      },
                      onMultiSelectSubmit: (List<String> selectedBrnads) {
                        addServicePartController
                            .applicableBrandsEditingController
                            .text = selectedBrnads.length.toString();
                        addServicePartController.selectedApplicableBrands =
                            selectedBrnads;
                        addServicePartController
                            .applicableModelsEditingController.text = "";
                        addServicePartController.makeOverlayPortalController
                            .hide();
                      },
                      dataSource:
                          addServicePartController.vehiclesDataSource.fold(
                        [],
                        (previousValue, element) {
                          previousValue.add(element.company ?? "");

                          return previousValue;
                        },
                      ),
                    );
                  },
                ),
                OverlayPortal(
                  controller:
                      addServicePartController.modelOverlayPortalController,
                  overlayChildBuilder: (BuildContext context) {
                    return CustomOverlay(
                      allowMultiSelection: true,
                      onBackPress: () {
                        addServicePartController.modelOverlayPortalController
                            .hide();
                      },
                      onMultiSelectSubmit: (List<String> selectedModels) {
                        addServicePartController
                            .applicableModelsEditingController
                            .text = selectedModels.length.toString();

                        addServicePartController.selectedApplicableModels =
                            selectedModels;

                        addServicePartController.modelOverlayPortalController
                            .hide();
                      },
                      dataSource:
                          addServicePartController.vehiclesDataSource.fold(
                        [],
                        (previousValue, element) {
                          if (addServicePartController.selectedApplicableBrands
                              .contains(element.company)) {
                            previousValue.addAll(element.models ?? []);
                          }

                          return previousValue;
                        },
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}

class RowWithRadioButton extends StatelessWidget {
  const RowWithRadioButton(
      {super.key, required this.label, this.onTap, this.isSelected});

  final String label;
  final void Function()? onTap;
  final bool? isSelected;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(2.sp),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: colorsConstants.primaryRed,
                width: 2.sp,
              ),
            ),
            child: Container(
              padding: EdgeInsets.all(5.sp),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected != null && isSelected!
                    ? colorsConstants.primaryRed
                    : null,
              ),
            ),
          ),
          WidthBox(10.w),
          Expanded(
            child: Text(
              label,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
