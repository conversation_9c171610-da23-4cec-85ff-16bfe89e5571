import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/stock_model.dart';
import 'package:speed_force_franchise/models/vendor_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class AddStockController extends GetxController {
  AddStockController({required this.homeScreenController});

  final HomeScreenController homeScreenController;

  OverlayPortalController selectPartsOverlayPortalController =
      OverlayPortalController();

  TextEditingController preferredVendorTextEditingController =
      TextEditingController();

  TextEditingController priceWithoutGSTTextEditingController =
      TextEditingController();
  TextEditingController mrpTextEditingController = TextEditingController();
  TextEditingController rackIdTextEditingController = TextEditingController();
  TextEditingController currentStockTextEditingController =
      TextEditingController();
  TextEditingController minStockTextEditingController = TextEditingController();
  TextEditingController maxStockTextEditingController = TextEditingController();
  TextEditingController commentTextEditingController = TextEditingController();

  List<PartModel> parts = [];

  PartModel? selectedPart;

  bool isLoading = false;

  StockModel stockModel = StockModel();
  VendorModel? selectedVendor;

  Future<void> fetchParts() async {
    parts.clear();
    isLoading = true;
    update();
    QuerySnapshot<Map<String, dynamic>> partsCollection =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.parts.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .get();

    for (var part in partsCollection.docs) {
      PartModel partModel = PartModel.fromMap(part.data());
      parts.add(partModel);
    }
    isLoading = false;
    update();
  }

  bool verifyFields() {
    if (selectedVendor != null &&
        priceWithoutGSTTextEditingController.text.isNotEmpty &&
        mrpTextEditingController.text.isNotEmpty &&
        rackIdTextEditingController.text.isNotEmpty &&
        currentStockTextEditingController.text.isNotEmpty &&
        minStockTextEditingController.text.isNotEmpty &&
        maxStockTextEditingController.text.isNotEmpty) {
      return true;
    }
    return false;
  }

  Future<void> saveStockInDatabase() async {
    isLoading = true;

    stockModel.franchiseId = FirebaseAuth.instance.currentUser?.uid;
    stockModel.purchasePrice =
        double.tryParse(priceWithoutGSTTextEditingController.text);
    stockModel.mrp = double.tryParse(mrpTextEditingController.text);
    stockModel.rackId = rackIdTextEditingController.text;
    stockModel.currentStock =
        double.tryParse(currentStockTextEditingController.text);
    stockModel.minStock = double.tryParse(minStockTextEditingController.text);
    stockModel.maxStock = double.tryParse(maxStockTextEditingController.text);
    stockModel.comment = commentTextEditingController.text;
    stockModel.partId = selectedPart?.partId;
    stockModel.vendorId = selectedVendor?.vendorId;

    QuerySnapshot<Map<String, dynamic>> stockQuerySnapshot =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.stock.name)
            .where("partId", isEqualTo: stockModel.partId)
            .get();

    if (stockQuerySnapshot.docs.isEmpty) {
      DocumentReference<Map<String, dynamic>> newStock = await FirebaseFirestore
          .instance
          .collection(FirebaseCollections.stock.name)
          .add(stockModel.toMap());

      stockModel.stockId = newStock.id;
    } else {
      stockModel.stockId =
          StockModel.fromMap(stockQuerySnapshot.docs.first.data()).stockId;
    }

    FirebaseFirestore.instance
        .collection(FirebaseCollections.stock.name)
        .doc(stockModel.stockId)
        .set(stockModel.toMap());

    //OLD LOGIC
    // CollectionReference<Map<String, dynamic>> stockCollection =
    //     FirebaseFirestore.instance.collection(FirebaseCollections.stock.name);
    // DocumentReference<Map<String, dynamic>> newStock =
    //     await stockCollection.add(stockModel.toMap());
    // stockModel.stockId = newStock.id;
    // await stockCollection.doc(stockModel.stockId).set(stockModel.toMap());

    isLoading = false;
    update();
  }
}
