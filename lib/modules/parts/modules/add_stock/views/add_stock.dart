import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/vendor_model.dart';
import 'package:speed_force_franchise/modules/parts/modules/add_stock/controllers/add_stock_controller.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/orders/widgets/custom_overlay.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

class AddStock extends StatefulWidget {
  const AddStock({super.key});

  @override
  State<AddStock> createState() => _AddStockState();
}

class _AddStockState extends State<AddStock> {
  @override
  void initState() {
    super.initState();
    Get.put(
      AddStockController(
        homeScreenController: Get.find<HomeScreenController>(),
      )..fetchParts(),
    );
  }

  @override
  void dispose() {
    Get.delete<AddStockController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AddStockController>(
      builder: (addStockController) {
        return GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Scaffold(
            appBar: AppBar(
              title: const Text("Add Stock"),
              elevation: 0.5,
              backgroundColor: colorsConstants.whiteColor,
              shadowColor: colorsConstants.whiteColor,
              surfaceTintColor: colorsConstants.whiteColor,
            ),
            body: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          if (addStockController.selectedPart == null) ...[
                            HeightBox(20.h),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                InkWell(
                                  onTap: () {
                                    addStockController
                                        .selectPartsOverlayPortalController
                                        .show();
                                  },
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 10.w, vertical: 5.h),
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                        color: colorsConstants.primaryRed,
                                        width: 1,
                                      ),
                                    ),
                                    child: Text(
                                      "Select Part",
                                      style: TextStyle(
                                          color: colorsConstants.primaryRed),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                          if (addStockController.selectedPart != null) ...[
                            HeightBox(20.h),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  addStockController.selectedPart!.name ?? "",
                                  style: TextStyle(fontSize: 22.sp),
                                ),
                                WidthBox(10.w),
                                InkWell(
                                  onTap: () {
                                    addStockController
                                        .selectPartsOverlayPortalController
                                        .show();
                                  },
                                  child: const Icon(Icons.edit),
                                ),
                              ],
                            )
                          ],
                          HeightBox(10.h),
                          Container(
                            decoration: BoxDecoration(
                              boxShadow: Constants.boxShadow,
                            ),
                            child: CustomTextField(
                              hintText: "Preferred Vendor",
                              controller: addStockController
                                  .preferredVendorTextEditingController,
                              readOnly: true,
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 10.w, vertical: 10.h),
                              suffix: const Icon(CupertinoIcons.chevron_down),
                              filled: true,
                              fillColor: colorsConstants.whiteColor,
                              onTap: () {
                                showDialog(
                                  context: context,
                                  builder: (context) {
                                    return preferredVendorDialog(context);
                                  },
                                );
                              },
                              onChange: (value) {},
                              border: const OutlineInputBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(5),
                                ),
                                borderSide: BorderSide.none,
                              ),
                              enabledBorder: const OutlineInputBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(5),
                                ),
                                borderSide: BorderSide.none,
                              ),
                              focusedBorder: const OutlineInputBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(5),
                                ),
                                borderSide: BorderSide.none,
                              ),
                            ),
                          ),
                          HeightBox(16.h),
                          CustomTextField(
                            label: "Purchase price (without gst)",
                            hintText: "Price*",
                            keyboardType: TextInputType.number,
                            textInputAction: TextInputAction.next,
                            controller: addStockController
                                .priceWithoutGSTTextEditingController,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 10.w, vertical: 10.h),
                            fillColor: colorsConstants.whiteColor,
                            border: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.hintGrey),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.hintGrey),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.primaryRed),
                            ),
                          ),
                          HeightBox(16.h),
                          CustomTextField(
                            label: "MRP",
                            hintText: "MRP*",
                            keyboardType: TextInputType.number,
                            textInputAction: TextInputAction.next,
                            controller:
                                addStockController.mrpTextEditingController,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 10.w, vertical: 10.h),
                            fillColor: colorsConstants.whiteColor,
                            border: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(5),
                              ),
                            ),
                            enabledBorder: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(5),
                              ),
                            ),
                            focusedBorder: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(5),
                              ),
                            ),
                          ),
                          HeightBox(16.h),
                          CustomTextField(
                            label: "Rack Id",
                            hintText: "Rack Id*",
                            textInputAction: TextInputAction.next,
                            controller:
                                addStockController.rackIdTextEditingController,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 10.w, vertical: 10.h),
                            fillColor: colorsConstants.whiteColor,
                            border: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.hintGrey),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.hintGrey),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.primaryRed),
                            ),
                          ),
                          HeightBox(16.h),
                          CustomTextField(
                            label: "Current Stock",
                            hintText: "Current Stock*",
                            textInputAction: TextInputAction.next,
                            keyboardType: TextInputType.number,
                            controller: addStockController
                                .currentStockTextEditingController,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 10.w, vertical: 10.h),
                            fillColor: colorsConstants.whiteColor,
                            border: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.hintGrey),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.hintGrey),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.primaryRed),
                            ),
                          ),
                          HeightBox(16.h),
                          CustomTextField(
                            label: "Min Stock",
                            hintText: "Min Stock*",
                            textInputAction: TextInputAction.next,
                            keyboardType: TextInputType.number,
                            controller: addStockController
                                .minStockTextEditingController,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 10.w, vertical: 10.h),
                            fillColor: colorsConstants.whiteColor,
                            border: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.hintGrey),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.hintGrey),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.primaryRed),
                            ),
                          ),
                          HeightBox(16.h),
                          CustomTextField(
                            label: "Max Stock",
                            hintText: "Max Stock*",
                            textInputAction: TextInputAction.next,
                            keyboardType: TextInputType.number,
                            controller: addStockController
                                .maxStockTextEditingController,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 10.w, vertical: 10.h),
                            fillColor: colorsConstants.whiteColor,
                            border: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.hintGrey),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.hintGrey),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.primaryRed),
                            ),
                          ),
                          HeightBox(16.h),
                          CustomTextField(
                            hintText: "Comment",
                            textInputAction: TextInputAction.done,
                            maxLine: 3,
                            controller:
                                addStockController.commentTextEditingController,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 10.w, vertical: 10.h),
                            fillColor: colorsConstants.whiteColor,
                            border: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.hintGrey),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.hintGrey),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: const BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide:
                                  BorderSide(color: colorsConstants.primaryRed),
                            ),
                          ),
                          HeightBox(25.h)
                        ],
                      ),
                    ),
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: PrimaryButton(
                          onPress: () async {
                            if (addStockController.verifyFields()) {
                              await addStockController.saveStockInDatabase();
                              if (context.mounted) Navigator.pop(context);
                            } else {
                              Utils.showSnackBar(
                                  title: "* fields are mandatory");
                            }
                          },
                          title: "Save Stock",
                          margin: EdgeInsets.only(top: 10.h, bottom: 20.h),
                        ),
                      ),
                    ],
                  ),
                  OverlayPortal(
                    controller:
                        addStockController.selectPartsOverlayPortalController,
                    overlayChildBuilder: (BuildContext context) {
                      return CustomOverlay(
                        title: "Choose Parts",
                        onBackPress: () {
                          addStockController.selectPartsOverlayPortalController
                              .hide();
                        },
                        onSelected: (String selectedPart) {
                          debugPrint(selectedPart);
                          PartModel? partModel =
                              addStockController.parts.firstWhereOrNull((part) {
                            return part.name == selectedPart;
                          });
                          if (partModel != null) {
                            addStockController.selectedPart = partModel;
                          }
                          addStockController.update();
                          addStockController.selectPartsOverlayPortalController
                              .hide();
                        },
                        addNewOnPress: () {
                          moveToAddServicePartScreen(
                            context: context,
                            isAddingService: false,
                            revertCallback: (_) async {
                              await addStockController.fetchParts();
                              addStockController.update();
                            },
                          );
                        },
                        // addNewActionWidget: PrimaryButton(
                        //   onPress: () {
                        //     moveToAddServicePartScreen(
                        //       context: context,
                        //       isAddingService: false,
                        //       revertCallback: (_) async {
                        //         await addStockController.fetchParts();
                        //         addStockController.update();
                        //       },
                        //     );
                        //   },
                        //   title: "Add new part",
                        // ),
                        dataSource: addStockController.parts.fold(<String>[],
                            (previousValue, element) {
                          previousValue.add(element.name ?? "");
                          return previousValue;
                        }),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget preferredVendorDialog(BuildContext context) {
    AddStockController addStockController = Get.find<AddStockController>();
    return AlertDialog(
      contentPadding: EdgeInsets.zero,
      content: ConstrainedBox(
        constraints: BoxConstraints(maxHeight: 300.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            HeightBox(10.h),
            Text(
              "Preferred Vendor",
              style: TextStyle(fontSize: 24.sp),
            ),
            const Divider(),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: addStockController.homeScreenController.vendors.map(
                    (VendorModel vendorModel) {
                      return Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10.w),
                        child: InkWell(
                          onTap: () {
                            addStockController.selectedVendor = vendorModel;
                            addStockController
                                .preferredVendorTextEditingController
                                .text = vendorModel.name ?? "";
                            addStockController.update();
                            Navigator.pop(context);
                          },
                          child: Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(8.sp),
                                margin: EdgeInsets.symmetric(vertical: 10.sp),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: colorsConstants.primaryRed,
                                    width: 2.sp,
                                  ),
                                ),
                              ),
                              WidthBox(10.w),
                              Text(vendorModel.name ?? ""),
                            ],
                          ),
                        ),
                      );
                    },
                  ).toList(),
                ),
              ),
            ),
            const Divider(),
            PrimaryButton(
              onPress: () {
                moveToAddVendorScreen(
                    context: context,
                    revertCallback: (dynamic vendor) {
                      if (vendor != null) {
                        addStockController.selectedVendor =
                            (vendor as VendorModel);
                        addStockController
                                .preferredVendorTextEditingController.text =
                            addStockController.selectedVendor?.name ?? "";
                        Navigator.pop(context);
                        addStockController.update();
                        Get.find<HomeScreenController>().fetchVendors();
                      }
                    });
              },
              title: "Add New Vendor",
            ),
            HeightBox(10.h),
          ],
        ),
      ),
    );
  }
}
