import 'dart:convert';
import 'dart:io';
// import 'dart:typed_data';
import 'package:excel/excel.dart' as excel;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:csv/csv.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:printing/printing.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/stock_model.dart';
import 'package:speed_force_franchise/models/vendor_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/parts/controllers/parts_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import 'package:flutter/services.dart' show ByteData, Uint8List, rootBundle;

class CsvUploadController extends GetxController {
  List<List<dynamic>> csvData = [];
  List<dynamic> headers = [];
  List<List<dynamic>> rows = [];
  String? filePath;
  String? fileName;
  String? fileExtension;

  List<List<MapEntry<dynamic, String>>> rowsNew = [];

  bool isLoading = false;

  List<String> columnsToValidate = [
    // "PARTNAME",
    // "PARTNUMBER",
    // "MANUFACTURER",
    // "CATEGORY",
    // "MAXSTOCK",
    // "MINSTOCK",
    // "CURRENTSTOCK",
    // "MRP",
    // "PURCHASEPRICE",
    // "RACKID",
    // "VENDOR",
    // "APPLICABLEBRANDS",
    // "APPLICABLEMODELS",
    "PART_DESCRIPTION",
    "PART_NUMBER",
    "MANUFACTURER",
    "MOQ",
    "CURRENT_STOCK",
    "GST_RATE",
    "MRP",
    "PURCHASE_PRICE",
    "VENDOR"
  ];

  Future<void> pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        allowMultiple: false,
        type: FileType.custom,
        allowedExtensions: [
          'csv',
          'xlsx',
          'xls',
        ],
      );
      if (result == null) return;

      csvData.clear();
      headers.clear();
      rows.clear();
      rowsNew.clear();
      fileName = '';
      filePath = '';
      filePath = result.files.single.path!;
      fileName = filePath?.substring(filePath!.lastIndexOf("/") + 1);
      if (result.files.single.extension == 'csv') {
        // Future<Uint8List?> accessFile() async {
        //   String filePath = result.files.single.path!;
        //   try {
        //     ByteData fileData = await rootBundle.load(filePath);
        //     Uint8List bytes = fileData.buffer.asUint8List();
        //     print("File found: $filePath");
        //     return bytes;
        //   } catch (e) {
        //     print("File not found: $filePath");
        //     return null;
        //   }
        // }
        // Uint8List? bytes =
        //     await accessFile(); // Ensure the file extension is included
        // if (bytes == null) return;
        // String csvString = utf8.decode(bytes);
        // final fields = const CsvToListConverter().convert(csvString, eol: '\n');
        // final input = File(filePath!).openRead();

        final input = await File(filePath!).openRead().first;
        final fields =
            const CsvToListConverter().convert(utf8.decode(input), eol: '\n');

        // final fields = await input
        //     .transform(utf8.decoder)
        //     .transform(const CsvToListConverter())
        //     .toList();

        csvData = fields;
        headers = csvData[0];
        rows = csvData.skip(1).toList();
      }

      if (result.files.first.extension == 'xlsx' ||
          result.files.first.extension == 'xls') {
        final bytes = File(filePath!).readAsBytesSync();
        final excelFile = excel.Excel.decodeBytes(bytes);

        final sheet = excelFile.sheets.keys.first;
        final table = excelFile.tables[sheet]!;

        for (var i = 0; i < table.maxColumns; i++) {
          headers.add(table.rows[0][i]?.value.toString().trim());
        }
        for (var rowIndex = 1; rowIndex < table.maxRows; rowIndex++) {
          for (var columnIndex = 0;
              columnIndex < table.maxColumns;
              columnIndex++) {
            if (columnIndex == 0) rows.insert(rowIndex - 1, []);
            rows[rowIndex - 1].add(
                table.rows[rowIndex][columnIndex]?.value.toString().trim());
          }
        }
        // for (var sheet in excelFile.sheets.keys) {
        //   try {
        //     final table = excelFile.tables[sheet]!;
        //     // print(table.maxColumns);
        //     // print(table.maxRows);
        //     // print(table.rows[1][2]!.value);
        //     for (var rowIndex = 1;
        //         rowIndex <= (table.maxRows - 1);
        //         rowIndex++) {
        //       //change column from 3 to 2
        //       String? enrollId = table.rows[rowIndex][3]?.value.toString();
        //       if (enrollId != null && (enrollId != '')) {}
        //     }
        //   } on Exception catch (e) {
        //     debugPrint(e.toString());
        //   }
        // }
      }

      for (var row in rows) {
        List<MapEntry<dynamic, String>> rowEntry = [];
        rowEntry = row.mapIndexed(
          (index, element) {
            return MapEntry<dynamic, String>(
              element.toString(),
              headers[index],
            );
          },
        ).toList();

        rowsNew.add(rowEntry);
      }
    } catch (e) {
      debugPrint(e.toString());
    }

    update();
  }

  Future<void> uploadCsvToDatabase() async {
    try {
      // if (isLoading) {
      //   Utils.showSnackBar(title: "File is already being processed");
      //   return;
      // }

      // isLoading = true;
      // update();

      for (String header in headers) {
        header = header.trim();
        // header = header.replaceAll("_", "").trim();//------------------------------------------//Check
        if (
            // header.toUpperCase() == "APPLICABLEBRANDS" ||
            //   header.toUpperCase() == "APPLICABLEMODELS" ||
            header.toUpperCase() == "VENDOR") {
          continue;
        }
        print(header);
        if (header != "APPLICABLEMODELS") {
          if (!columnsToValidate.contains(header.toUpperCase())) {
            Utils.showSnackBar(title: "Invalid csv format or data");
            throw Exception("Invalid Format or Data");
          }
        }
      }
      final vendorslist = Get.find<HomeScreenController>()
          .vendors
          .where((element) =>
              element.franchiseId == FirebaseAuth.instance.currentUser?.uid)
          .toList();
      final partslist = Get.find<PartsController>()
          .stockRecords
          .where((element) =>
              element.$1.franchiseId == FirebaseAuth.instance.currentUser?.uid)
          .toList();
      final stocklist = Get.find<PartsController>()
          .stockRecords
          .where((element) =>
              element.$2.franchiseId == FirebaseAuth.instance.currentUser?.uid)
          .toList();
      int vendorsLength = vendorslist.length;
      int partsLength = partslist.length;
      int stockLength = stocklist.length;
      int parttimes = (partsLength / 450).ceil();
      int vendortimes = (vendorsLength / 450).ceil();
      int stocktimes = (stockLength / 450).ceil();

      List<int> loop = [parttimes, vendortimes, stocktimes];
      List<int> lengthloop = [partsLength, vendorsLength, stockLength];

      for (var i = 0; i < 3; i++) {
        print("------------------------------------------${loop[i]}");
        // i is for iterating throught the 3 colllection
        int starting = 0;
        int ending = 450;

        for (var j = 0; j < loop[i]; j++) {
          if (loop[i] == 1) {
            starting = 0;
            ending = lengthloop[i];
          }

          // j is for iterating throught a collection in 450 length seperation
          final batch = FirebaseFirestore.instance.batch();
          for (var k = starting; k < ending; k++) {
            if (i == 0) {
              final docref = FirebaseFirestore.instance
                  .collection(FirebaseCollections.parts.name)
                  .doc(partslist[k].$1.partId);
              batch.delete(docref);
            }
            if (i == 1) {
              final docref = FirebaseFirestore.instance
                  .collection(FirebaseCollections.vendors.name)
                  .doc(vendorslist[k].vendorId);
              batch.delete(docref);
            }
            if (i == 2) {
              final docref = FirebaseFirestore.instance
                  .collection(FirebaseCollections.stock.name)
                  .doc(stocklist[k].$2.stockId);

              batch.delete(docref);
            }

            //code for batch delete
          }
          await batch.commit();
          await Future.delayed(Duration(milliseconds: 300));
          if (j < (loop[i] - 1)) {
            int x = ending;
            starting = ending + 1;
            ending = x + 450;
          } else {
            starting = ending + 1;
            ending = lengthloop[i];
          }
        }
      }

      for (var row in rowsNew) {
        // print(row.last['VENDOR']);
        // String? id ;
        //  Get.find<HomeScreenController>()
        //     .vendors
        //     .firstWhereOrNull((element) =>
        //         element.name?.toUpperCase() ==
        //         extractProperty(row, 'VENDOR').toString().toUpperCase())
        //     ?.vendorId;
        // print(id);
        PartModel partModel = PartModel();

        partModel.franchiseId = FirebaseAuth.instance.currentUser?.uid;
        partModel.name = extractProperty(row, "PART_DESCRIPTION");
        partModel.partNumber = extractProperty(row, "PART_NUMBER");
        partModel.manufacturer = extractProperty(row, "MANUFACTURER");
        partModel.gstRate = num.tryParse(extractProperty(row, "GST_RATE"));
        // partModel.category = extractProperty(row, "CATEGORY");

        // partModel.applicableBrands =
        //     extractProperty(row, "APPLICABLEBRANDS")?.split(',');

        // partModel.applicableModels =
        //     extractProperty(row, "APPLICABLEMODELS")?.split(',');

        StockModel stockModel = StockModel();
        stockModel.franchiseId = FirebaseAuth.instance.currentUser?.uid;
        // stockModel.maxStock = double.parse(extractProperty(row, "MAXSTOCK"));
        stockModel.currentStock =
            double.parse(extractProperty(row, "CURRENT_STOCK"));
        stockModel.minStock = double.parse(extractProperty(row, "MOQ"));

        stockModel.mrp = double.parse(extractProperty(row, "MRP"));

        stockModel.purchasePrice =
            double.parse(extractProperty(row, "PURCHASE_PRICE"));

        // stockModel.rackId = extractProperty(row, "RACKID");

        VendorModel vendorModel = VendorModel();
        vendorModel.franchiseId = FirebaseAuth.instance.currentUser?.uid;
        vendorModel.name = extractProperty(row, "VENDOR");

        // QuerySnapshot<Map<String, dynamic>> vendorsQuerySnapshot =
        //     await FirebaseFirestore.instance
        //         .collection(FirebaseCollections.vendors.name)
        //         .where("name", isEqualTo: vendorModel.name)
        //         // .where("name", isEqualTo: partModel.name)
        //         .where("franchiseId", isEqualTo: vendorModel.franchiseId)
        //         .get();

        // if (vendorsQuerySnapshot.docs.isEmpty) {
        //   DocumentReference<Map<String, dynamic>> newPart =
        //       await FirebaseFirestore.instance
        //           .collection(FirebaseCollections.vendors.name)
        //           .add(vendorModel.toMap());

        //   vendorModel.vendorId = newPart.id;
        // } else {
        //   vendorModel.vendorId =
        //       VendorModel.fromMap(vendorsQuerySnapshot.docs.first.data())
        //           .vendorId;
        // }

        // if (id == null) {
        final vendorData = await (FirebaseFirestore.instance
            .collection(FirebaseCollections.vendors.name)
            .add(vendorModel.toMap()));
        vendorModel.vendorId = vendorData.id;
        await (FirebaseFirestore.instance
            .collection(FirebaseCollections.vendors.name)
            .doc(vendorData.id)
            .set(vendorModel.toMap()));
        //     final newVendor=VendorModel.fromJson(vendorData.);
        stockModel.vendorId = vendorData.id;

        await Get.find<HomeScreenController>().fetchVendors();
        // } else {
        //   stockModel.vendorId = id;
        // }
        // print(
        // "-------------------------*************--${vendorModel.vendorId}");
        // QuerySnapshot<Map<String, dynamic>> partsQuerySnapshot =
        //     await FirebaseFirestore.instance
        //         .collection(FirebaseCollections.parts.name)
        //         .where("partNumber", isEqualTo: partModel.partNumber)
        //         .where("name", isEqualTo: partModel.name)
        //         .where("franchiseId", isEqualTo: partModel.franchiseId)
        //         .get();

        // if (partsQuerySnapshot.docs.isEmpty) {
        DocumentReference<Map<String, dynamic>> newVendor =
            await FirebaseFirestore.instance
                .collection(FirebaseCollections.parts.name)
                .add(partModel.toMap());

        partModel.partId = newVendor.id;
        // } else {
        //   partModel.partId =
        //       PartModel.fromMap(partsQuerySnapshot.docs.first.data()).partId;
        // }
        FirebaseFirestore.instance
            .collection(FirebaseCollections.parts.name)
            .doc(partModel.partId)
            .set(partModel.toMap());

        stockModel.partId = partModel.partId;

        // QuerySnapshot<Map<String, dynamic>> stockQuerySnapshot =
        //     await FirebaseFirestore.instance
        //         .collection(FirebaseCollections.stock.name)
        //         .where("partId", isEqualTo: stockModel.partId)
        //         .get();

        // if (stockQuerySnapshot.docs.isEmpty) {
        DocumentReference<Map<String, dynamic>> newStock =
            await FirebaseFirestore.instance
                .collection(FirebaseCollections.stock.name)
                .add(stockModel.toMap());

        stockModel.stockId = newStock.id;
        // } else {
        //   stockModel.stockId =
        //       StockModel.fromMap(stockQuerySnapshot.docs.first.data()).stockId;
        // }

        await FirebaseFirestore.instance
            .collection(FirebaseCollections.stock.name)
            .doc(stockModel.stockId)
            .set(stockModel.toMap());
      }

      // isLoading = false;
      // update();

      Utils.showSnackBar(title: "File processed successfully");
    } catch (e) {
      // isLoading = false;
      Utils.showSnackBar(title: e.toString());

      // update();
    }
  }

  dynamic extractProperty(
      List<MapEntry<dynamic, String>> row, String columnToSearch) {
    return row.firstWhereOrNull((el) {
      return el.value.toUpperCase().trim() == columnToSearch;
    })?.key;
  }

  Future<void> downloadSampleFile() async {
    try {
      await Utils.requestPermissions(permissionsList: [
        Platform.isIOS ? Permission.storage : Permission.manageExternalStorage
      ]);
      // Load the file from assets
      final ByteData data = await rootBundle.load('assets/sample_csv.csv');
      final buffer = data.buffer.asUint8List();

      // Get the path to the local storage
      // if (Platform.isAndroid) {
// Redirects it to download folder in android
      await Printing.sharePdf(bytes: buffer, filename: 'sample.csv');
      // } else {
      //   Directory? directory;
      //   // directory = await getExternalStorageDirectory();
      //   directory = await getApplicationDocumentsDirectory();
      //   final path = '${directory?.path}/sample.csv';

      //   // Write the file to local storage
      //   final file = File(path);
      //   await file.writeAsBytes(buffer);

      //   Utils.showSnackBar(title: "File downloaded to $path");

      //   await Printing.sharePdf(
      //       bytes: await file.readAsBytes(), filename: "sample.csv");
      // }
    } catch (e) {
      Utils.showSnackBar(title: "Error $e");
    }
  }
}
