import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/stock_model.dart';
import 'package:speed_force_franchise/models/vendor_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/parts/controllers/parts_controller.dart';
import 'package:speed_force_franchise/modules/parts/modules/csv_upload/controllers/csv_upload_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

class CsvUpload extends StatefulWidget {
  const CsvUpload({super.key});

  @override
  State<CsvUpload> createState() => _CsvUploadState();
}

class _CsvUploadState extends State<CsvUpload> {
  final key = GlobalKey<FormState>();
  final partNumberctrl = TextEditingController();
  final partDescctrl = TextEditingController();
  final manufacurerctrl = TextEditingController();
  final currentStockctrl = TextEditingController();
  final moqctrl = TextEditingController();
  final gstRatectrl = TextEditingController();
  final mrpctrl = TextEditingController();
  final vendorctrl = TextEditingController();
  final purchasePricectrl = TextEditingController();
  @override
  void initState() {
    Get.put(CsvUploadController());
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<CsvUploadController>();
    super.dispose();
  }

  bool submitted = false;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CsvUploadController>(
      builder: (csvUploadCntroller) {
        return GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Scaffold(
            appBar: AppBar(
              title: const Text("Inventory CSV Upload"),
              elevation: 0.5,
              backgroundColor: colorsConstants.whiteColor,
              shadowColor: colorsConstants.whiteColor,
              surfaceTintColor: colorsConstants.whiteColor,
            ),
            body: SingleChildScrollView(
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 16.w),
                child: Form(
                  key: key,
                  child: Column(
                    children: [
                      HeightBox(20.h),
                      Center(
                        child: PrimaryButton(
                          onPress: () {
                            csvUploadCntroller.downloadSampleFile();
                          },
                          title: "Download Sample CSV",
                        ),
                      ),
                      HeightBox(20.h),
                      Text(
                        "Please download the format before uploading your file and Make sure that your csv file should match with that format",
                        style: TextStyle(
                            color: colorsConstants.hintGrey, fontSize: 12.sp),
                        textAlign: TextAlign.center,
                      ),
                      const Divider(),
                      HeightBox(20.h),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              // csvUploadCntroller.csvData.isNotEmpty
                              //     ? csvUploadCntroller.fileName ?? ""
                              //     : "No file selected",
                              csvUploadCntroller.fileName ?? "No file selected",
                              style: TextStyle(color: colorsConstants.hintGrey),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          PrimaryButton(
                            onPress: () async {
                              if (csvUploadCntroller.isLoading) {
                                return;
                              }
                              await csvUploadCntroller.pickFile();
                            },
                            title: "Choose file",
                          ),
                        ],
                      ),
                      if (csvUploadCntroller.headers.isEmpty ||
                          csvUploadCntroller.rows.isEmpty) ...[
                        HeightBox(20.h),
                        const Row(
                          children: [
                            Expanded(child: Divider()),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 15.0),
                              child: Text(
                                "Or",
                                style: TextStyle(
                                    color: Color.fromARGB(255, 88, 88, 88)),
                              ),
                            ),
                            Expanded(child: Divider())
                          ],
                        ),
                        HeightBox(10.h),
                        const Text("Enter Manually"),
                        HeightBox(10.h),

                        TextFormField(
                            controller: partNumberctrl,
                            validator: (value) {
                              if ((value?.isEmpty ?? true) || (value == null)) {
                                return "Enter Value";
                              } else {
                                return null;
                              }
                            },
                            decoration: const InputDecoration(
                                label: Row(
                              children: [Text("Part Number"), Asterisk()],
                            ))),
                        HeightBox(10.h),
                        TextFormField(
                            controller: partDescctrl,
                            validator: (value) {
                              if ((value?.isEmpty ?? true) || (value == null)) {
                                return "Enter Value";
                              } else {
                                return null;
                              }
                            },
                            decoration: const InputDecoration(
                                label: Row(
                              children: [Text("Part Description"), Asterisk()],
                            ))),
                        // HeightBox(10.h),  TextFormField(decoration: InputDecoration(labelText: "Category")),
                        HeightBox(10.h),
                        TextFormField(
                            controller: manufacurerctrl,
                            validator: (value) {
                              if ((value?.isEmpty ?? true) || (value == null)) {
                                return "Enter Value";
                              } else {
                                return null;
                              }
                            },
                            decoration: const InputDecoration(
                                label: Row(
                              children: [Text("Manufacturer"), Asterisk()],
                            ))),
                        HeightBox(10.h),
                        TextFormField(
                            controller: moqctrl,
                            validator: (value) {
                              if ((value?.isEmpty ?? true) || (value == null)) {
                                return "Enter Value";
                              } else if (!value.isNumericOnly) {
                                return "Enter Numeric Value";
                              } else {
                                return null;
                              }
                            },
                            decoration: const InputDecoration(
                                label: Row(
                              children: [Text("MOQ"), Asterisk()],
                            ))),
                        HeightBox(10.h),
                        TextFormField(
                            controller: currentStockctrl,
                            validator: (value) {
                              if ((value?.isEmpty ?? true) || (value == null)) {
                                return "Enter Value";
                              } else if (!value.isNumericOnly) {
                                return "Enter Numeric Value";
                              } else {
                                return null;
                              }
                            },
                            decoration: const InputDecoration(
                                label: Row(
                              children: [Text("Current Stock"), Asterisk()],
                            ))),
                        HeightBox(10.h),
                        TextFormField(
                            controller: gstRatectrl,
                            validator: (value) {
                              if ((value?.isEmpty ?? true) || (value == null)) {
                                return "Enter Value";
                              } else if (!value.isNumericOnly) {
                                return "Enter Numeric Value";
                              } else {
                                return null;
                              }
                            },
                            decoration: const InputDecoration(
                                label: Row(
                              children: [Text("GST Rate"), Asterisk()],
                            ))),
                        HeightBox(10.h),
                        TextFormField(
                            controller: mrpctrl,
                            validator: (value) {
                              if ((value?.isEmpty ?? true) || (value == null)) {
                                return "Enter Value";
                              } else if (!value.isNumericOnly) {
                                return "Enter Numeric Value";
                              } else {
                                return null;
                              }
                            },
                            decoration: const InputDecoration(
                                label: Row(
                              children: [Text("MRP"), Asterisk()],
                            ))),
                        HeightBox(10.h),
                        TextFormField(
                            controller: purchasePricectrl,
                            validator: (value) {
                              if ((value?.isEmpty ?? true) || (value == null)) {
                                return "Enter Value";
                              } else if (!value.isNumericOnly) {
                                return "Enter Numeric Value";
                              } else {
                                return null;
                              }
                            },
                            decoration: const InputDecoration(
                                label: Row(
                              children: [Text("Purchase Price"), Asterisk()],
                            ))),
                        HeightBox(10.h),
                        TextFormField(
                            controller: vendorctrl,
                            validator: (value) {
                              if ((value?.isEmpty ?? true) || (value == null)) {
                                return "Enter Value";
                              } else {
                                return null;
                              }
                            },
                            decoration: const InputDecoration(
                                label: Row(
                              children: [Text("Vendor"), Asterisk()],
                            ))),
                        HeightBox(20.h),
                        PrimaryButton(
                          onPress: () async {
                            if (submitted) {
                              return;
                            }
                            submitted = true;

                            await newPartAdd();
                            submitted = false;
                          },
                          title: "Submit",
                        ),
                      ],
                      //               this.partId,
                      // this.franchiseId,
                      // this.name,
                      // this.partNumber,
                      // this.category,
                      // this.manufacturer,
                      // this.applicableBrands,
                      // this.applicableModels,
                      // this.quantity,
                      // this.mrp,
                      // this.purchasePrice,
                      // this.isOil,
                      // this.gstRate,

                      HeightBox(20.h),
                      if (csvUploadCntroller.headers.isNotEmpty ||
                          csvUploadCntroller.rows.isNotEmpty) ...[
                        SingleChildScrollView(
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              headingRowColor: WidgetStatePropertyAll<Color>(
                                colorsConstants.primaryRed,
                              ),
                              columns: csvUploadCntroller.headers.map((header) {
                                return DataColumn(
                                  label: Text(
                                    header.toString(),
                                    style: TextStyle(
                                        color: colorsConstants.whiteColor),
                                  ),
                                );
                              }).toList(),
                              rows: csvUploadCntroller.rows.mapIndexed(
                                (index, element) {
                                  return DataRow(
                                    color: index % 2 == 0
                                        ? const WidgetStatePropertyAll<Color>(
                                            Colors.white)
                                        : WidgetStatePropertyAll<Color>(
                                            Colors.grey[50] ?? Colors.grey),
                                    cells: element.map((data) {
                                      return DataCell(
                                        Text(
                                          data.toString(),
                                        ),
                                      );
                                    }).toList(),
                                  );
                                },
                              ).toList(),
                            ),
                          ),
                        ),
                        const SizedBox(height: 50),
                        Container(
                          width: double.maxFinite,
                          padding: EdgeInsets.only(top: 5.h, bottom: 12.h),
                          child: PrimaryButton(
                            onPress: () async {
                              try {
                                context.loaderOverlay
                                    .show(progress: "Uploading CSV Data...");
                                await csvUploadCntroller.uploadCsvToDatabase();

                                if (context.mounted) {
                                  context.loaderOverlay.hide();
                                  Navigator.pop(context);
                                }
                              } catch (e) {
                                if (context.mounted) {
                                  context.loaderOverlay.hide();
                                }
                              }
                            },
                            title: "Upload",
                          ),
                        )
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  newPartAdd() async {
    bool oldPart = false;
    if (key.currentState?.validate() ?? false) {
      // context.loaderOverlay.show(progress: "Uploading CSV Data...");
      final vendorslist = Get.find<HomeScreenController>()
          .vendors
          .where((element) =>
              element.franchiseId == FirebaseAuth.instance.currentUser?.uid)
          .toList();

      final partFromDb = await FirebaseFirestore.instance
          .collection(FirebaseCollections.parts.name)
          .where('franchiseId',
              isEqualTo: FirebaseAuth.instance.currentUser?.uid)
          .get();
      final list =
          partFromDb.docs.map((e) => PartModel.fromMap(e.data())).toList();
      final existingParts = list
          .where((element) =>
              (element.name?.toLowerCase().trim() ==
                  partDescctrl.text.toLowerCase().trim()) &&
              (element.partNumber?.toLowerCase().trim() ==
                  partNumberctrl.text.toLowerCase().trim()))
          .toList();
      print("${existingParts.length}");
      StockModel? stockExist;

      Get.put(PartsController(
          homeScreenController: Get.find<HomeScreenController>()));
      for (var existingPart in existingParts) {
        stockExist = Get.find<PartsController>()
            .stockRecords
            .firstWhereOrNull(
                (element) => element.$2.partId == existingPart.partId)
            ?.$2;
        final oldvendor = vendorslist.firstWhereOrNull(
            (element) => element.vendorId == stockExist?.vendorId);
        if (oldvendor?.name?.toLowerCase().trim() ==
            vendorctrl.text.toLowerCase().trim()) {
          oldPart = true;
          break;
        }
      }
      print("${oldPart}");
      if (oldPart) {
        int quantity = 1;
        bool saved = false;
        showDialog(
            context: context,
            builder: (context) =>
                StatefulBuilder(builder: (context, setState2) {
                  void increment() {
                    setState2(() {
                      quantity++;
                    });
                  }

                  void decrement() {
                    if (quantity > 1) {
                      setState2(() {
                        quantity--;
                      });
                    }
                  }

                  return AlertDialog(
                    title: Text('Part Exists'),
                    content: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text("Do you want to add in Quantity"),
                        SizedBox(
                          height: 10.h,
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              onPressed: decrement,
                              icon: const Icon(Icons.remove),
                              color: Colors.red,
                            ),
                            Text(
                              '$quantity',
                              style: const TextStyle(
                                  fontSize: 18, fontWeight: FontWeight.bold),
                            ),
                            IconButton(
                              onPressed: increment,
                              icon: const Icon(Icons.add),
                              color: Colors.green,
                            ),
                          ],
                        )
                      ],
                    ),
                    actions: [
                      ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              elevation: 0,
                              foregroundColor: Colors.white,
                              backgroundColor: Colors.green),
                          onPressed: () async {
                            if (saved) {
                              return;
                            }
                            saved = true;
                            await FirebaseFirestore.instance
                                .collection(FirebaseCollections.stock.name)
                                .doc(stockExist?.stockId)
                                .update({
                              'currentStock': FieldValue.increment(quantity)
                            });
                            if (context.mounted) {
                              context.loaderOverlay.hide();
                              Navigator.pop(context);
                            }
                            Utils.showSnackBar(
                                title: "Data updated successfully");
                            saved = false;
                          },
                          child: Text("Save")),
                      ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              elevation: 0,
                              foregroundColor: Colors.white,
                              backgroundColor: Colors.redAccent),
                          onPressed: () async {
                            if (context.mounted) {
                              context.loaderOverlay.hide();
                              Navigator.pop(context);
                            }
                          },
                          child: Text("Cancel"))
                    ],
                  );
                }));
      } else {
        PartModel partModel = PartModel();

        partModel.franchiseId = FirebaseAuth.instance.currentUser?.uid;
        partModel.name = partDescctrl.text.trim();
        partModel.partNumber = partNumberctrl.text.trim();
        partModel.manufacturer = manufacurerctrl.text.trim();
        partModel.gstRate = num.tryParse(gstRatectrl.text.trim());

        StockModel stockModel = StockModel();
        stockModel.franchiseId = FirebaseAuth.instance.currentUser?.uid;
        stockModel.currentStock = double.parse(currentStockctrl.text.trim());
        stockModel.minStock = double.parse(moqctrl.text.trim());

        stockModel.mrp = double.parse(mrpctrl.text.trim());

        stockModel.purchasePrice = double.parse(purchasePricectrl.text.trim());

        final vendor = vendorslist.firstWhereOrNull((element) =>
            element.name?.toLowerCase() ==
            vendorctrl.text.trim().toLowerCase());
        if (vendor == null) {
          VendorModel vendorModel = VendorModel();
          vendorModel.franchiseId = FirebaseAuth.instance.currentUser?.uid;
          vendorModel.name = vendorctrl.text.trim();

          final vendorData = await (FirebaseFirestore.instance
              .collection(FirebaseCollections.vendors.name)
              .add(vendorModel.toMap()));
          vendorModel.vendorId = vendorData.id;
          await (FirebaseFirestore.instance
              .collection(FirebaseCollections.vendors.name)
              .doc(vendorData.id)
              .set(vendorModel.toMap()));
          stockModel.vendorId = vendorData.id;
        } else {
          stockModel.vendorId = vendor.vendorId;
        }

        await Get.find<HomeScreenController>().fetchVendors();
        DocumentReference<Map<String, dynamic>> newPart =
            await FirebaseFirestore.instance
                .collection(FirebaseCollections.parts.name)
                .add(partModel.toMap());

        partModel.partId = newPart.id;

        await FirebaseFirestore.instance
            .collection(FirebaseCollections.parts.name)
            .doc(partModel.partId)
            .set(partModel.toMap());

        stockModel.partId = partModel.partId;
        DocumentReference<Map<String, dynamic>> newStock =
            await FirebaseFirestore.instance
                .collection(FirebaseCollections.stock.name)
                .add(stockModel.toMap());

        stockModel.stockId = newStock.id;

        await FirebaseFirestore.instance
            .collection(FirebaseCollections.stock.name)
            .doc(stockModel.stockId)
            .set(stockModel.toMap());
        if (context.mounted) {
          context.loaderOverlay.hide();
          Navigator.pop(context);
        }
        Utils.showSnackBar(title: "Data uploaded successfully");
      }

      // final stocklist = Get.find<PartsController>()
      //     .stockRecords
      //     .where((element) =>
      //         element.$2.franchiseId == FirebaseAuth.instance.currentUser?.uid)
      //     .toList();
    } else {
      print("Not Validate");
    }
  }
}

class Asterisk extends StatelessWidget {
  const Asterisk({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      "*",
      style: TextStyle(fontSize: 22, color: Colors.red),
    );
  }
}
