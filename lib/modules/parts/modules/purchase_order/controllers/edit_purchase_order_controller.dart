import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/widgets.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/purchase_order_model.dart';
import 'package:speed_force_franchise/models/vendor_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class EditPurchaseOrderController extends GetxController {
  EditPurchaseOrderController(
      {required this.homeScreenController, required this.purchaseOrderModel}) {
    preferredVendorTextEditingController.text =
        purchaseOrderModel.vendorModel?.name ?? "";
    selectedVendor = purchaseOrderModel.vendorModel;
    selectedParts.addAll(purchaseOrderModel.parts ?? []);
    commentsTextEditingController.text =
        purchaseOrderModel.instructionsForVendor ?? "";
  }

  PurchaseOrderModel purchaseOrderModel;

  final HomeScreenController homeScreenController;

  TextEditingController preferredVendorTextEditingController =
      TextEditingController();

  TextEditingController commentsTextEditingController = TextEditingController();

  VendorModel? selectedVendor;

  final OverlayPortalController addPartsOverlayPortalController =
      OverlayPortalController();

  List<PartModel> allParts = [];
  List<PartModel> selectedParts = [];

  Future<void> fetchParts() async {
    allParts.clear();
    QuerySnapshot<Map<String, dynamic>> partsCollection =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.parts.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .get();

    for (var part in partsCollection.docs) {
      PartModel partModel = PartModel.fromMap(part.data());

      allParts.add(partModel);
    }
  }

  void addParts({required List<String> partsNames}) {
    selectedParts.clear();

    for (String partName in partsNames) {
      PartModel? partModel = allParts.firstWhereOrNull((part) {
        return part.name?.toLowerCase() == partName.toLowerCase();
      });

      final PartModel? purchaseOrderPart =
          purchaseOrderModel.parts?.firstWhereOrNull((pOrderPart) {
        return pOrderPart.name?.toLowerCase() == partModel?.name?.toLowerCase();
      });
      if (purchaseOrderPart != null) {
        partModel?.quantity = purchaseOrderPart.quantity;
      }

      if (partModel != null) {
        selectedParts.add(partModel);
      }
    }

    update();
  }

  void removePart({required PartModel partModel}) {
    purchaseOrderModel.parts?.remove(partModel);
    selectedParts.remove(partModel);

    update();
  }

  Future<void> editPurchaseOrderInDatabase() async {
    purchaseOrderModel.parts = selectedParts;
    purchaseOrderModel.vendorId = selectedVendor?.vendorId;
    purchaseOrderModel.vendorModel = selectedVendor;

    purchaseOrderModel.instructionsForVendor =
        commentsTextEditingController.text;

    CollectionReference<Map<String, dynamic>> purchaseOrdersCollection =
        FirebaseFirestore.instance
            .collection(FirebaseCollections.puchaseOrders.name);

    await purchaseOrdersCollection
        .doc(purchaseOrderModel.purchaseOrderId)
        .set(purchaseOrderModel.toMap());

    update();
  }
}
