import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/widgets.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/purchase_order_model.dart';
import 'package:speed_force_franchise/models/stock_model.dart';
import 'package:speed_force_franchise/models/vendor_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class PurchaseOrderController extends GetxController {
  PurchaseOrderController({required this.homeScreenController});

  List<(PartModel, StockModel)> stockRecords = [];

  int selectedTab = 0;

  final HomeScreenController homeScreenController;

  TextEditingController preferredVendorTextEditingController =
      TextEditingController();

  TextEditingController commentsTextEditingController = TextEditingController();

  VendorModel? selectedVendor;

  final OverlayPortalController addPartsOverlayPortalController =
      OverlayPortalController();

  List<PartModel> allParts = [];
  List<PartModel> selectedParts = [];

  List<PurchaseOrderModel> purchaseOrders = [];

  void changeSelectedTab({required int selectedIndex}) {
    selectedTab = selectedIndex;
    update();
  }

  Future<void> fetchParts() async {
    allParts.clear();

    QuerySnapshot<Map<String, dynamic>> partsCollection =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.parts.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .get();

    for (var part in partsCollection.docs) {
      PartModel partModel = PartModel.fromMap(part.data());
      allParts.add(partModel);
    }
    update();
  }

  Future<void> fetchPurchaseOrders() async {
    purchaseOrders.clear();
    QuerySnapshot<Map<String, dynamic>> puchaseOrdersCollection =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.puchaseOrders.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .get();

    for (var puchaseOrder in puchaseOrdersCollection.docs) {
      PurchaseOrderModel purchaseOrderModel =
          PurchaseOrderModel.fromMap(puchaseOrder.data());

      DocumentSnapshot<Map<String, dynamic>> vendorDocument =
          await FirebaseFirestore.instance
              .collection(FirebaseCollections.vendors.name)
              .doc(purchaseOrderModel.vendorId)
              .get();

      Map<String, dynamic>? vendorMap = vendorDocument.data();

      if (vendorMap != null) {
        purchaseOrderModel.vendorModel = VendorModel.fromMap(vendorMap);
      }

      purchaseOrders.add(purchaseOrderModel);
    }
    update();
  }

  void addParts({required List<String> partsNames}) {
    selectedParts.clear();

    for (String partName in partsNames) {
      PartModel? partModel = allParts.firstWhereOrNull((part) {
        return part.name?.toLowerCase() == partName.toLowerCase();
      });

      if (partModel != null) {
        partModel.quantity = 0;
        selectedParts.add(partModel);
      }
    }

    update();
  }

  void removePart({required PartModel partModel}) {
    selectedParts.remove(partModel);

    update();
  }

  Future<void> createPurchaseOrderInDatabase() async {
    PurchaseOrderModel purchaseOrderModel = PurchaseOrderModel(
      franchiseId: FirebaseAuth.instance.currentUser?.uid,
      createAt: DateTime.now().toIso8601String(),
      parts: selectedParts,
      vendorId: selectedVendor?.vendorId,
      instructionsForVendor: commentsTextEditingController.text,
      purchaseOrderStatus: PurchaseOrderStatus.open,
    );

    CollectionReference<Map<String, dynamic>> purchaseOrdersCollection =
        FirebaseFirestore.instance
            .collection(FirebaseCollections.puchaseOrders.name);

    DocumentReference<Map<String, dynamic>> newPurchaseOrder =
        await purchaseOrdersCollection.add(purchaseOrderModel.toMap());

    purchaseOrderModel.purchaseOrderId = newPurchaseOrder.id;

    await purchaseOrdersCollection
        .doc(purchaseOrderModel.purchaseOrderId)
        .set(purchaseOrderModel.toMap());

    update();
  }
}
