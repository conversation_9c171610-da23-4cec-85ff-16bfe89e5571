import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/models/purchase_order_model.dart';
import 'package:speed_force_franchise/models/vendor_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/orders/widgets/custom_overlay.dart';
import 'package:speed_force_franchise/modules/parts/modules/purchase_order/controllers/edit_purchase_order_controller.dart';
import 'package:speed_force_franchise/modules/parts/modules/purchase_order/widgets/parts_stocks_card.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

class EditPurchaseOrder extends StatefulWidget {
  const EditPurchaseOrder({super.key, required this.purchaseOrderModel});

  final PurchaseOrderModel purchaseOrderModel;

  @override
  State<EditPurchaseOrder> createState() => _EditPurchaseOrderState();
}

class _EditPurchaseOrderState extends State<EditPurchaseOrder> {
  @override
  void initState() {
    super.initState();
    Get.put(EditPurchaseOrderController(
      homeScreenController: Get.find<HomeScreenController>(),
      purchaseOrderModel: widget.purchaseOrderModel,
    )..fetchParts());
  }

  @override
  void dispose() {
    Get.delete<EditPurchaseOrderController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<EditPurchaseOrderController>(
        builder: (editPurchaseOrderController) {
      return GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Scaffold(
          appBar: AppBar(
            title: const Text("Edit Purchase Order"),
            elevation: 0.5,
            backgroundColor: colorsConstants.whiteColor,
            shadowColor: colorsConstants.whiteColor,
            surfaceTintColor: colorsConstants.whiteColor,
          ),
          body: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        HeightBox(20.h),
                        HeightBox(20.h),
                        Container(
                          decoration: BoxDecoration(
                            boxShadow: Constants.boxShadow,
                          ),
                          child: CustomTextField(
                            hintText: "Preferred Vendor",
                            controller: editPurchaseOrderController
                                .preferredVendorTextEditingController,
                            readOnly: true,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 10.w, vertical: 10.h),
                            suffix: const Icon(CupertinoIcons.chevron_down),
                            filled: true,
                            fillColor: colorsConstants.whiteColor,
                            onTap: () {
                              showDialog(
                                context: context,
                                builder: (context) {
                                  return preferredVendorDialog(context);
                                },
                              );
                            },
                            onChange: (value) {},
                            border: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide: BorderSide.none,
                            ),
                            enabledBorder: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide: BorderSide.none,
                            ),
                            focusedBorder: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide: BorderSide.none,
                            ),
                          ),
                        ),
                        HeightBox(20.h),
                        PartsStocksCard(
                          controller: editPurchaseOrderController,
                        ),
                        HeightBox(20.h),
                        CustomTextField(
                          hintText: "Comments or instructions for vendor",
                          hintStyle: TextStyle(fontSize: 15.sp),
                          maxLine: 2,
                          controller: editPurchaseOrderController
                              .commentsTextEditingController,
                        ),
                        HeightBox(20.h),
                        OverlayPortal(
                          controller: editPurchaseOrderController
                              .addPartsOverlayPortalController,
                          overlayChildBuilder: (BuildContext context) {
                            return CustomOverlay(
                              title: "Choose Parts",
                              allowMultiSelection: true,
                              onBackPress: () {
                                editPurchaseOrderController
                                    .addPartsOverlayPortalController
                                    .hide();
                              },
                              onMultiSelectSubmit:
                                  (List<String> selectedParts) {
                                editPurchaseOrderController
                                    .addPartsOverlayPortalController
                                    .hide();
                                editPurchaseOrderController.addParts(
                                  partsNames: selectedParts,
                                );
                              },
                              dataSource: editPurchaseOrderController.allParts
                                  .fold(<String>[], (previousValue, element) {
                                previousValue.add(element.name ?? "");
                                return previousValue;
                              }),
                              preSelectedValues: editPurchaseOrderController
                                  .selectedParts
                                  .fold(<String>[], (previousValue, element) {
                                previousValue?.add(element.name ?? "");
                                return previousValue;
                              }),
                              addNewOnPress: () {
                                moveToAddServicePartScreen(
                                  context: context,
                                  isAddingService: false,
                                  revertCallback: (_) async {
                                    await editPurchaseOrderController
                                        .fetchParts();

                                    editPurchaseOrderController.update();
                                  },
                                );
                              },
                              // addNewActionWidget: PrimaryButton(
                              //   onPress: () {
                              //     moveToAddServicePartScreen(
                              //       context: context,
                              //       isAddingService: false,
                              //       revertCallback: (_) async {
                              //         await editPurchaseOrderController
                              //             .fetchParts();

                              //         editPurchaseOrderController.update();
                              //       },
                              //     );
                              //   },
                              //   title: "Add new part",
                              // ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(vertical: 20.h),
                  child: Row(
                    children: [
                      Expanded(
                        child: PrimaryButton(
                          onPress: () async {
                            await editPurchaseOrderController
                                .editPurchaseOrderInDatabase();
                            if (context.mounted) {
                              Navigator.pop(context);
                            }
                          },
                          title: "Save Order",
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget preferredVendorDialog(BuildContext context) {
    EditPurchaseOrderController editPurchaseOrderController =
        Get.find<EditPurchaseOrderController>();
    return AlertDialog(
      contentPadding: EdgeInsets.zero,
      content: ConstrainedBox(
        constraints: BoxConstraints(maxHeight: 300.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            HeightBox(10.h),
            Text(
              "Preferred Vendor",
              style: TextStyle(fontSize: 24.sp),
            ),
            const Divider(),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: editPurchaseOrderController
                      .homeScreenController.vendors
                      .map(
                    (VendorModel vendorModel) {
                      return Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10.w),
                        child: InkWell(
                          onTap: () {
                            editPurchaseOrderController.selectedVendor =
                                vendorModel;
                            editPurchaseOrderController
                                .preferredVendorTextEditingController
                                .text = vendorModel.name ?? "";
                            editPurchaseOrderController.update();
                            Navigator.pop(context);
                          },
                          child: Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(8.sp),
                                margin: EdgeInsets.symmetric(vertical: 10.sp),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: colorsConstants.primaryRed,
                                    width: 2.sp,
                                  ),
                                ),
                              ),
                              WidthBox(10.w),
                              Text(vendorModel.name ?? ""),
                            ],
                          ),
                        ),
                      );
                    },
                  ).toList(),
                ),
              ),
            ),
            const Divider(),
            PrimaryButton(
              onPress: () {
                moveToAddVendorScreen(
                    context: context,
                    revertCallback: (dynamic vendor) {
                      if (vendor != null) {
                        editPurchaseOrderController.selectedVendor =
                            (vendor as VendorModel);
                        editPurchaseOrderController
                                .preferredVendorTextEditingController.text =
                            editPurchaseOrderController.selectedVendor?.name ??
                                "";
                        Navigator.pop(context);
                        editPurchaseOrderController.update();
                        Get.find<HomeScreenController>().fetchVendors();
                      }
                    });
              },
              title: "Add New Vendor",
            ),
            HeightBox(10.h),
          ],
        ),
      ),
    );
  }
}
