import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/stock_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/parts/modules/purchase_order/controllers/purchase_order_controller.dart';
import 'package:speed_force_franchise/modules/parts/modules/purchase_order/widgets/purchase_order_card.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class MyPurchaseOrders extends StatefulWidget {
  const MyPurchaseOrders({super.key, this.stockRecords});

  final List<(PartModel, StockModel)>? stockRecords;

  @override
  State<MyPurchaseOrders> createState() => _MyPurchaseOrdersState();
}

class _MyPurchaseOrdersState extends State<MyPurchaseOrders> {
  @override
  void initState() {
    Get.put(
      PurchaseOrderController(
        homeScreenController: Get.find<HomeScreenController>(),
      )
        ..fetchParts()
        ..fetchPurchaseOrders(),
    );

    super.initState();
  }

  @override
  void dispose() {
    Get.delete<PurchaseOrderController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PurchaseOrderController>(
        builder: (purchaseOrderController) {
      return GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Scaffold(
          appBar: AppBar(
            title: const Text("My Purchase Orders"),
            elevation: 0.5,
            backgroundColor: colorsConstants.whiteColor,
            shadowColor: colorsConstants.whiteColor,
            surfaceTintColor: colorsConstants.whiteColor,
          ),
          body: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        HeightBox(20.h),
                        Row(
                          children: [
                            Expanded(
                              child: GestureDetector(
                                onTap: () {
                                  purchaseOrderController.changeSelectedTab(
                                      selectedIndex: 0);
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  padding: EdgeInsets.only(bottom: 5.h),
                                  decoration: BoxDecoration(
                                    border: Border(
                                      bottom: BorderSide(
                                        width: 1.sp,
                                        color: purchaseOrderController
                                                    .selectedTab ==
                                                0
                                            ? colorsConstants.primaryRed
                                            : colorsConstants.slateGrey,
                                      ),
                                    ),
                                  ),
                                  child: Text(
                                    PurchaseOrderStatus.open.name,
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color:
                                          purchaseOrderController.selectedTab ==
                                                  0
                                              ? colorsConstants.blackColor
                                              : colorsConstants.hintGrey,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            Expanded(
                              child: GestureDetector(
                                onTap: () {
                                  purchaseOrderController.changeSelectedTab(
                                      selectedIndex: 1);
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  padding: EdgeInsets.only(bottom: 5.h),
                                  decoration: BoxDecoration(
                                    border: Border(
                                      bottom: BorderSide(
                                        width: 1.sp,
                                        color: purchaseOrderController
                                                    .selectedTab ==
                                                1
                                            ? colorsConstants.primaryRed
                                            : colorsConstants.slateGrey,
                                      ),
                                    ),
                                  ),
                                  child: Text(
                                    PurchaseOrderStatus.completed.name,
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color:
                                          purchaseOrderController.selectedTab ==
                                                  1
                                              ? colorsConstants.blackColor
                                              : colorsConstants.hintGrey,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        if (purchaseOrderController.selectedTab == 0) ...[
                          HeightBox(10.h),
                          ...purchaseOrderController.purchaseOrders
                              .where((order) {
                            return order.purchaseOrderStatus ==
                                PurchaseOrderStatus.open;
                          }).map((purchaseOrder) {
                            return InkWell(
                              onTap: () {
                                Navigator.pop(context, purchaseOrder);
                              },
                              child: Container(
                                margin: EdgeInsets.symmetric(vertical: 10.h),
                                child: PurchaseOrderCard(
                                  purchaseOrderModel: purchaseOrder,
                                  editable: false,
                                ),
                              ),
                            );
                          }),
                        ],
                        if (purchaseOrderController.selectedTab == 1) ...[
                          HeightBox(10.h),
                          ...purchaseOrderController.purchaseOrders
                              .where((order) {
                            return order.purchaseOrderStatus ==
                                PurchaseOrderStatus.completed;
                          }).map((purchaseOrder) {
                            return InkWell(
                              onTap: () {
                                Navigator.pop(context, purchaseOrder);
                              },
                              child: Container(
                                margin: EdgeInsets.symmetric(vertical: 10.h),
                                child: PurchaseOrderCard(
                                  purchaseOrderModel: purchaseOrder,
                                  editable: false,
                                ),
                              ),
                            );
                          }),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
