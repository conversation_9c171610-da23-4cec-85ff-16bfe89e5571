import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/stock_model.dart';
import 'package:speed_force_franchise/models/vendor_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/orders/widgets/custom_overlay.dart';
import 'package:speed_force_franchise/modules/parts/modules/purchase_order/controllers/purchase_order_controller.dart';
import 'package:speed_force_franchise/modules/parts/modules/purchase_order/widgets/parts_stocks_card.dart';
import 'package:speed_force_franchise/modules/parts/modules/purchase_order/widgets/purchase_order_card.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

class PurchaseOrder extends StatefulWidget {
  const PurchaseOrder({super.key, this.stockRecords});

  final List<(PartModel, StockModel)>? stockRecords;

  @override
  State<PurchaseOrder> createState() => _PurchaseOrderState();
}

class _PurchaseOrderState extends State<PurchaseOrder> {
  @override
  void initState() {
    Get.put(PurchaseOrderController(
      homeScreenController: Get.find<HomeScreenController>(),
    )
      ..selectedParts = widget.stockRecords?.map((stock) {
            stock.$1.quantity = stock.$2.maxStock ?? 0;
            return stock.$1;
          }).toList() ??
          []
      ..fetchParts()
      ..fetchPurchaseOrders());

    super.initState();
  }

  @override
  void dispose() {
    Get.delete<PurchaseOrderController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PurchaseOrderController>(
        builder: (purchaseOrderController) {
      return GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Scaffold(
          appBar: AppBar(
            title: const Text("Purchase Order"),
            elevation: 0.5,
            backgroundColor: colorsConstants.whiteColor,
            shadowColor: colorsConstants.whiteColor,
            surfaceTintColor: colorsConstants.whiteColor,
          ),
          body: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        HeightBox(20.h),
                        Row(
                          children: [
                            Expanded(
                              child: GestureDetector(
                                onTap: () {
                                  purchaseOrderController.changeSelectedTab(
                                      selectedIndex: 0);
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  padding: EdgeInsets.only(bottom: 5.h),
                                  decoration: BoxDecoration(
                                    border: Border(
                                      bottom: BorderSide(
                                        width: 1.sp,
                                        color: purchaseOrderController
                                                    .selectedTab ==
                                                0
                                            ? colorsConstants.primaryRed
                                            : colorsConstants.slateGrey,
                                      ),
                                    ),
                                  ),
                                  child: Text(
                                    "CREATE ORDER",
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color:
                                          purchaseOrderController.selectedTab ==
                                                  0
                                              ? colorsConstants.blackColor
                                              : colorsConstants.hintGrey,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            Expanded(
                              child: GestureDetector(
                                onTap: () {
                                  purchaseOrderController.changeSelectedTab(
                                      selectedIndex: 1);
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  padding: EdgeInsets.only(bottom: 5.h),
                                  decoration: BoxDecoration(
                                    border: Border(
                                      bottom: BorderSide(
                                        width: 1.sp,
                                        color: purchaseOrderController
                                                    .selectedTab ==
                                                1
                                            ? colorsConstants.primaryRed
                                            : colorsConstants.slateGrey,
                                      ),
                                    ),
                                  ),
                                  child: Text(
                                    "ORDERS",
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color:
                                          purchaseOrderController.selectedTab ==
                                                  1
                                              ? colorsConstants.blackColor
                                              : colorsConstants.hintGrey,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        if (purchaseOrderController.selectedTab == 0) ...[
                          HeightBox(20.h),
                          Container(
                            decoration: BoxDecoration(
                              boxShadow: Constants.boxShadow,
                            ),
                            child: CustomTextField(
                              hintText: "Preferred Vendor",
                              controller: purchaseOrderController
                                  .preferredVendorTextEditingController,
                              readOnly: true,
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 10.w, vertical: 10.h),
                              suffix: const Icon(CupertinoIcons.chevron_down),
                              filled: true,
                              fillColor: colorsConstants.whiteColor,
                              onTap: () {
                                showDialog(
                                  context: context,
                                  builder: (context) {
                                    return preferredVendorDialog(context);
                                  },
                                );
                              },
                              onChange: (value) {},
                              border: const OutlineInputBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(5),
                                ),
                                borderSide: BorderSide.none,
                              ),
                              enabledBorder: const OutlineInputBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(5),
                                ),
                                borderSide: BorderSide.none,
                              ),
                              focusedBorder: const OutlineInputBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(5),
                                ),
                                borderSide: BorderSide.none,
                              ),
                            ),
                          ),
                          HeightBox(20.h),
                          Container(
                            decoration: BoxDecoration(
                              boxShadow: Constants.boxShadow,
                              borderRadius: BorderRadius.circular(5.r),
                              color: Colors.white,
                            ),
                            child: Column(
                              children: [
                                HeightBox(10.h),
                                PartsStocksCard(
                                  controller: purchaseOrderController,
                                ),
                              ],
                            ),
                          ),
                          HeightBox(20.h),
                          CustomTextField(
                            hintText: "Comments or instructions for vendor",
                            hintStyle: TextStyle(fontSize: 15.sp),
                            maxLine: 2,
                            controller: purchaseOrderController
                                .commentsTextEditingController,
                          ),
                          HeightBox(20.h),
                        ],
                        if (purchaseOrderController.selectedTab == 1) ...[
                          HeightBox(20.h),
                          ...purchaseOrderController.purchaseOrders.map(
                            (purchaseOrder) {
                              return Container(
                                margin: EdgeInsets.symmetric(vertical: 10.h),
                                child: PurchaseOrderCard(
                                  purchaseOrderModel: purchaseOrder,
                                ),
                              );
                            },
                          )
                        ],
                        OverlayPortal(
                          controller: purchaseOrderController
                              .addPartsOverlayPortalController,
                          overlayChildBuilder: (BuildContext context) {
                            return CustomOverlay(
                              title: "Choose Parts",
                              allowMultiSelection: true,
                              onBackPress: () {
                                purchaseOrderController
                                    .addPartsOverlayPortalController
                                    .hide();
                              },
                              onMultiSelectSubmit:
                                  (List<String> selectedParts) {
                                purchaseOrderController
                                    .addPartsOverlayPortalController
                                    .hide();
                                purchaseOrderController.addParts(
                                  partsNames: selectedParts,
                                );
                              },
                              dataSource: purchaseOrderController.allParts
                                  .fold(<String>[], (previousValue, element) {
                                previousValue.add(element.name ?? "");
                                return previousValue;
                              }),
                              preSelectedValues: purchaseOrderController
                                  .selectedParts
                                  .fold(<String>[], (previousValue, element) {
                                previousValue?.add(element.name ?? "");
                                return previousValue;
                              }),
                              addNewOnPress: () {
                                moveToAddServicePartScreen(
                                  context: context,
                                  isAddingService: false,
                                  revertCallback: (_) async {
                                    await purchaseOrderController.fetchParts();

                                    purchaseOrderController.update();
                                  },
                                );
                              },
                              // addNewActionWidget: PrimaryButton(
                              //   onPress: () {
                              //     moveToAddServicePartScreen(
                              //       context: context,
                              //       isAddingService: false,
                              //       revertCallback: (_) async {
                              //         await purchaseOrderController
                              //             .fetchParts();

                              //         purchaseOrderController.update();
                              //       },
                              //     );
                              //   },
                              //   title: "Add new part",
                              // ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(vertical: 20.h),
                  child: Row(
                    children: [
                      Expanded(
                        child: PrimaryButton(
                          onPress: () async {
                            await purchaseOrderController
                                .createPurchaseOrderInDatabase();

                            purchaseOrderController.fetchPurchaseOrders();
                            purchaseOrderController.changeSelectedTab(
                                selectedIndex: 1);
                          },
                          title: "Create Order",
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget preferredVendorDialog(BuildContext context) {
    PurchaseOrderController purchaseOrderController =
        Get.find<PurchaseOrderController>();
    return AlertDialog(
      contentPadding: EdgeInsets.zero,
      content: ConstrainedBox(
        constraints: BoxConstraints(maxHeight: 300.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            HeightBox(10.h),
            Text(
              "Preferred Vendor",
              style: TextStyle(fontSize: 24.sp),
            ),
            const Divider(),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children:
                      purchaseOrderController.homeScreenController.vendors.map(
                    (VendorModel vendorModel) {
                      return Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10.w),
                        child: InkWell(
                          onTap: () {
                            purchaseOrderController.selectedVendor =
                                vendorModel;
                            purchaseOrderController
                                .preferredVendorTextEditingController
                                .text = vendorModel.name ?? "";
                            purchaseOrderController.update();
                            Navigator.pop(context);
                          },
                          child: Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(8.sp),
                                margin: EdgeInsets.symmetric(vertical: 10.sp),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: colorsConstants.primaryRed,
                                    width: 2.sp,
                                  ),
                                ),
                              ),
                              WidthBox(10.w),
                              Text(vendorModel.name ?? ""),
                            ],
                          ),
                        ),
                      );
                    },
                  ).toList(),
                ),
              ),
            ),
            const Divider(),
            PrimaryButton(
              onPress: () {
                moveToAddVendorScreen(
                    context: context,
                    revertCallback: (dynamic vendor) {
                      if (vendor != null) {
                        purchaseOrderController.selectedVendor =
                            (vendor as VendorModel);
                        purchaseOrderController
                                .preferredVendorTextEditingController.text =
                            purchaseOrderController.selectedVendor?.name ?? "";
                        Navigator.pop(context);
                        purchaseOrderController.update();
                        Get.find<HomeScreenController>().fetchVendors();
                      }
                    });
              },
              title: "Add New Vendor",
            ),
            HeightBox(10.h),
          ],
        ),
      ),
    );
  }
}
