import 'package:flutter/material.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/widgets/small_text_field.dart';

class PartQtyRow extends StatefulWidget {
  PartQtyRow({super.key, required this.controller, required this.part});

  final dynamic controller;
  final PartModel part;

  final TextEditingController qtyTextEditingController =
      TextEditingController();

  @override
  State<PartQtyRow> createState() => _PartQtyRowState();
}

class _PartQtyRowState extends State<PartQtyRow> {
  @override
  void initState() {
    super.initState();
    if (widget.part.quantity != null) {
      widget.qtyTextEditingController.text = widget.part.quantity!.toString();
    }
  }

  @override
  void didUpdateWidget(covariant PartQtyRow oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.part.quantity != null) {
      widget.qtyTextEditingController.text = widget.part.quantity!.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            widget.part.partNumber.toString(),
            style: TextStyle(
              fontSize: 12.sp,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Expanded(
          child: Text(
            widget.part.name.toString(),
            style: TextStyle(
              fontSize: 12.sp,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Expanded(
          child: SmallTextField(
            controller: widget.qtyTextEditingController,
            onChanged: (value) {
              widget.part.quantity = double.tryParse(value) ?? 0;
            },
          ),
        ),
        WidthBox(10.w),
        InkWell(
          onTap: () {
            widget.controller.removePart(partModel: widget.part);
          },
          child: const Icon(Icons.close),
        ),
      ],
    );
  }
}
