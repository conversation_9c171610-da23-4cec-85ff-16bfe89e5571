import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/modules/parts/modules/purchase_order/widgets/part_qty_row.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/widgets/small_primary_button.dart';

class PartsStocksCard extends StatefulWidget {
  const PartsStocksCard({super.key, required this.controller});

  final dynamic controller;

  @override
  State<PartsStocksCard> createState() => _PartsStocksCardState();
}

class _PartsStocksCardState extends State<PartsStocksCard> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        boxShadow: Constants.boxShadow,
        borderRadius: BorderRadius.circular(5.r),
        color: Colors.white,
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
            decoration: BoxDecoration(
              color: colorsConstants.primaryRed.withOpacity(0.3),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(5.r),
                topRight: Radius.circular(5.r),
              ),
            ),
            child: Row(
              children: [
                Text(
                  "PARTS/STOCKS",
                  style: TextStyle(
                    color: colorsConstants.blackColor.withOpacity(0.8),
                  ),
                ),
                const Spacer(),
                SmallPrimaryButton(
                  title: "ADD",
                  icon: CupertinoIcons.add_circled_solid,
                  onPress: () {
                    widget.controller.addPartsOverlayPortalController.show();
                  },
                ),
              ],
            ),
          ),
          if (widget.controller.selectedParts.isNotEmpty) ...[
            HeightBox(10.h),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      "Part No.",
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: colorsConstants.hintGrey,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      "Part Name",
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: colorsConstants.hintGrey,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      "Qty",
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: colorsConstants.hintGrey,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  WidthBox(10.w),
                  Icon(
                    Icons.close,
                    color: colorsConstants.transparent,
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              child: Column(
                children:
                    (widget.controller.selectedParts as List<PartModel>).map(
                  (part) {
                    return Container(
                      margin: EdgeInsets.symmetric(vertical: 5.h),
                      child: PartQtyRow(
                        controller: widget.controller,
                        part: part,
                      ),
                    );
                  },
                ).toList(),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
