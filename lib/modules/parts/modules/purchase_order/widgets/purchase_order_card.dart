import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/purchase_order_model.dart';
import 'package:speed_force_franchise/modules/parts/modules/purchase_order/controllers/purchase_order_controller.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class PurchaseOrderCard extends StatefulWidget {
  const PurchaseOrderCard({
    super.key,
    required this.purchaseOrderModel,
    this.editable = true,
  });

  final PurchaseOrderModel purchaseOrderModel;
  final bool? editable;

  @override
  State<PurchaseOrderCard> createState() => _PurchaseOrderCardState();
}

class _PurchaseOrderCardState extends State<PurchaseOrderCard> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: 10.h,
      ),
      width: double.maxFinite,
      decoration: BoxDecoration(
        boxShadow: Constants.boxShadow,
        color: colorsConstants.whiteColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                "Created On:",
                style: TextStyle(
                  color: colorsConstants.hintGrey,
                  fontSize: 15.sp,
                ),
              ),
              WidthBox(5.w),
              Expanded(
                child: Text(
                  DateFormat("MMM d, y").format(
                    DateTime.parse(
                      widget.purchaseOrderModel.createAt.toString(),
                    ),
                  ),
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: colorsConstants.hintGrey,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          HeightBox(10.h),
          Row(
            children: [
              Text(
                "Ref. number:",
                style: TextStyle(
                  color: colorsConstants.hintGrey,
                  fontSize: 15.sp,
                ),
              ),
              WidthBox(5.w),
              Expanded(
                child: Text(
                  widget.purchaseOrderModel.purchaseOrderId ?? "",
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: colorsConstants.hintGrey,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          HeightBox(10.h),
          Row(
            children: [
              Text(
                "Vendor:",
                style: TextStyle(
                  color: colorsConstants.hintGrey,
                  fontSize: 15.sp,
                ),
              ),
              WidthBox(5.w),
              Expanded(
                child: Text(
                  widget.purchaseOrderModel.vendorModel?.name ?? "",
                  style: TextStyle(
                    fontSize: 15.sp,
                    fontWeight: FontWeight.bold,
                    color: colorsConstants.hintGrey,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          HeightBox(10.h),
          Row(
            children: [
              InkWell(
                onTap: () {
                  isExpanded = !isExpanded;
                  setState(() {});
                },
                child: Text(
                  isExpanded ? "Hide details" : "Show details",
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: colorsConstants.primaryRed,
                  ),
                ),
              ),
              const Spacer(),
              if (widget.purchaseOrderModel.purchaseOrderStatus ==
                  PurchaseOrderStatus.completed) ...[
                Text(
                  "Completed",
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: colorsConstants.primaryRed,
                  ),
                ),
              ],
              if (widget.purchaseOrderModel.purchaseOrderStatus ==
                  PurchaseOrderStatus.open) ...[
                InkWell(
                  onTap: () {
                    moveToEditPurchaseOrderScreen(
                      context: context,
                      purchaseOrderModel: widget.purchaseOrderModel,
                      revertCallback: (_) async {
                        PurchaseOrderController purchaseOrderController =
                            Get.find<PurchaseOrderController>();
                        await purchaseOrderController.fetchParts();
                        await purchaseOrderController.fetchPurchaseOrders();
                      },
                    );
                  },
                  child: Text(
                    "Edit",
                    style: TextStyle(
                      fontSize: 15.sp,
                      color: colorsConstants.primaryRed,
                    ),
                  ),
                ),
              ],
            ],
          ),
          if (isExpanded) ...[
            HeightBox(10.h),
            Row(
              children: [
                Expanded(
                  child: Text(
                    "Part",
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: colorsConstants.hintGrey,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    "Quantity",
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: colorsConstants.hintGrey,
                    ),
                  ),
                ),
              ],
            ),
            ...widget.purchaseOrderModel.parts!.map(
              (part) {
                return Row(
                  children: [
                    Expanded(
                      child: Text(
                        part.name ?? "",
                        style: TextStyle(
                          color: colorsConstants.hintGrey,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        part.quantity.toString(),
                        style: TextStyle(
                          color: colorsConstants.hintGrey,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                );
              },
            ),
            if (widget.purchaseOrderModel.instructionsForVendor != null &&
                widget
                    .purchaseOrderModel.instructionsForVendor!.isNotEmpty) ...[
              HeightBox(10.h),
              Text(
                "Comment: ${widget.purchaseOrderModel.instructionsForVendor ?? ""}",
                maxLines: 5,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ],
      ),
    );
  }
}
