import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/widgets.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/purchase_order_model.dart';
import 'package:speed_force_franchise/models/selected_images_model.dart';
import 'package:speed_force_franchise/models/stock_in_model.dart';
import 'package:speed_force_franchise/models/stock_model.dart';
import 'package:speed_force_franchise/models/vendor_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class StockInController extends GetxController {
  StockInController({required this.homeScreenController}) {
    stockInDateTextEditingController.text =
        stockInDateFormat.format(DateTime.now());
    paymentModeTextEditingController.text = paymentMode.name;
  }

  List<(PartModel, StockModel)> stockRecords = [];

  HomeScreenController homeScreenController;

  TextEditingController stockInDateTextEditingController =
      TextEditingController();

  TextEditingController invoiceNumberTextEditingController =
      TextEditingController();
  DateFormat stockInDateFormat = DateFormat("dd-MM-yyyy");

  TextEditingController purchaseRefNumberTextEditingController =
      TextEditingController();

  TextEditingController vendorTextEditingController = TextEditingController();
  TextEditingController paymentModeTextEditingController =
      TextEditingController();

  bool linkWithExistingPurchaseOrder = false;

  VendorModel? selectedVendor;

  PurchaseOrderModel? linkedPurchseOrder;

  PaymentMode paymentMode = PaymentMode.cash;

  List<SelectedImageModel> invoiceImages = [];

  List<PartModel> allParts = [];
  List<PartModel> selectedParts = [];

  final OverlayPortalController addPartsOverlayPortalController =
      OverlayPortalController();

  StockInModel stockInModel = StockInModel(
      franchiseId: FirebaseAuth.instance.currentUser?.uid, stockInParts: []);

  void toggleLinkWithExistingPurchaseOrder() {
    linkWithExistingPurchaseOrder = !linkWithExistingPurchaseOrder;
    update();
  }

  Future<void> fetchParts() async {
    allParts.clear();

    QuerySnapshot<Map<String, dynamic>> partsCollection =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.parts.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .get();

    for (var part in partsCollection.docs) {
      PartModel partModel = PartModel.fromMap(part.data());
      allParts.add(partModel);
    }
    update();
  }

  void addParts({required List<String> partsNames}) {
    selectedParts.clear();

    for (String partName in partsNames) {
      PartModel? partModel = allParts.firstWhereOrNull((part) {
        return part.name?.toLowerCase() == partName.toLowerCase();
      });

      if (partModel != null) {
        partModel.quantity = 0;

        selectedParts.add(partModel);

        if (stockInModel.stockInParts?.firstWhereOrNull((data) {
              return data.partId == partModel.partId;
            }) ==
            null) {
          StockInPart stockInPart = StockInPart(
            partName: partModel.name,
            partId: partModel.partId,
            newStock: 0.0,
            purchasePrice: partModel.purchasePrice ?? 0.0,
            mrp: partModel.mrp ?? 0.0,
          );
          stockInModel.stockInParts?.add(stockInPart);
        }
      }
    }

    update();
  }

  void removePart({required String partId}) {
    stockInModel.stockInParts?.removeWhere((part) => part.partId == partId);

    update();
  }

  Future<void> saveStockInDataInDatabase() async {
    stockInModel.invoiceDate = stockInDateTextEditingController.text;
    stockInModel.invoiceNumber = invoiceNumberTextEditingController.text;
    stockInModel.paymentMode = paymentMode;
    stockInModel.vendorId = selectedVendor?.vendorId;
    stockInModel.referenceId = linkedPurchseOrder?.purchaseOrderId;

    CollectionReference<Map<String, dynamic>> stockInCollection =
        FirebaseFirestore.instance.collection(FirebaseCollections.stockIn.name);

    DocumentReference<Map<String, dynamic>> newStockInModel =
        await stockInCollection.add(stockInModel.toMap());
    stockInModel.stockInId = newStockInModel.id;

    await stockInCollection
        .doc(stockInModel.stockInId)
        .set(stockInModel.toMap());

    await FirebaseFirestore.instance
        .collection(FirebaseCollections.puchaseOrders.name)
        .doc(stockInModel.referenceId)
        .update({"purchaseOrderStatus": PurchaseOrderStatus.completed.name});

    update();
  }
}
