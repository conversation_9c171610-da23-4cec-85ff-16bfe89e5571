import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/purchase_order_model.dart';
import 'package:speed_force_franchise/models/selected_images_model.dart';
import 'package:speed_force_franchise/models/stock_in_model.dart';
import 'package:speed_force_franchise/models/stock_model.dart';
import 'package:speed_force_franchise/models/vendor_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/orders/widgets/custom_overlay.dart';
import 'package:speed_force_franchise/modules/orders/widgets/load_media_container.dart';
import 'package:speed_force_franchise/modules/parts/modules/stock_in/controllers/stock_in_controller.dart';
import 'package:speed_force_franchise/modules/parts/modules/stock_in/widgets/selected_parts_stocks_list.dart';
import 'package:speed_force_franchise/modules/parts/views/parts.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

class StockIn extends StatefulWidget {
  const StockIn({super.key, required this.stockRecords});

  final List<(PartModel, StockModel)> stockRecords;

  @override
  State<StockIn> createState() => _StockInState();
}

class _StockInState extends State<StockIn> {
  @override
  void initState() {
    Get.put(
      StockInController(
        homeScreenController: Get.find<HomeScreenController>(),
      )
        ..stockRecords = widget.stockRecords
        ..fetchParts(),
    );
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<StockInController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<StockInController>(builder: (stockInController) {
      return GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Scaffold(
          appBar: AppBar(
            title: const Text("Stock In Information"),
            elevation: 0.5,
            backgroundColor: colorsConstants.whiteColor,
            shadowColor: colorsConstants.whiteColor,
            surfaceTintColor: colorsConstants.whiteColor,
          ),
          body: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      HeightBox(20.h),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        child: Row(
                          children: [
                            Expanded(
                              child: CustomTextField(
                                readOnly: true,
                                onTap: () async {
                                  DateTime minDateTime =
                                      DateTime.utc(1985, 04, 20);
                                  DateTime maxDateTime =
                                      DateTime.utc(275760, 09, 13);
                                  DateTime? pickedDateTime =
                                      await showDatePicker(
                                    context: context,
                                    firstDate: minDateTime,
                                    lastDate: maxDateTime,
                                  );

                                  if (pickedDateTime != null) {
                                    stockInController
                                            .stockInDateTextEditingController
                                            .text =
                                        stockInController.stockInDateFormat
                                            .format(pickedDateTime);
                                  }
                                },
                                contentPadding:
                                    EdgeInsets.symmetric(horizontal: 10.w),
                                controller: stockInController
                                    .stockInDateTextEditingController,
                                enabledBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    color: colorsConstants.blackColor
                                        .withOpacity(0.3),
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    color: colorsConstants.blackColor
                                        .withOpacity(0.3),
                                  ),
                                ),
                                disabledBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    color: colorsConstants.blackColor
                                        .withOpacity(0.3),
                                  ),
                                ),
                                suffix: Icon(
                                  Icons.calendar_month_outlined,
                                  size: 24.sp,
                                ),
                                inputTextStyle: TextStyle(
                                  color: colorsConstants.blackColor
                                      .withOpacity(0.7),
                                ),
                              ),
                            ),
                            WidthBox(20.w),
                            Expanded(
                              child: CustomTextField(
                                hintText: "Invoice No.",
                                controller: stockInController
                                    .invoiceNumberTextEditingController,
                                enabledBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    color: colorsConstants.blackColor
                                        .withOpacity(0.3),
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    color: colorsConstants.blackColor
                                        .withOpacity(0.3),
                                  ),
                                ),
                                disabledBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    color: colorsConstants.blackColor
                                        .withOpacity(0.3),
                                  ),
                                ),
                                inputTextStyle: TextStyle(
                                  color: colorsConstants.blackColor
                                      .withOpacity(0.7),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      HeightBox(16.h),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        child: InkWell(
                          onTap: () {
                            stockInController
                                .toggleLinkWithExistingPurchaseOrder();
                          },
                          child: Row(
                            children: [
                              Checkbox(
                                value: stockInController
                                    .linkWithExistingPurchaseOrder,
                                activeColor: colorsConstants.primaryRed,
                                materialTapTargetSize:
                                    MaterialTapTargetSize.shrinkWrap,
                                onChanged: (_) {
                                  stockInController
                                      .toggleLinkWithExistingPurchaseOrder();
                                },
                              ),
                              const Text("Link existing purchase order")
                            ],
                          ),
                        ),
                      ),
                      if (stockInController.linkWithExistingPurchaseOrder) ...[
                        HeightBox(16.h),
                        Row(
                          children: [
                            WidthBox(16.w),
                            const Text("Reference Id :"),
                          ],
                        ),
                        HeightBox(2.h),
                        Container(
                          margin: EdgeInsets.symmetric(horizontal: 16.w),
                          decoration: BoxDecoration(
                            boxShadow: Constants.boxShadow,
                          ),
                          child: CustomTextField(
                            hintText: "Select purchase Ref. No",
                            controller: stockInController
                                .purchaseRefNumberTextEditingController,
                            readOnly: true,
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 10.w, vertical: 10.h),
                            suffix: const Icon(CupertinoIcons.chevron_down),
                            filled: true,
                            fillColor: colorsConstants.whiteColor,
                            onTap: () {
                              moveToMyPurchaseOrdersScreen(
                                context: context,
                                revertCallback: (dynamic purchaseOrder) {
                                  if (purchaseOrder != null) {
                                    stockInController.stockInModel.stockInParts!
                                        .clear();
                                    stockInController.linkedPurchseOrder =
                                        (purchaseOrder as PurchaseOrderModel);
                                    stockInController.selectedVendor =
                                        stockInController
                                            .linkedPurchseOrder?.vendorModel;

                                    stockInController
                                        .purchaseRefNumberTextEditingController
                                        .text = stockInController
                                            .linkedPurchseOrder
                                            ?.purchaseOrderId ??
                                        "";

                                    stockInController
                                        .vendorTextEditingController
                                        .text = stockInController
                                            .selectedVendor?.name ??
                                        "";

                                    stockInController.selectedParts =
                                        purchaseOrder.parts ?? [];

                                    for (PartModel partModel
                                        in stockInController.selectedParts) {
                                      StockInPart stockInData = StockInPart(
                                        partId: partModel.partId,
                                        partName: partModel.name,
                                        // currentStock: partStockRecord.$2.currentStock,
                                        newStock: partModel.quantity,
                                        purchasePrice:
                                            partModel.purchasePrice ?? 0.0,
                                        mrp: partModel.mrp ?? 0.0,
                                      );
                                      stockInController
                                          .stockInModel.stockInParts
                                          ?.add(
                                        stockInData,
                                      );
                                    }

                                    stockInController.update();
                                  }
                                },
                              );
                            },
                            onChange: (value) {},
                            border: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide: BorderSide.none,
                            ),
                            enabledBorder: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide: BorderSide.none,
                            ),
                            focusedBorder: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(5),
                              ),
                              borderSide: BorderSide.none,
                            ),
                          ),
                        ),
                      ],
                      HeightBox(16.h),
                      Row(
                        children: [
                          WidthBox(16.w),
                          const Text("Vendor :"),
                        ],
                      ),
                      HeightBox(2.h),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 16.w),
                        decoration: BoxDecoration(
                          boxShadow: Constants.boxShadow,
                        ),
                        child: CustomTextField(
                          hintText: "Vendor",
                          controller:
                              stockInController.vendorTextEditingController,
                          readOnly: true,
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 10.w, vertical: 10.h),
                          suffix: const Icon(CupertinoIcons.chevron_down),
                          filled: true,
                          fillColor: colorsConstants.whiteColor,
                          onTap: () {
                            showDialog(
                              context: context,
                              builder: (context) {
                                return preferredVendorDialog(context);
                              },
                            );
                          },
                          onChange: (value) {},
                          border: const OutlineInputBorder(
                            borderRadius: BorderRadius.all(
                              Radius.circular(5),
                            ),
                            borderSide: BorderSide.none,
                          ),
                          enabledBorder: const OutlineInputBorder(
                            borderRadius: BorderRadius.all(
                              Radius.circular(5),
                            ),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: const OutlineInputBorder(
                            borderRadius: BorderRadius.all(
                              Radius.circular(5),
                            ),
                            borderSide: BorderSide.none,
                          ),
                        ),
                      ),
                      HeightBox(16.h),
                      Row(
                        children: [
                          WidthBox(16.w),
                          const Text("Payment mode :"),
                        ],
                      ),
                      HeightBox(2.h),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 16.w),
                        decoration: BoxDecoration(
                          boxShadow: Constants.boxShadow,
                        ),
                        child: CustomTextField(
                          hintText: "Select payment mode",
                          controller: stockInController
                              .paymentModeTextEditingController,
                          readOnly: true,
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 10.w, vertical: 10.h),
                          suffix: const Icon(CupertinoIcons.chevron_down),
                          filled: true,
                          fillColor: colorsConstants.whiteColor,
                          onTap: () {
                            showDialog(
                              context: context,
                              builder: (context) {
                                return paymentModeDialog(context);
                              },
                            );
                          },
                          onChange: (value) {},
                          border: const OutlineInputBorder(
                            borderRadius: BorderRadius.all(
                              Radius.circular(5),
                            ),
                            borderSide: BorderSide.none,
                          ),
                          enabledBorder: const OutlineInputBorder(
                            borderRadius: BorderRadius.all(
                              Radius.circular(5),
                            ),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: const OutlineInputBorder(
                            borderRadius: BorderRadius.all(
                              Radius.circular(5),
                            ),
                            borderSide: BorderSide.none,
                          ),
                        ),
                      ),
                      HeightBox(16.h),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 16.w),
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 10.h,
                        ),
                        decoration: BoxDecoration(
                          boxShadow: Constants.boxShadow,
                          borderRadius: BorderRadius.circular(5.r),
                          color: Colors.white,
                        ),
                        child: LoadMediaContainer(
                          title: "Invoice Images",
                          onMediaLoaded: (SelectedImageModel pickedMedia) {
                            stockInController.invoiceImages.add(pickedMedia);
                            stockInController.update();
                          },
                          onRemoveMedia: (SelectedImageModel removedMedia) {
                            stockInController.invoiceImages
                                .remove(removedMedia);
                            stockInController.update();
                          },
                        ),
                      ),
                      HeightBox(16.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          ButtonWithIcon(
                            title: "Add Part/Stock",
                            icon: CupertinoIcons.add_circled_solid,
                            onPress: () {
                              stockInController.addPartsOverlayPortalController
                                  .show();
                            },
                            backgroundColor: colorsConstants.primaryRed,
                          ),
                          WidthBox(16.w),
                        ],
                      ),
                      HeightBox(16.h),
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 16.w),
                        child: SelectedPartsStocksList(
                          controller: stockInController,
                        ),
                      ),
                      HeightBox(30.h),
                      OverlayPortal(
                        controller:
                            stockInController.addPartsOverlayPortalController,
                        overlayChildBuilder: (BuildContext context) {
                          return CustomOverlay(
                            title: "Choose Parts",
                            allowMultiSelection: true,
                            onBackPress: () {
                              stockInController.addPartsOverlayPortalController
                                  .hide();
                            },
                            onMultiSelectSubmit: (List<String> selectedParts) {
                              stockInController.addPartsOverlayPortalController
                                  .hide();
                              stockInController.addParts(
                                partsNames: selectedParts,
                              );
                            },
                            dataSource: stockInController.allParts
                                .fold(<String>[], (previousValue, element) {
                              previousValue.add(element.name ?? "");
                              return previousValue;
                            }),
                            preSelectedValues: stockInController.selectedParts
                                .fold(<String>[], (previousValue, element) {
                              previousValue?.add(element.name ?? "");
                              return previousValue;
                            }),
                            addNewOnPress: () {
                              moveToAddServicePartScreen(
                                context: context,
                                isAddingService: false,
                                revertCallback: (_) async {
                                  await stockInController.fetchParts();

                                  stockInController.update();
                                },
                              );
                            },
                            // addNewActionWidget: PrimaryButton(
                            //   onPress: () {
                            //     moveToAddServicePartScreen(
                            //       context: context,
                            //       isAddingService: false,
                            //       revertCallback: (_) async {
                            //         await stockInController.fetchParts();

                            //         stockInController.update();
                            //       },
                            //     );
                            //   },
                            //   title: "Add new part",
                            // ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget preferredVendorDialog(BuildContext context) {
    StockInController stockInController = Get.find<StockInController>();
    return AlertDialog(
      contentPadding: EdgeInsets.zero,
      content: ConstrainedBox(
        constraints: BoxConstraints(maxHeight: 300.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            HeightBox(10.h),
            Text(
              "Preferred Vendor",
              style: TextStyle(fontSize: 24.sp),
            ),
            const Divider(),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: stockInController.homeScreenController.vendors.map(
                    (VendorModel vendorModel) {
                      return Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10.w),
                        child: InkWell(
                          onTap: () {
                            stockInController.selectedVendor = vendorModel;
                            stockInController.vendorTextEditingController.text =
                                vendorModel.name ?? "";
                            stockInController.update();
                            Navigator.pop(context);
                          },
                          child: Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(8.sp),
                                margin: EdgeInsets.symmetric(vertical: 10.sp),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: colorsConstants.primaryRed,
                                    width: 2.sp,
                                  ),
                                ),
                              ),
                              WidthBox(10.w),
                              Text(vendorModel.name ?? ""),
                            ],
                          ),
                        ),
                      );
                    },
                  ).toList(),
                ),
              ),
            ),
            const Divider(),
            PrimaryButton(
              onPress: () {
                moveToAddVendorScreen(
                    context: context,
                    revertCallback: (dynamic vendor) {
                      if (vendor != null) {
                        stockInController.selectedVendor =
                            (vendor as VendorModel);
                        stockInController.vendorTextEditingController.text =
                            stockInController.selectedVendor?.name ?? "";
                        Navigator.pop(context);
                        stockInController.update();
                        Get.find<HomeScreenController>().fetchVendors();
                      }
                    });
              },
              title: "Add New Vendor",
            ),
            HeightBox(10.h),
          ],
        ),
      ),
    );
  }

  Widget paymentModeDialog(BuildContext context) {
    StockInController stockInController = Get.find<StockInController>();
    return AlertDialog(
      contentPadding: EdgeInsets.zero,
      content: ConstrainedBox(
        constraints: BoxConstraints(maxHeight: 300.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            HeightBox(10.h),
            Text(
              "Payment mode",
              style: TextStyle(fontSize: 24.sp),
            ),
            const Divider(),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: PaymentMode.values.map(
                    (PaymentMode paymentMode) {
                      return Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10.w),
                        child: InkWell(
                          onTap: () {
                            stockInController.paymentMode = paymentMode;
                            stockInController.paymentModeTextEditingController
                                .text = paymentMode.name;
                            stockInController.update();
                            Navigator.pop(context);
                          },
                          child: Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(8.sp),
                                margin: EdgeInsets.symmetric(vertical: 10.sp),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: colorsConstants.primaryRed,
                                    width: 2.sp,
                                  ),
                                ),
                              ),
                              WidthBox(10.w),
                              Text(paymentMode.name),
                            ],
                          ),
                        ),
                      );
                    },
                  ).toList(),
                ),
              ),
            ),
            HeightBox(10.h),
          ],
        ),
      ),
    );
  }
}
