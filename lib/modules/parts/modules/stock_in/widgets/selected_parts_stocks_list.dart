import 'package:flutter/material.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:speed_force_franchise/modules/parts/modules/stock_in/controllers/stock_in_controller.dart';
import 'package:speed_force_franchise/modules/parts/modules/stock_in/widgets/stock_in_row.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';
import 'package:speed_force_franchise/widgets/small_text_field.dart';

class SelectedPartsStocksList extends StatefulWidget {
  const SelectedPartsStocksList({super.key, required this.controller});

  final StockInController controller;

  @override
  State<SelectedPartsStocksList> createState() =>
      _SelectedPartsStocksListState();
}

class _SelectedPartsStocksListState extends State<SelectedPartsStocksList> {
  @override
  Widget build(BuildContext context) {
    StockInController stockInController = Get.find<StockInController>();
    return Column(
      children: [
        if (widget.controller.selectedParts.isNotEmpty) ...[
          HeightBox(10.h),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 10.w),
            decoration: BoxDecoration(
              boxShadow: Constants.boxShadow,
              borderRadius: BorderRadius.circular(5.r),
              color: Colors.white,
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        "Part Name",
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: colorsConstants.hintGrey,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    // Expanded(
                    //   child: Text(
                    //     "Current Stock",
                    //     style: TextStyle(
                    //       fontSize: 12.sp,
                    //       color: colorsConstants.hintGrey,
                    //     ),
                    //     maxLines: 2,
                    //     overflow: TextOverflow.ellipsis,
                    //   ),
                    // ),
                    Expanded(
                      child: Text(
                        "New Stock",
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: colorsConstants.hintGrey,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        "Purch. Price",
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: colorsConstants.hintGrey,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        "MRP",
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: colorsConstants.hintGrey,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    WidthBox(2.w),
                    Icon(
                      Icons.close,
                      color: colorsConstants.transparent,
                    ),
                  ],
                ),
                Divider(
                  color: colorsConstants.hintGrey,
                ),
                ...(widget.controller.stockInModel.stockInParts)!.map(
                  (stockInPart) {
                    return Container(
                      margin: EdgeInsets.symmetric(vertical: 5.h),
                      child: StockInRow(
                        controller: widget.controller,
                        // partStockModel: partStockRecord,
                        stockInPart: stockInPart,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          HeightBox(20.h),
          GetBuilder<StockInController>(
              id: "Total",
              builder: (_) {
                return Container(
                  padding:
                      EdgeInsets.symmetric(vertical: 10.h, horizontal: 5.w),
                  decoration: BoxDecoration(
                    boxShadow: Constants.boxShadow,
                    color: colorsConstants.whiteColor,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            const Text("Paid Amount"),
                            HeightBox(5.h),
                            SmallTextField(
                              width: double.maxFinite,
                              onChanged: (value) => stockInController
                                  .stockInModel
                                  .amountPaid = double.parse(value),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Column(
                          children: [
                            const Text("Total"),
                            HeightBox(5.h),
                            Text(
                                "${Constants.rupeeSign} ${stockInController.stockInModel.stockInParts?.fold(0.0, (previousValue, element) {
                              return previousValue +
                                  element.newStock! * element.purchasePrice!;
                            })}"),
                          ],
                        ),
                      ),
                      PrimaryButton(
                        onPress: () async {
                          try {
                            context.loaderOverlay.show();
                            await stockInController.saveStockInDataInDatabase();
                            if (context.mounted) Navigator.pop(context);
                            if (context.mounted) {
                              context.loaderOverlay.hide();
                            }
                          } catch (e) {
                            Utils.showSnackBar(title: e.toString());
                            if (context.mounted) {
                              context.loaderOverlay.hide();
                            }
                          }
                        },
                        titleWidget: Text(
                          "Save Data",
                          style: TextStyle(fontSize: 12.sp),
                        ),
                        backgroundColor: const Color.fromARGB(255, 9, 64, 83),
                      )
                    ],
                  ),
                );
              }),
          HeightBox(20.h),
        ],
      ],
    );
  }
}
