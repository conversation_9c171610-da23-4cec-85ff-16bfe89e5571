import 'package:flutter/material.dart';
import 'package:speed_force_franchise/models/stock_in_model.dart';
import 'package:speed_force_franchise/modules/parts/modules/stock_in/controllers/stock_in_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/widgets/small_text_field.dart';

class StockInRow extends StatefulWidget {
  StockInRow({
    super.key,
    required this.controller,
    // required this.partStockModel,
    required this.stockInPart,
  });

  final StockInController controller;
  // final (PartModel, StockModel) partStockModel;
  final StockInPart stockInPart;

  final TextEditingController newStockTextEditingController =
      TextEditingController();
  final TextEditingController purchasePriceTextEditingController =
      TextEditingController();
  final TextEditingController mrpPriceTextEditingController =
      TextEditingController();

  @override
  State<StockInRow> createState() => _StockInRowState();
}

class _StockInRowState extends State<StockInRow> {
  @override
  void initState() {
    super.initState();
    widget.newStockTextEditingController.text =
        widget.stockInPart.newStock.toString();

    widget.purchasePriceTextEditingController.text =
        widget.stockInPart.purchasePrice.toString();

    widget.mrpPriceTextEditingController.text =
        widget.stockInPart.mrp.toString();
  }

  @override
  void didUpdateWidget(covariant StockInRow oldWidget) {
    super.didUpdateWidget(oldWidget);
    widget.newStockTextEditingController.text =
        widget.stockInPart.newStock.toString();

    widget.purchasePriceTextEditingController.text =
        widget.stockInPart.purchasePrice.toString();

    widget.mrpPriceTextEditingController.text =
        widget.stockInPart.mrp.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            widget.stockInPart.partName.toString(),
            style: TextStyle(
              fontSize: 12.sp,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        // Expanded(
        //   child: Text(
        //     widget.stockInModel.currentStock.toString(),
        //     style: TextStyle(
        //       fontSize: 12.sp,
        //     ),
        //     maxLines: 2,
        //     overflow: TextOverflow.ellipsis,
        //   ),
        // ),
        Expanded(
          child: SmallTextField(
            controller: widget.newStockTextEditingController,
            onChanged: (value) {
              widget.stockInPart.newStock = double.tryParse(value) ?? 0;
              widget.controller.update(["Total"]);
            },
          ),
        ),
        WidthBox(2.w),
        Expanded(
          child: SmallTextField(
            controller: widget.purchasePriceTextEditingController,
            onChanged: (value) {
              widget.stockInPart.purchasePrice = double.tryParse(value) ?? 0;
              widget.controller.update(["Total"]);

              // widget.part.quantity = double.tryParse(value) ?? 0;
            },
          ),
        ),
        WidthBox(2.w),
        Expanded(
          child: SmallTextField(
            controller: widget.mrpPriceTextEditingController,
            onChanged: (value) {
              widget.stockInPart.mrp = double.tryParse(value) ?? 0;

              // widget.part.quantity = double.tryParse(value) ?? 0;
            },
          ),
        ),
        WidthBox(2.w),
        InkWell(
          onTap: () {
            widget.controller
                .removePart(partId: widget.stockInPart.partId ?? "");
          },
          child: const Icon(Icons.close),
        ),
      ],
    );
  }
}
