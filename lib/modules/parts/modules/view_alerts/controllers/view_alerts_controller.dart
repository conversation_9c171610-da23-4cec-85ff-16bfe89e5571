import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/stock_model.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class ViewAlertController extends GetxController {
  List<(PartModel, StockModel)> stockRecords = [];

  List<(PartModel, StockModel)> selectedRecords = [];

  bool isSelectAll = false;

  void toggleIsSelectAll() {
    isSelectAll = !isSelectAll;
    selectedRecords.clear();

    if (isSelectAll) {
      selectedRecords.addAll(stockRecords);
    } else {
      selectedRecords.clear();
    }
    update();
  }

  void addSelectedRecord({required (PartModel, StockModel) stock}) {
    selectedRecords.add(stock);
    if (stockRecords.length == selectedRecords.length) {
      isSelectAll = true;
    }
    update();
  }

  void removeSelectedRecord({required (PartModel, StockModel) stock}) {
    selectedRecords.remove(stock);
    if (isSelectAll) {
      isSelectAll = false;
    }
    update();
  }
}
