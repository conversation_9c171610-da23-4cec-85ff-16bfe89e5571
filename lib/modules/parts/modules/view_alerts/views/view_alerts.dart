import 'package:flutter/material.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/stock_model.dart';
import 'package:speed_force_franchise/modules/parts/modules/view_alerts/controllers/view_alerts_controller.dart';
import 'package:speed_force_franchise/modules/parts/views/parts.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class ViewAlert extends StatefulWidget {
  const ViewAlert({super.key, required this.stockRecords});

  final List<(PartModel, StockModel)> stockRecords;

  @override
  State<ViewAlert> createState() => _ViewAlertState();
}

class _ViewAlertState extends State<ViewAlert> {
  @override
  void initState() {
    Get.put(ViewAlertController()
      ..stockRecords = widget.stockRecords.where((e) {
        if (e.$2.currentStock != null && e.$2.minStock != null) {
          return e.$2.currentStock! < e.$2.minStock!;
        }
        return false;
      }).toList());
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<ViewAlertController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ViewAlertController>(builder: (viewAlertController) {
      return Scaffold(
        appBar: AppBar(
          title: const Text("Inventory Alerts"),
          elevation: 0.5,
          backgroundColor: colorsConstants.whiteColor,
          shadowColor: colorsConstants.whiteColor,
          surfaceTintColor: colorsConstants.whiteColor,
        ),
        body: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: SingleChildScrollView(
            child: Column(
              children: [
                HeightBox(20.h),
                Row(
                  children: [
                    ButtonWithIcon(
                      onPress: () {
                        moveToPurchaseOrderScreen(
                          context: context,
                          stockRecords: viewAlertController.selectedRecords,
                        );
                      },
                      title: 'Purchase Order',
                      icon: Icons.add,
                    ),
                  ],
                ),
                HeightBox(10.h),
                InkWell(
                  onTap: () {
                    viewAlertController.toggleIsSelectAll();
                  },
                  child: Row(
                    children: [
                      Checkbox(
                        value: viewAlertController.isSelectAll,
                        activeColor: colorsConstants.primaryRed,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        onChanged: (_) {
                          viewAlertController.toggleIsSelectAll();
                        },
                      ),
                      Text(
                        "Select All",
                        style: TextStyle(fontSize: 16.sp),
                      )
                    ],
                  ),
                ),
                HeightBox(20.h),
                Row(
                  children: [
                    WidthBox(20.w),
                    Expanded(
                      child: Text(
                        "P No./Number",
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                          color: colorsConstants.hintGrey,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        "Stock",
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                          color: colorsConstants.hintGrey,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        "Min Qty",
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                          color: colorsConstants.hintGrey,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        "Max Qty",
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                          color: colorsConstants.hintGrey,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        "Order Qty",
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                          color: colorsConstants.hintGrey,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const Divider(),
                Column(
                  children: viewAlertController.stockRecords.map((stock) {
                    return InkWell(
                      onTap: () {
                        if (viewAlertController.selectedRecords
                            .contains(stock)) {
                          viewAlertController.removeSelectedRecord(
                              stock: stock);
                        } else {
                          viewAlertController.addSelectedRecord(stock: stock);
                        }
                      },
                      child: Row(
                        children: [
                          SizedBox(
                            height: 20.h,
                            width: 25.w,
                            child: Checkbox(
                              value: viewAlertController.selectedRecords
                                  .contains(stock),
                              activeColor: colorsConstants.primaryRed,
                              materialTapTargetSize:
                                  MaterialTapTargetSize.shrinkWrap,
                              onChanged: (isSelected) {
                                if (viewAlertController.selectedRecords
                                    .contains(stock)) {
                                  viewAlertController.removeSelectedRecord(
                                      stock: stock);
                                } else {
                                  viewAlertController.addSelectedRecord(
                                      stock: stock);
                                }
                              },
                            ),
                          ),
                          Expanded(
                            child: Text(
                              stock.$1.name ?? "",
                              style: TextStyle(
                                fontSize: 12.sp,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Expanded(
                            child: Text(
                              stock.$2.currentStock.toString(),
                              style: TextStyle(
                                fontSize: 12.sp,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Expanded(
                            child: Text(
                              stock.$2.minStock.toString(),
                              style: TextStyle(
                                fontSize: 12.sp,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Expanded(
                            child: Text(
                              stock.$2.maxStock.toString(),
                              style: TextStyle(
                                fontSize: 12.sp,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Expanded(
                            child: Text(
                              stock.$2.maxStock.toString(),
                              style: TextStyle(
                                fontSize: 12.sp,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
