import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/vendor_model.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import 'package:speed_force_franchise/widgets/primary_button.dart';

class AddVendor extends StatefulWidget {
  const AddVendor({super.key});

  @override
  State<AddVendor> createState() => _AddVendorState();
}

class _AddVendorState extends State<AddVendor> {
  TextEditingController nameTextEditingController = TextEditingController();
  TextEditingController phoneTextEditingController = TextEditingController();
  TextEditingController emailTextEditingController = TextEditingController();
  TextEditingController addressTextEditingController = TextEditingController();
  TextEditingController gstinTextEditingController = TextEditingController();
  TextEditingController panTextEditingController = TextEditingController();
  TextEditingController vendorReferenceIdTextEditingController =
      TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Vendor Details"),
        elevation: 0.5,
        backgroundColor: colorsConstants.whiteColor,
        shadowColor: colorsConstants.whiteColor,
        surfaceTintColor: colorsConstants.whiteColor,
      ),
      body: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: SingleChildScrollView(
          child: Column(
            children: [
              HeightBox(20.h),
              CustomTextField(
                hintText: "Vendor Name*",
                contentPadding: EdgeInsets.zero,
                controller: nameTextEditingController,
                filled: true,
                fillColor: colorsConstants.whiteColor,
                border: const UnderlineInputBorder(
                  borderSide: BorderSide.none,
                ),
                enabledBorder: const UnderlineInputBorder(
                  borderSide: BorderSide.none,
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide:
                      BorderSide(color: colorsConstants.primaryRed, width: 2.0),
                ),
              ),
              HeightBox(20.h),
              CustomTextField(
                hintText: "Phone Number*",
                contentPadding: EdgeInsets.zero,
                controller: phoneTextEditingController,
                filled: true,
                keyboardType: TextInputType.number,
                fillColor: colorsConstants.whiteColor,
                border: const UnderlineInputBorder(
                  borderSide: BorderSide.none,
                ),
                enabledBorder: const UnderlineInputBorder(
                  borderSide: BorderSide.none,
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide:
                      BorderSide(color: colorsConstants.primaryRed, width: 2.0),
                ),
              ),
              HeightBox(20.h),
              CustomTextField(
                hintText: "Email",
                contentPadding: EdgeInsets.zero,
                controller: emailTextEditingController,
                filled: true,
                fillColor: colorsConstants.whiteColor,
                border: const UnderlineInputBorder(
                  borderSide: BorderSide.none,
                ),
                enabledBorder: const UnderlineInputBorder(
                  borderSide: BorderSide.none,
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide:
                      BorderSide(color: colorsConstants.primaryRed, width: 2.0),
                ),
              ),
              HeightBox(20.h),
              CustomTextField(
                hintText: "Address",
                contentPadding: EdgeInsets.zero,
                controller: addressTextEditingController,
                filled: true,
                fillColor: colorsConstants.whiteColor,
                border: const UnderlineInputBorder(
                  borderSide: BorderSide.none,
                ),
                enabledBorder: const UnderlineInputBorder(
                  borderSide: BorderSide.none,
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide:
                      BorderSide(color: colorsConstants.primaryRed, width: 2.0),
                ),
              ),
              HeightBox(20.h),
              CustomTextField(
                hintText: "GSTIN",
                contentPadding: EdgeInsets.zero,
                controller: gstinTextEditingController,
                filled: true,
                fillColor: colorsConstants.whiteColor,
                border: const UnderlineInputBorder(
                  borderSide: BorderSide.none,
                ),
                enabledBorder: const UnderlineInputBorder(
                  borderSide: BorderSide.none,
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide:
                      BorderSide(color: colorsConstants.primaryRed, width: 2.0),
                ),
              ),
              HeightBox(20.h),
              CustomTextField(
                hintText: "PAN*",
                contentPadding: EdgeInsets.zero,
                controller: panTextEditingController,
                filled: true,
                fillColor: colorsConstants.whiteColor,
                border: const UnderlineInputBorder(
                  borderSide: BorderSide.none,
                ),
                enabledBorder: const UnderlineInputBorder(
                  borderSide: BorderSide.none,
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide:
                      BorderSide(color: colorsConstants.primaryRed, width: 2.0),
                ),
              ),
              HeightBox(20.h),
              CustomTextField(
                hintText: "Vendor Reference Id",
                contentPadding: EdgeInsets.zero,
                controller: vendorReferenceIdTextEditingController,
                filled: true,
                fillColor: colorsConstants.whiteColor,
                border: const UnderlineInputBorder(
                  borderSide: BorderSide.none,
                ),
                enabledBorder: const UnderlineInputBorder(
                  borderSide: BorderSide.none,
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide:
                      BorderSide(color: colorsConstants.primaryRed, width: 2.0),
                ),
              ),
              HeightBox(50.h),
              PrimaryButton(
                onPress: () async {
                  VendorModel? vendorModel = await addVendorInDatabase();
                  if (context.mounted && vendorModel != null) {
                    Navigator.pop(context, vendorModel);
                  }
                },
                title: "Save Vendor",
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<VendorModel?> addVendorInDatabase() async {
    if (nameTextEditingController.text.isEmpty &&
        phoneTextEditingController.text.isEmpty &&
        panTextEditingController.text.isEmpty) {
      Utils.showSnackBar(title: "Fields with * are mandatory");
    }

    VendorModel? alreadyVendor =
        Get.find<HomeScreenController>().vendors.firstWhereOrNull((vendor) {
      return vendor.phone?.toLowerCase() ==
          phoneTextEditingController.text.toLowerCase();
    });

    if (alreadyVendor != null) {
      Utils.showSnackBar(title: "Vendor already existed");
      return null;
    }

    VendorModel vendorModel = VendorModel(
      franchiseId: FirebaseAuth.instance.currentUser?.uid ?? "",
      name: nameTextEditingController.text,
      phone: phoneTextEditingController.text,
      email: emailTextEditingController.text,
      address: addressTextEditingController.text,
      gstin: gstinTextEditingController.text,
      pan: panTextEditingController.text,
      referenceId: vendorReferenceIdTextEditingController.text,
    );

    CollectionReference<Map<String, dynamic>> vendorsCollection =
        FirebaseFirestore.instance.collection(FirebaseCollections.vendors.name);

    DocumentReference<Map<String, dynamic>> newVendor =
        await vendorsCollection.add(vendorModel.toMap());

    vendorModel.vendorId = newVendor.id;

    await vendorsCollection.doc(vendorModel.vendorId).set(vendorModel.toMap());

    // Get.find<PartsController>().preferredVendorTextEditingController.text =
    //     nameTextEditingController.text;

    return vendorModel;
  }
}
