import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/parts/controllers/parts_controller.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/permission.dart';

class Parts extends StatefulWidget {
  const Parts({super.key});

  @override
  State<Parts> createState() => _PartsState();
}

class _PartsState extends State<Parts> {
  @override
  void initState() {
    super.initState();
    Get.put(
      PartsController(
        homeScreenController: Get.find<HomeScreenController>(),
      )..fetchStock(),
    );
  }

  @override
  void dispose() {
    Get.delete<PartsController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PartsController>(builder: (partsController) {
      return SingleChildScrollView(
        child: Column(
          children: [
            // HeightBox(20.h),
            // Container(
            //   margin: EdgeInsets.symmetric(horizontal: 16.w),
            //   child: Row(
            //     children: [
            //       Expanded(
            //         child: ButtonWithIcon(
            //           title: "Purchase Order",
            //           icon: CupertinoIcons.add_circled_solid,
            //           onPress: () {
            //             moveToPurchaseOrderScreen(
            //                 context: context, stockRecords: []);
            //           },
            //         ),
            //       ),
            //       WidthBox(10.w),
            //       Expanded(
            //         child: ButtonWithIcon(
            //           title: "View Alerts",
            //           icon: CupertinoIcons.search,
            //           onPress: () {
            //             moveToViewAlertScreen(
            //               context: context,
            //               stockRecords: partsController.stockRecords,
            //             );
            //           },
            //           backgroundColor: colorsConstants.primaryRed,
            //         ),
            //       ),
            //     ],
            //   ),
            // ),
            // HeightBox(10.h),
            // Container(
            //   margin: EdgeInsets.symmetric(horizontal: 16.w),
            //   child: Row(
            //     children: [
            // Expanded(
            //   child: ButtonWithIcon(
            //     title: "Counter Sale",
            //     icon: Icons.pie_chart,
            //     onPress: () {
            //       moveToCreateInvoiceScreen(
            //           context: context, isCounterSale: true);
            //     },
            //   ),
            // ),
            // WidthBox(10.w),
            // Expanded(
            //   child: ButtonWithIcon(
            //     title: "Stock In",
            //     icon: CupertinoIcons.bag_fill,
            //     onPress: () {
            //       moveToStockInScreen(
            //         context: context,
            //         stockRecords: partsController.stockRecords,
            //       );
            //     },
            //   ),
            // ),
            //     ],
            //   ),
            // ),
            // HeightBox(10.h),
            // Container(
            //   margin: EdgeInsets.symmetric(horizontal: 16.w),
            //   decoration: BoxDecoration(
            //     boxShadow: Constants.boxShadow,
            //   ),
            //   child: CustomTextField(
            //     prefix: Icon(
            //       Icons.search_rounded,
            //       size: 22.sp,
            //     ),
            //     hintText: "Search",
            //     contentPadding: EdgeInsets.zero,
            //     filled: true,
            //     fillColor: colorsConstants.whiteColor,
            //     onChange: (value) {},
            //     border: const OutlineInputBorder(
            //       borderRadius: BorderRadius.all(
            //         Radius.circular(5),
            //       ),
            //       borderSide: BorderSide.none,
            //     ),
            //     enabledBorder: const OutlineInputBorder(
            //       borderRadius: BorderRadius.all(
            //         Radius.circular(5),
            //       ),
            //       borderSide: BorderSide.none,
            //     ),
            //     focusedBorder: const OutlineInputBorder(
            //       borderRadius: BorderRadius.all(
            //         Radius.circular(5),
            //       ),
            //       borderSide: BorderSide.none,
            //     ),
            //   ),
            // ),
            HeightBox(20.h),
            Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              child: Row(
                children: [
                  Expanded(
                    child: ButtonWithIcon(
                      title: " Upload",
                      icon: Icons.cloud_upload,
                      onPress: () {
                        moveToCsvUploadScreen(
                          context: context,
                          revertCallback: (_) async {
                            await partsController.fetchStock();
                          },
                        );
                      },
                    ),
                  ),
                  WidthBox(10.w),
                  Expanded(
                    child: ButtonWithIcon(
                      title: "Current Stock",
                      icon: Icons.download,
                      onPress: () {
                        partsController.currentStockdownlaodCSVFile();
                      },
                      backgroundColor: colorsConstants.primaryRed,
                    ),
                  ),
                ],
              ),
            ),
            HeightBox(5.h),
            // Container(
            //   margin: EdgeInsets.symmetric(horizontal: 16.w),
            //   child: Row(
            //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //     children: [
            //       InkWell(
            //         onTap: () {
            //           moveToCsvUploadScreen(
            //             context: context,
            //             revertCallback: (_) async {
            //               await partsController.fetchStock();
            //             },
            //           );
            //         },
            //         child: Row(
            //           children: [
            //             WidthBox(10.w),
            //             const Icon(
            //               Icons.cloud_upload,
            //               color: Color.fromARGB(255, 9, 64, 83),
            //             ),
            //             WidthBox(5.w),
            //             Text(
            //               "Upload",
            //               style: TextStyle(color: colorsConstants.primaryRed),
            //             ),
            //           ],
            //         ),
            //       ),
            //       Row(
            //         children: [
            //           // ButtonWithIcon(
            //           //   onPress: () {
            //           //     moveToAddStockScreen(
            //           //       context: context,
            //           //       revertCallback: (_) {
            //           //         partsController.fetchStock();
            //           //       },
            //           //     );
            //           //   },
            //           //   title: "Add Stock",
            //           //   icon: CupertinoIcons.add_circled_solid,
            //           // ),
            //           if (partsController.stockRecords.isNotEmpty) ...[
            //             SizedBox(width: 2.w),
            //             Row(
            //               crossAxisAlignment: CrossAxisAlignment.center,
            //               children: [
            //                 InkWell(
            //                   onTap: () {
            //                     // bool granted =
            //                     //     await requestManageExternalStoragePermission();
            //                     // if (granted) {
            //                     partsController.currentStockdownlaodCSVFile();
            //                     // }
            //                   },
            //                   child: Row(
            //                     children: [
            //                       WidthBox(10.w),
            //                       Icon(
            //                         Icons.download,
            //                         // color: Colors.white,
            //                         color: colorsConstants.primaryRed,
            //                         // size: 22,
            //                       ),
            //                       WidthBox(5.w),
            //                       Text(
            //                         "Current Stock",
            //                         style: TextStyle(
            //                             color: colorsConstants.primaryRed),
            //                       ),
            //                     ],
            //                   ),
            //                 ),
            //                 // IconButton(
            //                 //     padding: EdgeInsets.all(0),
            //                 //     style: IconButton.styleFrom(
            //                 //         // backgroundColor: colorsConstants.primaryRed,
            //                 //         ),
            //                 //     onPressed: () async {
            //                 //       // bool granted =
            //                 //       //     await requestManageExternalStoragePermission();
            //                 //       // if (granted) {
            //                 //       partsController.currentStockdownlaodCSVFile();
            //                 //       // }
            //                 //     },
            //                 //     icon: Icon(
            //                 //       Icons.download,
            //                 //       // color: Colors.white,
            //                 //       color: colorsConstants.primaryRed,
            //                 //       // size: 22,
            //                 //     )),
            //                 // // WidthBox(5.w),
            //                 // Text(
            //                 //   "Upload",
            //                 //   style:
            //                 //       TextStyle(color: colorsConstants.primaryRed),
            //                 // ),
            //               ],
            //             )
            //           ]
            //         ],
            //       )
            //     ],
            //   ),
            // ),
            HeightBox(16.h),

            partsController.stockRecords.isEmpty
                ? CircularProgressIndicator()
                : SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Container(
                      // width: 1000,
                      margin: EdgeInsets.symmetric(horizontal: 16.w),
                      child: IntrinsicWidth(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            /*  Expanded(
                        child: SingleChildScrollView(
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              headingRowColor: WidgetStatePropertyAll<Color>(
                                colorsConstants.primaryRed,
                              ),
                              columns: tableHeaders.map((header) {
                                return DataColumn(
                                  label: Text(
                                    header.toString().toUpperCase(),
                                    style: TextStyle(
                                        color: colorsConstants.whiteColor),
                                  ),
                                );
                              }).toList(),
                              rows: true
                                  ? <DataRow>[
                                      ...List.generate(
                                        partsController.stockRecords.length,
                                        (rowIndex) {
                                          final stockRecord = partsController
                                              .stockRecords[rowIndex];
                                          return DataRow(cells: [
                                            ...List.generate(
                                              tableHeaders.length,
                                              (cellIndex) {
                                                final cellData =
                                                    stockRecord.$1.name;
                                                return DataCell(
                                                    Text(cellData ?? "-"));
                                              },
                                            )
                                          ]);
                                        },
                                      )
                                    ]
                                  : Get.find().rows.mapIndexed(
                                      (index, element) {
                                        return DataRow(
                                          color: index % 2 == 0
                                              ? const WidgetStatePropertyAll<Color>(
                                                  Colors.white)
                                              : WidgetStatePropertyAll<Color>(
                                                  Colors.grey[50] ?? Colors.grey),
                                          cells: element.map((data) {
                                            return DataCell(
                                              Text(
                                                data.toString(),
                                              ),
                                            );
                                          }).toList(),
                                        );
                                      },
                                    ).toList(),
                            ),
                          ),
                        ),
                      ), */

                            IntrinsicWidth(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        right: 10.0,
                                        left: 5,
                                        top: 5,
                                        bottom: 5),
                                    child: Text(
                                      "PART_DESCRIPTION".toUpperCase(),
                                      style: TextStyle(
                                          color: colorsConstants.primaryRed),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  HeightBox(10.h),
                                  ...List.generate(
                                    partsController.stockRecords.length,
                                    (index) {
                                      return Container(
                                          margin: EdgeInsets.only(
                                              top: 5, bottom: 5, left: 5),
                                          padding: REdgeInsets.only(
                                            left: 5,
                                            right: 10,
                                          ),
                                          color: index % 2 == 0
                                              ? Colors.white
                                              : Colors.grey[300],
                                          child: Row(
                                            children: [
                                              Text(partsController
                                                      .stockRecords[index]
                                                      .$1
                                                      .name ??
                                                  ""),
                                            ],
                                          ));
                                    },
                                  )
                                  // ...partsController.stockRecords
                                  //     .map((stockRecord) {
                                  //   return Container(
                                  //     color: ,
                                  //     child: Text(stockRecord.$1.name ?? ""));
                                  // })
                                ],
                              ),
                            ),
                            // const WidthBox(10),
                            IntrinsicWidth(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        right: 10.0,
                                        left: 5,
                                        top: 5,
                                        bottom: 5),
                                    child: Text(
                                      "PART_NUMBER".toUpperCase(),
                                      style: TextStyle(
                                          color: colorsConstants.primaryRed),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  HeightBox(10.h),
                                  ...List.generate(
                                    partsController.stockRecords.length,
                                    (index) {
                                      return Container(
                                          margin: EdgeInsets.only(
                                            top: 5,
                                            bottom: 5,
                                          ),
                                          padding: const EdgeInsets.only(
                                              left: 5, right: 10.0),
                                          color: index % 2 == 0
                                              ? Colors.white
                                              : Colors.grey[300],
                                          child: Row(
                                            children: [
                                              Text(partsController
                                                          .stockRecords[index]
                                                          .$1
                                                          .partNumber ==
                                                      "null"
                                                  ? "-"
                                                  : partsController
                                                          .stockRecords[index]
                                                          .$1
                                                          .partNumber ??
                                                      ""),
                                            ],
                                          ));
                                    },
                                  )
                                  // ...partsController.stockRecords
                                  //     .map((stockRecord) {
                                  //   return Text(stockRecord.$1.partNumber ==
                                  //           "null"
                                  //       ? "-"
                                  //       : stockRecord.$1.partNumber ?? "");
                                  // })
                                ],
                              ),
                            ),
                            // const WidthBox(10),
                            IntrinsicWidth(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        right: 10.0,
                                        left: 5,
                                        top: 5,
                                        bottom: 5),
                                    child: Text(
                                      "MANUFACTURER".toUpperCase(),
                                      style: TextStyle(
                                          color: colorsConstants.primaryRed),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  HeightBox(10.h),
                                  ...List.generate(
                                    partsController.stockRecords.length,
                                    (index) {
                                      return Container(
                                          margin: EdgeInsets.only(
                                            top: 5,
                                            bottom: 5,
                                          ),
                                          padding: const EdgeInsets.only(
                                              left: 5, right: 10.0),
                                          color: index % 2 == 0
                                              ? Colors.white
                                              : Colors.grey[300],
                                          child: Row(
                                            children: [
                                              Text(partsController
                                                          .stockRecords[index]
                                                          .$1
                                                          .partNumber ==
                                                      "null"
                                                  ? "-"
                                                  : partsController
                                                          .stockRecords[index]
                                                          .$1
                                                          .partNumber ??
                                                      ""),
                                            ],
                                          ));
                                    },
                                  )
                                  // ...partsController.stockRecords
                                  //     .map((stockRecord) {
                                  //   return Text(stockRecord.$1.manufacturer ==
                                  //           "null"
                                  //       ? "-"
                                  //       : stockRecord.$1.manufacturer ?? "");
                                  // })
                                ],
                              ),
                            ),
                            // const WidthBox(10),
                            // Column(
                            //   crossAxisAlignment: CrossAxisAlignment.start,
                            //   children: [
                            //     Text(
                            //       "CATEGORY".toUpperCase(),
                            //       style: TextStyle(color: colorsConstants.primaryRed),
                            //       overflow: TextOverflow.ellipsis,
                            //     ),
                            //     HeightBox(10.h),
                            //     ...partsController.stockRecords.map((stockRecord) {
                            //       return Text(stockRecord.$1.category ?? "");
                            //     })
                            //   ],
                            // ),
                            // const WidthBox(10),
                            // Column(
                            //   crossAxisAlignment: CrossAxisAlignment.start,
                            //   children: [
                            //     Text(
                            //       "MAX STOCK".toUpperCase(),
                            //       style: TextStyle(color: colorsConstants.primaryRed),
                            //       overflow: TextOverflow.ellipsis,
                            //     ),
                            //     HeightBox(10.h),
                            //     ...partsController.stockRecords.map((stockRecord) {
                            //       return Text(
                            //           (stockRecord.$2.maxStock ?? 0).toString());
                            //     })
                            //   ],
                            // ),
                            // const WidthBox(10),
                            IntrinsicWidth(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        right: 10.0,
                                        left: 5,
                                        top: 5,
                                        bottom: 5),
                                    child: Text(
                                      "MOQ".toUpperCase(),
                                      style: TextStyle(
                                          color: colorsConstants.primaryRed),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  HeightBox(10.h),
                                  ...List.generate(
                                    partsController.stockRecords.length,
                                    (index) {
                                      return Container(
                                          margin: EdgeInsets.only(
                                            top: 5,
                                            bottom: 5,
                                          ),
                                          padding: const EdgeInsets.only(
                                              left: 5, right: 10.0),
                                          color: index % 2 == 0
                                              ? Colors.white
                                              : Colors.grey[300],
                                          child: Row(
                                            children: [
                                              Text((partsController
                                                          .stockRecords[index]
                                                          .$2
                                                          .minStock ??
                                                      0)
                                                  .toString()),
                                            ],
                                          ));
                                    },
                                  )
                                  // ...partsController.stockRecords
                                  //     .map((stockRecord) {
                                  //   return Text((stockRecord.$2.minStock ?? 0)
                                  //       .toString());
                                  // })
                                ],
                              ),
                            ),
                            // const WidthBox(10),

                            IntrinsicWidth(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        right: 10.0,
                                        left: 5,
                                        top: 5,
                                        bottom: 5),
                                    child: Text(
                                      "CURRENT_STOCK".toUpperCase(),
                                      style: TextStyle(
                                          color: colorsConstants.primaryRed),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  HeightBox(10.h),
                                  ...List.generate(
                                    partsController.stockRecords.length,
                                    (index) {
                                      return Container(
                                          margin: EdgeInsets.only(
                                            top: 5,
                                            bottom: 5,
                                          ),
                                          padding: const EdgeInsets.only(
                                              left: 5, right: 10.0),
                                          color: index % 2 == 0
                                              ? Colors.white
                                              : Colors.grey[300],
                                          child: Row(
                                            children: [
                                              Text((partsController
                                                          .stockRecords[index]
                                                          .$2
                                                          .currentStock ??
                                                      0)
                                                  .toString()),
                                            ],
                                          ));
                                    },
                                  )
                                  // ...partsController.stockRecords
                                  //     .map((stockRecord) {
                                  //   return Text(
                                  //       (stockRecord.$2.currentStock ?? 0)
                                  //           .toString());
                                  // })
                                ],
                              ),
                            ),
                            // const WidthBox(10),
                            IntrinsicWidth(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        right: 10.0,
                                        left: 5,
                                        top: 5,
                                        bottom: 5),
                                    child: Text(
                                      "GST_RATE".toUpperCase(),
                                      style: TextStyle(
                                          color: colorsConstants.primaryRed),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  HeightBox(10.h),
                                  ...List.generate(
                                    partsController.stockRecords.length,
                                    (index) {
                                      return Container(
                                          margin: EdgeInsets.only(
                                            top: 5,
                                            bottom: 5,
                                          ),
                                          padding: const EdgeInsets.only(
                                              left: 5, right: 10.0),
                                          color: index % 2 == 0
                                              ? Colors.white
                                              : Colors.grey[300],
                                          child: Row(
                                            children: [
                                              Text((partsController
                                                          .stockRecords[index]
                                                          .$1
                                                          .gstRate ??
                                                      0)
                                                  .toString()),
                                            ],
                                          ));
                                    },
                                  )
                                  // ...partsController.stockRecords
                                  //     .map((stockRecord) {
                                  //   return Text((stockRecord.$1.gstRate ?? 0)
                                  //       .toString());
                                  // })
                                ],
                              ),
                            ),
                            // const WidthBox(10),

                            IntrinsicWidth(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        right: 10.0,
                                        left: 5,
                                        top: 5,
                                        bottom: 5),
                                    child: Text(
                                      "MRP".toUpperCase(),
                                      style: TextStyle(
                                          color: colorsConstants.primaryRed),
                                    ),
                                  ),
                                  HeightBox(10.h),
                                  ...List.generate(
                                    partsController.stockRecords.length,
                                    (index) {
                                      return Container(
                                          margin: EdgeInsets.only(
                                            top: 5,
                                            bottom: 5,
                                          ),
                                          padding: const EdgeInsets.only(
                                              left: 5, right: 10.0),
                                          color: index % 2 == 0
                                              ? Colors.white
                                              : Colors.grey[300],
                                          child: Row(
                                            children: [
                                              Text(partsController
                                                  .stockRecords[index].$2.mrp
                                                  .toString()),
                                            ],
                                          ));
                                    },
                                  )
                                  // ...partsController.stockRecords
                                  //     .map((stockRecord) {
                                  //   return Text(
                                  //       stockRecord.$2.mrp.toString());
                                  // })
                                ],
                              ),
                            ),
                            // const WidthBox(10),
                            // Column(
                            //   crossAxisAlignment: CrossAxisAlignment.start,
                            //   children: [
                            //     Text(
                            //       "MRP".toUpperCase(),
                            //       style: TextStyle(color: colorsConstants.primaryRed),
                            //     ),
                            //     HeightBox(10.h),
                            //     ...partsController.stockRecords.map((stockRecord) {
                            //       return Text(stockRecord.$2.mrp.toString());
                            //     })
                            //   ],
                            // ),
                            // const WidthBox(10),

                            IntrinsicWidth(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        right: 10.0,
                                        left: 5,
                                        top: 5,
                                        bottom: 5),
                                    child: Text(
                                      "PURCHASE_PRICE".toUpperCase(),
                                      style: TextStyle(
                                          color: colorsConstants.primaryRed),
                                    ),
                                  ),
                                  HeightBox(10.h),
                                  ...List.generate(
                                    partsController.stockRecords.length,
                                    (index) {
                                      return Container(
                                          margin: EdgeInsets.only(
                                            top: 5,
                                            bottom: 5,
                                          ),
                                          padding: const EdgeInsets.only(
                                              left: 5, right: 10.0),
                                          color: index % 2 == 0
                                              ? Colors.white
                                              : Colors.grey[300],
                                          child: Row(
                                            children: [
                                              Text(partsController
                                                  .stockRecords[index]
                                                  .$2
                                                  .purchasePrice
                                                  .toString()),
                                            ],
                                          ));
                                    },
                                  )
                                  // ...partsController.stockRecords
                                  //     .map((stockRecord) {
                                  //   return Text(stockRecord.$2.purchasePrice
                                  //       .toString());
                                  // })
                                ],
                              ),
                            ),
                            // const WidthBox(10),

                            IntrinsicWidth(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        right: 10.0,
                                        left: 5,
                                        top: 5,
                                        bottom: 5),
                                    child: Text(
                                      "VENDOR".toUpperCase(),
                                      style: TextStyle(
                                          color: colorsConstants.primaryRed),
                                    ),
                                  ),
                                  HeightBox(10.h),
                                  ...List.generate(
                                    partsController.stockRecords.length,
                                    (index) {
                                      String? vendorName =
                                          Get.find<HomeScreenController>()
                                              .vendors
                                              .firstWhereOrNull((element) =>
                                                  element.vendorId ==
                                                  partsController
                                                      .stockRecords[index]
                                                      .$2
                                                      .vendorId)
                                              ?.name;
                                      return Container(
                                          margin: EdgeInsets.only(
                                            top: 5,
                                            bottom: 5,
                                          ),
                                          padding: const EdgeInsets.only(
                                              left: 5, right: 10.0),
                                          color: index % 2 == 0
                                              ? Colors.white
                                              : Colors.grey[300],
                                          child: Row(
                                            children: [
                                              Text(
                                                vendorName ?? "",
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ],
                                          ));
                                    },
                                  )
                                  // ...partsController.stockRecords
                                  //     .map((stockRecord) {
                                  //   String? vendorName =
                                  //       Get.find<HomeScreenController>()
                                  //           .vendors
                                  //           .firstWhereOrNull((element) =>
                                  //               element.vendorId ==
                                  //               stockRecord.$2.vendorId)
                                  //           ?.name;
                                  //   return Text(
                                  //     vendorName ?? "",
                                  //     overflow: TextOverflow.ellipsis,
                                  //   );
                                  // })
                                ],
                              ),
                            ),
                            //  const WidthBox(10),

                            // Column(
                            //   crossAxisAlignment: CrossAxisAlignment.start,
                            //   children: [
                            //     Text(
                            //       "Rack No.".toUpperCase(),
                            //       style: TextStyle(color: colorsConstants.primaryRed),
                            //     ),
                            //     HeightBox(10.h),
                            //     ...partsController.stockRecords.map((stockRecord) {
                            //       return Text(
                            //         stockRecord.$2.rackId ?? "",
                            //         overflow: TextOverflow.ellipsis,
                            //       );
                            //     })
                            //   ],
                            // ),
                            // const WidthBox(10),
                            // Column(
                            //   crossAxisAlignment: CrossAxisAlignment.start,
                            //   children: [
                            //     Text(
                            //       "VENDOR",
                            //       style: TextStyle(color: colorsConstants.primaryRed),
                            //     ),
                            //     HeightBox(10.h),
                            //     ...partsController.stockRecords.map((stockRecord) {
                            //       return Text(
                            //         stockRecord.$2.vendorId ?? "",
                            //         overflow: TextOverflow.ellipsis,
                            //       );
                            //     })
                            //   ],
                            // ),
                            // const WidthBox(10),

                            // Column(
                            //   crossAxisAlignment: CrossAxisAlignment.start,
                            //   children: [
                            //     Text(
                            //       "APPLICABLE BRANDS".toUpperCase(),
                            //       style: TextStyle(color: colorsConstants.primaryRed),
                            //     ),
                            //     HeightBox(10.h),
                            //     ...partsController.stockRecords.map((stockRecord) {
                            //       return stockRecord.$1.applicableBrands != null &&
                            //               stockRecord.$1.applicableBrands!.isNotEmpty
                            //           ? Text(
                            //               stockRecord.$1.applicableBrands?.first ??
                            //                   "",
                            //               overflow: TextOverflow.ellipsis,
                            //             )
                            //           : const Text(
                            //               '',
                            //               overflow: TextOverflow.ellipsis,
                            //             );
                            //     })
                            //   ],
                            // ),
                            // const WidthBox(10),

                            // Column(
                            //   crossAxisAlignment: CrossAxisAlignment.start,
                            //   children: [
                            //     Text(
                            //       "APPLICABLE MODELS".toUpperCase(),
                            //       style: TextStyle(color: colorsConstants.primaryRed),
                            //     ),
                            //     HeightBox(10.h),
                            //     ...partsController.stockRecords.map((stockRecord) {
                            //       return stockRecord.$1.applicableModels != null &&
                            //               stockRecord.$1.applicableModels!.isNotEmpty
                            //           ? Text(
                            //               stockRecord.$1.applicableModels?.first ??
                            //                   "",
                            //               overflow: TextOverflow.ellipsis,
                            //             )
                            //           : const Text(
                            //               '',
                            //               overflow: TextOverflow.ellipsis,
                            //             );
                            //     })
                            //   ],
                            // ),
                          ],
                        ),
                      ),
                    ),
                  ),

            HeightBox(16.h),

            partsController.stockRecords.isEmpty
                ? SizedBox()
                : Container(
                    margin: EdgeInsets.symmetric(horizontal: 16.w),
                    padding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 16.h),
                    decoration: BoxDecoration(
                      color: colorsConstants.whiteColor,
                      boxShadow: Constants.boxShadow,
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            children: [
                              Text(
                                "Total Parts",
                                style: TextStyle(fontSize: 12.sp),
                              ),
                              Text(
                                partsController.stockRecords.length.toString(),
                                style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            children: [
                              Text(
                                "Total Stock",
                                style: TextStyle(fontSize: 12.sp),
                              ),
                              Text(
                                partsController.stockRecords.fold<double>(0,
                                    (preValue, element) {
                                  return (preValue +
                                          (element.$2.currentStock ?? 0.0))
                                      .toDouble();
                                }).toString(),
                                style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Column(
                            children: [
                              Text(
                                "Total Stock Value",
                                style: TextStyle(fontSize: 12.sp),
                              ),
                              Text(
                                "${Constants.rupeeSign} ${partsController.stockRecords.fold<double>(0, (preValue, element) {
                                  return preValue +
                                      ((element.$2.purchasePrice ?? 0) *
                                          (element.$2.currentStock ?? 00));
                                }).toStringAsFixed(2)}",
                                style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
          ],
        ),
      );
    });
  }
}

class ButtonWithIcon extends StatelessWidget {
  const ButtonWithIcon({
    super.key,
    required this.title,
    required this.icon,
    required this.onPress,
    this.backgroundColor,
  });
  final String title;
  final IconData icon;
  final void Function() onPress;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPress,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
        decoration: BoxDecoration(
          boxShadow: Constants.boxShadow,
          color: backgroundColor ?? const Color.fromARGB(255, 9, 64, 83),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: colorsConstants.whiteColor,
              size: 20.sp,
            ),
            WidthBox(5.w),
            Text(
              title,
              style:
                  TextStyle(color: colorsConstants.whiteColor, fontSize: 12.sp),
            ),
          ],
        ),
      ),
    );
  }
}
