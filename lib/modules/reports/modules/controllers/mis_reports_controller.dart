import 'dart:convert';
import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:csv/csv.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:printing/printing.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/vehicle_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/modules/reports/modules/models/repair_order_all_details_model.dart';
import 'package:speed_force_franchise/repositories/customers_repository.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import 'package:path_provider/path_provider.dart';

class MISReportController extends GetxController {
  bool isLoading = false;

  TextEditingController searchTextEditingController = TextEditingController();

  List<RepairOrderAllDetailsModel> repairOrdersData = [];
  List<RepairOrderAllDetailsModel> searchedRepairOrdersData = [];

  List<String> headers = [
    "Sr No",
    "Job Card Number",
    "Customer Name",
    "Contact No",
    "Make",
    "Model",
    "Parts Total",
    "Services Total",
    "Oil",
  ];

  List<List<String>> rows = [];

  QueryDocumentSnapshot<Object?>? lastVisible;

  Future<void> fetchMISReport() async {
    try {
      isLoading = true;
      update();

      QuerySnapshot<Map<String, dynamic>> ordersQuerySnapshot =
          lastVisible == null
              ? await FirebaseFirestore.instance
                  .collection(FirebaseCollections.orders.name)
                  .where("franchiseId",
                      isEqualTo: FirebaseAuth.instance.currentUser?.uid ?? "")
                  .limit(25)
                  .get()
              : await FirebaseFirestore.instance
                  .collection(FirebaseCollections.orders.name)
                  .where("franchiseId",
                      isEqualTo: FirebaseAuth.instance.currentUser?.uid ?? "")
                  .startAfter([lastVisible])
                  .limit(25)
                  .get();

      lastVisible =
          ordersQuerySnapshot.docs[ordersQuerySnapshot.docs.length - 1];

      for (QueryDocumentSnapshot<
              Map<String, dynamic>> orderQueryDocumentSnapshot
          in ordersQuerySnapshot.docs) {
        RepairOrderModel repairOrderModel = RepairOrderModel.fromMap(
            orderQueryDocumentSnapshot.id, orderQueryDocumentSnapshot.data());

        CustomerDetailsModel? customerDetailsModel =
            await CustomersRepository.fetchCustomer(
                customerId: repairOrderModel.customerId ?? "");

        RepairOrderAllDetailsModel repairOrderAllDetailsModel =
            RepairOrderAllDetailsModel(
                repairOrderModel: repairOrderModel,
                customerDetailsModel: customerDetailsModel);

        QuerySnapshot<Map<String, dynamic>> customerVehiclesQuerySnapshot =
            await FirebaseFirestore.instance
                .collection(FirebaseCollections.customerVehicles.name)
                .where("franchiseId",
                    isEqualTo: FirebaseAuth.instance.currentUser?.uid ?? "")
                .where("customerId",
                    isEqualTo: customerDetailsModel?.customerId)
                .get();

        for (QueryDocumentSnapshot<
                Map<String, dynamic>> customerVehiclesQueryDocumentSnapshot
            in customerVehiclesQuerySnapshot.docs) {
          VehicleDetailsModel vehicleDetailsModel = VehicleDetailsModel.fromMap(
              customerVehiclesQueryDocumentSnapshot.data());

          repairOrderAllDetailsModel.vehicleDetailsModel = vehicleDetailsModel;
        }
        repairOrdersData.add(repairOrderAllDetailsModel);
      }

      if (repairOrdersData.isNotEmpty) {
        generateDataForCSV(repairOrderAllDetailsModels: repairOrdersData);
      }

      isLoading = false;
      update();
    } catch (e) {
      isLoading = false;
      update();
    }
  }

  void searchCustomerByName({required String name}) {
    if (name.isEmpty) {
      rows.clear();
      generateDataForCSV(repairOrderAllDetailsModels: repairOrdersData);
      update(["misReports"]);
    } else {
      searchedRepairOrdersData =
          repairOrdersData.where((repairOrderAllDetailsModel) {
        return repairOrderAllDetailsModel.customerDetailsModel!.username!
            .toLowerCase()
            .contains(name.toLowerCase());
      }).toList();

      rows.clear();

      generateDataForCSV(repairOrderAllDetailsModels: searchedRepairOrdersData);

      update(["misReports"]);
    }
  }

  void generateDataForCSV(
      {required List<RepairOrderAllDetailsModel> repairOrderAllDetailsModels}) {
    int index = 0;

    for (RepairOrderAllDetailsModel repairOrderAllDetailsModel
        in repairOrderAllDetailsModels) {
      List<String> rowData = [(++index).toString()];
      rowData
          .add(repairOrderAllDetailsModel.repairOrderModel.jobCardId ?? "NA");
      rowData.add(
          repairOrderAllDetailsModel.customerDetailsModel?.username ?? "NA");

      rowData
          .add(repairOrderAllDetailsModel.customerDetailsModel?.phone ?? "NA");
      rowData.add(repairOrderAllDetailsModel.vehicleDetailsModel?.make ?? "NA");
      rowData
          .add(repairOrderAllDetailsModel.vehicleDetailsModel?.model ?? "NA");
      rowData.add(repairOrderAllDetailsModel
              .repairOrderModel.repairDetailsModel?.parts
              ?.fold<double>(
            0,
            (previousValue, element) {
              if ((element.isOil == null || !element.isOil!)) {
                if (element.amount != null) {
                  return (previousValue + element.amount!);
                }
              }
              return previousValue;
            },
          ).toString() ??
          "");

      rowData.add(repairOrderAllDetailsModel
              .repairOrderModel.repairDetailsModel?.services
              ?.fold<double>(
            0,
            (previousValue, element) {
              if (element.amount != null) {
                return (previousValue + element.amount!);
              }
              return previousValue;
            },
          ).toString() ??
          "");

      rowData.add(repairOrderAllDetailsModel
              .repairOrderModel.repairDetailsModel?.parts
              ?.fold<double>(
            0,
            (previousValue, element) {
              if ((element.isOil != null && element.isOil!)) {
                if (element.amount != null) {
                  return (previousValue + element.amount!);
                }
              }
              return previousValue;
            },
          ).toString() ??
          "");

      rows.add(rowData);
    }

    // String csv = const ListToCsvConverter().convert(rows);

    // downlaodCSVFile(csv: csv);
  }

  Future<void> downlaodCSVFile() async {
    String csv = const ListToCsvConverter().convert(rows);

    try {
      await Utils.requestPermissions(permissionsList: [
        Platform.isIOS ? Permission.storage : Permission.manageExternalStorage
      ]);
      // Load the file from assets
      final Uint8List data = utf8.encode(csv);
      final buffer = data.buffer.asUint8List();

      Directory? directory;

      // Get the path to the local storage
      if (Platform.isAndroid) {
// Redirects it to download folder in android
        directory = Directory("/storage/emulated/0/Download");
      } else {
        directory = await getApplicationDocumentsDirectory();
      }
      final path = '${directory.path}/mis_report.csv';

      // Write the file to local storage
      final file = File(path);
      await file.writeAsBytes(buffer);

      Utils.showSnackBar(title: "File downloaded to $path");

      await Printing.sharePdf(
          bytes: await file.readAsBytes(), filename: "mis_report.csv");
    } catch (e) {
      Utils.showSnackBar(title: "Error $e");
    }
  }
}
