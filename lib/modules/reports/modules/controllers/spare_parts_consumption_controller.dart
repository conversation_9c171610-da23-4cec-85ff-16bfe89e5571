import 'dart:convert';
import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:csv/csv.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:printing/printing.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/service_part.model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/vehicle_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/modules/reports/modules/models/repair_order_all_details_model.dart';
import 'package:speed_force_franchise/repositories/customers_repository.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import 'package:path_provider/path_provider.dart';

class SparePartsConsumptionController extends GetxController {
  bool isLoading = false;

  TextEditingController searchTextEditingController = TextEditingController();

  List<RepairOrderAllDetailsModel> repairOrdersData = [];
  List<RepairOrderAllDetailsModel> searchedRepairOrdersData = [];
  DateTimeRange selectedDateRange = DateTimeRange(
      start: DateTime.utc(
          DateTime.now().year, DateTime.now().month, DateTime.now().day),
      end: DateTime.utc(
          DateTime.now().year, DateTime.now().month, DateTime.now().day));
  // List<String> headers = [
  //   "Sr No",
  //   "Part Description",
  //   "Part Number",
  //   "Make",
  //   "Model",
  //   "Quantity",
  //   "MRP"
  // ];
  List<String> headers = [
    "Sr No",
    "Date",
    "Invoice No",
    "Customer Name",
    "Gross Value",
    "Discount",
    "Taxable Amout",
    "CGST",
    "SGST",
    "IGST",
    "Total",
    "Recieved Amount",
    "Payment Details"
  ];

  List<List<String>> rows = [];

  // QueryDocumentSnapshot<Object?>? lastVisible;

  Future<void> fetchMonthlySparePartsConsumptionReport() async {
    try {
      isLoading = true;
      update();
      rows.clear();
      repairOrdersData.clear();
      QuerySnapshot<Map<String, dynamic>> ordersQuerySnapshot =
          // lastVisible == null ?
          await FirebaseFirestore.instance
              .collection(FirebaseCollections.orders.name)
              .where("franchiseId",
                  isEqualTo: FirebaseAuth.instance.currentUser?.uid ?? "")
              .where('completionDate',
                  isGreaterThanOrEqualTo: selectedDateRange.start)
              .where('completionDate',
                  isLessThanOrEqualTo:
                      selectedDateRange.end.add(const Duration(days: 1)))
              .orderBy('completionDate', descending: true)
              .get();
      // : await FirebaseFirestore.instance
      //     .collection(FirebaseCollections.orders.name)
      //     .orderBy('completionDate', descending: true)
      //     .where("franchiseId",
      //         isEqualTo: FirebaseAuth.instance.currentUser?.uid ?? "")
      //     .startAfter([lastVisible?.id])
      //     .limit(25)
      //     .get();

      // lastVisible =
      //     ordersQuerySnapshot.docs[ordersQuerySnapshot.docs.length - 1];

      for (QueryDocumentSnapshot<
              Map<String, dynamic>> orderQueryDocumentSnapshot
          in ordersQuerySnapshot.docs) {
        RepairOrderModel repairOrderModel = RepairOrderModel.fromMap(
            orderQueryDocumentSnapshot.id, orderQueryDocumentSnapshot.data());
        print(repairOrderModel.gstIncluded);

        CustomerDetailsModel? customerDetailsModel =
            await CustomersRepository.fetchCustomer(
                customerId: repairOrderModel.customerId ?? "");

        RepairOrderAllDetailsModel repairOrderAllDetailsModel =
            RepairOrderAllDetailsModel(
                repairOrderModel: repairOrderModel,
                customerDetailsModel: customerDetailsModel);

        QuerySnapshot<Map<String, dynamic>> customerVehiclesQuerySnapshot =
            await FirebaseFirestore.instance
                .collection(FirebaseCollections.customerVehicles.name)
                .where("franchiseId",
                    isEqualTo: FirebaseAuth.instance.currentUser?.uid ?? "")
                .where("customerId",
                    isEqualTo: customerDetailsModel?.customerId)
                .get();

        for (QueryDocumentSnapshot<
                Map<String, dynamic>> customerVehiclesQueryDocumentSnapshot
            in customerVehiclesQuerySnapshot.docs) {
          VehicleDetailsModel vehicleDetailsModel = VehicleDetailsModel.fromMap(
              customerVehiclesQueryDocumentSnapshot.data());

          repairOrderAllDetailsModel.vehicleDetailsModel = vehicleDetailsModel;
        }
        repairOrdersData.add(repairOrderAllDetailsModel);
      }

      if (repairOrdersData.isNotEmpty) {
        generateDataForCSV(repairOrderAllDetailsModels: repairOrdersData);
      }

      isLoading = false;
      update();
    } catch (e) {
      print(e.toString());
      isLoading = false;
      update();
    }
  }

  void searchFromTable({required String searchTerm}) {
    if (searchTerm.isEmpty) {
      rows.clear();
      generateDataForCSV(repairOrderAllDetailsModels: repairOrdersData);
      update(["sparePartsConsumptionTable"]);
    } else {
      searchedRepairOrdersData =
          repairOrdersData.fold<List<RepairOrderAllDetailsModel>>(
        <RepairOrderAllDetailsModel>[],
        (previousValue, element) {
          List<ServicePartModel>? matchedParts =
              element.repairOrderModel.repairDetailsModel?.parts?.where((part) {
            if (part.partServiceNumber
                    ?.toLowerCase()
                    .contains(searchTerm.toLowerCase()) ??
                false) {
              return true;
            }
            return part.title
                    ?.toLowerCase()
                    .contains(searchTerm.toLowerCase()) ??
                false;
          }).toList();

          if (matchedParts != null && matchedParts.isNotEmpty) {
            RepairOrderAllDetailsModel matchedRepairOrderAllDetailsModel =
                RepairOrderAllDetailsModel.fromMap(element.toMap());

            matchedRepairOrderAllDetailsModel
                .repairOrderModel.repairDetailsModel?.parts = matchedParts;
            previousValue.add(matchedRepairOrderAllDetailsModel);
          } else {
            if (element.vehicleDetailsModel?.make
                    ?.toLowerCase()
                    .contains(searchTerm.toLowerCase()) ??
                false) {
              previousValue.add(element);
            } else if (element.vehicleDetailsModel?.model
                    ?.toLowerCase()
                    .contains(searchTerm.toLowerCase()) ??
                false) {
              previousValue.add(element);
            }
          }

          return previousValue;
        },
      );

      rows.clear();

      generateDataForCSV(repairOrderAllDetailsModels: searchedRepairOrdersData);

      update(["sparePartsConsumptionTable"]);
    }
  }

  void generateDataForCSV(
      {required List<RepairOrderAllDetailsModel> repairOrderAllDetailsModels}) {
    int index = 1;

    for (RepairOrderAllDetailsModel repairOrderAllDetailsModel
        in repairOrderAllDetailsModels) {
      double grossvalue = 0.0;
      double tax = 0.0;
      for (ServicePartModel element in repairOrderAllDetailsModel
              .repairOrderModel.repairDetailsModel?.parts ??
          []) {
        grossvalue += element.purchasePrice ?? 0;
        tax += element.partsGst ?? 0;
      }
      for (ServicePartModel element in repairOrderAllDetailsModel
              .repairOrderModel.repairDetailsModel?.services ??
          []) {
        tax += element.servicesGst ?? 0;
      }
      List<String> rowData = [(index).toString()];
      rowData.add(repairOrderAllDetailsModel.repairOrderModel.completionDate
              ?.toDate()
              ?.goodDayDate()
              ?.toString() ??
          "NA");
      rowData.add(
          repairOrderAllDetailsModel.repairOrderModel.jobCardId?.toString() ??
              "NA");
      rowData.add(repairOrderAllDetailsModel
              .repairOrderModel.customerDetailsModel?.username ??
          "NA");
      rowData.add(grossvalue == 0.0 ? "NA" : grossvalue.toStringAsFixed(2));
      rowData.add(repairOrderAllDetailsModel
              .repairOrderModel.repairDetailsModel?.discount
              ?.toStringAsFixed(2) ??
          "NA");
      rowData.add(tax == 0.0 ? "NA" : tax.toStringAsFixed(2));
      rowData.add((((repairOrderAllDetailsModel.repairOrderModel.gstIncluded) ??
                      false) &&
                  !(repairOrderAllDetailsModel.repairOrderModel.isigst ??
                      false)) ==
              true
          ? (tax / 2).toStringAsFixed(2)
          : "NA");
      rowData.add((((repairOrderAllDetailsModel.repairOrderModel.gstIncluded) ??
                      false) &&
                  !(repairOrderAllDetailsModel.repairOrderModel.isigst ??
                      false)) ==
              true
          ? (tax / 2).toStringAsFixed(2)
          : "NA");
      rowData.add((((repairOrderAllDetailsModel.repairOrderModel.gstIncluded) ??
                      false) &&
                  (repairOrderAllDetailsModel.repairOrderModel.isigst ??
                      false)) ==
              true
          ? (tax).toStringAsFixed(2)
          : "NA");
      rowData.add(repairOrderAllDetailsModel
              .repairOrderModel.repairDetailsModel?.total
              ?.toStringAsFixed(2) ??
          "NA");
      rowData.add(repairOrderAllDetailsModel
              .repairOrderModel.repairDetailsModel?.paymentReceived
              ?.toStringAsFixed(2) ??
          "NA");
      rowData.add(repairOrderAllDetailsModel.repairOrderModel.paymentMode?.name
              .toString() ??
          "NA");
      rows.add(rowData);
      // for (ServicePartModel part in repairOrderAllDetailsModel
      //         .repairOrderModel.repairDetailsModel?.parts ??
      //     []) {

      // }
      index = index + 1;
    }

    // String csv = const ListToCsvConverter().convert(rows);

    // downlaodCSVFile(csv: csv);
  }

  Future<void> downlaodCSVFile() async {
    String csv = const ListToCsvConverter().convert(rows);

    try {
      // await Utils.requestPermissions(permissionsList: [
      //   Platform.isIOS ? Permission.storage : Permission.manageExternalStorage
      // ]);
      // Load the file from assets
      final Uint8List data = utf8.encode(csv);
      final buffer = data.buffer.asUint8List();

      // Directory? directory;

      // Get the path to the local storage
//       if (Platform.isAndroid) {
// // Redirects it to download folder in android
//         directory = Directory("/storage/emulated/0/Download");
//       } else {
//         directory = await getApplicationDocumentsDirectory();
//       }
//       final path = '${directory.path}/spare_parts_consumption.csv';

//       // Write the file to local storage
//       final file = File(path);
//       await file.writeAsBytes(buffer);

      // Utils.showSnackBar(title: "File downloaded to $path");

      await Printing.sharePdf(
          bytes: buffer, filename: "spare_parts_consumption.csv");
    } catch (e) {
      Utils.showSnackBar(title: "Error $e");
    }
  }
}
