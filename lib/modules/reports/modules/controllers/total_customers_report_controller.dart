import 'dart:convert';
import 'dart:typed_data';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:csv/csv.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:printing/printing.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/vehicle_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/modules/reports/modules/models/customer_all_details_model.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import 'package:path_provider/path_provider.dart';

class TotalCustomersReportController extends GetxController {
  bool isLoading = false;

  TextEditingController searchTextEditingController = TextEditingController();

  List<CustomerAllDetailsModel> customersData = [];
  List<CustomerAllDetailsModel> searchedCustomerData = [];

  List<String> headers = [
    "Sr No",
    "Customer Name",
    "Contact",
    "Make",
    "Model",
    "Last Service Date",
    "Job Card Number"
  ];

  List<List<String>> rows = [];

  QueryDocumentSnapshot<Object?>? lastVisible;

  Future<void> fetchTotalCustomersReport() async {
    try {
      isLoading = true;
      update();

      QuerySnapshot<Map<String, dynamic>> customersQuerySnapshot =
          lastVisible == null
              ? await FirebaseFirestore.instance
                  .collection(FirebaseCollections.customers.name)
                  .where("franchiseId",
                      isEqualTo: FirebaseAuth.instance.currentUser?.uid ?? "")
                  .limit(25)
                  .get()
              : await FirebaseFirestore.instance
                  .collection(FirebaseCollections.customers.name)
                  .where("franchiseId",
                      isEqualTo: FirebaseAuth.instance.currentUser?.uid ?? "")
                  .startAfter([lastVisible])
                  .limit(25)
                  .get();

      lastVisible =
          customersQuerySnapshot.docs[customersQuerySnapshot.docs.length - 1];

      for (QueryDocumentSnapshot<
              Map<String, dynamic>> customerQueryDocumentSnapshot
          in customersQuerySnapshot.docs) {
        CustomerDetailsModel customerDetailsModel =
            CustomerDetailsModel.fromMap(customerQueryDocumentSnapshot.data());

        CustomerAllDetailsModel customerAllDetailsModel =
            CustomerAllDetailsModel(
                customerDetailsModel: customerDetailsModel,
                vehicleAndRepairOrdersMap: {});

        QuerySnapshot<Map<String, dynamic>> customerVehiclesQuerySnapshot =
            await FirebaseFirestore.instance
                .collection(FirebaseCollections.customerVehicles.name)
                .where("franchiseId",
                    isEqualTo: FirebaseAuth.instance.currentUser?.uid ?? "")
                .where("customerId", isEqualTo: customerDetailsModel.customerId)
                .get();

        for (QueryDocumentSnapshot<
                Map<String, dynamic>> customerVehiclesQueryDocumentSnapshot
            in customerVehiclesQuerySnapshot.docs) {
          VehicleDetailsModel vehicleDetailsModel = VehicleDetailsModel.fromMap(
              customerVehiclesQueryDocumentSnapshot.data());

          customerAllDetailsModel
              .vehicleAndRepairOrdersMap?[vehicleDetailsModel] = [];

          QuerySnapshot<Map<String, dynamic>> repairOrderQuerySnapshot =
              await FirebaseFirestore.instance
                  .collection(FirebaseCollections.orders.name)
                  .where("franchiseId",
                      isEqualTo: FirebaseAuth.instance.currentUser?.uid ?? "")
                  .where("vehicleId", isEqualTo: vehicleDetailsModel.vehicleId)
                  .get();

          for (QueryDocumentSnapshot<
                  Map<String, dynamic>> repairQueryDocumentSnapshot
              in repairOrderQuerySnapshot.docs) {
            RepairOrderModel repairOrderModel = RepairOrderModel.fromMap(
                repairQueryDocumentSnapshot.id,
                repairQueryDocumentSnapshot.data());

            customerAllDetailsModel
                .vehicleAndRepairOrdersMap?[vehicleDetailsModel]
                ?.add(repairOrderModel);
          }
        }
        customersData.add(customerAllDetailsModel);
      }

      if (customersData.isNotEmpty) {
        generateDataForCSV(customerAllDetailsModels: customersData);
      }

      isLoading = false;
      update();
    } catch (e) {
      isLoading = false;
      update();
    }
  }

  void searchCustomerByName({required String name}) {
    if (name.isEmpty) {
      rows.clear();
      generateDataForCSV(customerAllDetailsModels: customersData);
      update(["totalCustomersTable"]);
    } else {
      searchedCustomerData = customersData.where((customer) {
        return customer.customerDetailsModel.username!
            .toLowerCase()
            .contains(name.toLowerCase());
      }).toList();

      rows.clear();

      generateDataForCSV(customerAllDetailsModels: searchedCustomerData);

      update(["totalCustomersTable"]);
    }
  }

  void generateDataForCSV(
      {required List<CustomerAllDetailsModel> customerAllDetailsModels}) {
    int index = 0;

    for (CustomerAllDetailsModel customerAllDetailsModels
        in customerAllDetailsModels) {
      if (customerAllDetailsModels.vehicleAndRepairOrdersMap!.entries.isEmpty) {
        List<String> rowData = [(++index).toString()];

        rowData
            .add(customerAllDetailsModels.customerDetailsModel.username ?? "");
        rowData.add(customerAllDetailsModels.customerDetailsModel.phone ?? "");
        rowData.add("-");
        rowData.add("-");
        rowData.add("-");
        rowData.add("-");

        rows.add(rowData);
      } else {
        for (var vehicleAndRepairData
            in customerAllDetailsModels.vehicleAndRepairOrdersMap!.entries) {
          List<String> rowData = [(++index).toString()];

          rowData.add(
              customerAllDetailsModels.customerDetailsModel.username ?? "NA");
          rowData
              .add(customerAllDetailsModels.customerDetailsModel.phone ?? "NA");
          rowData.add(vehicleAndRepairData.key.make ?? "NA");
          rowData.add(vehicleAndRepairData.key.model ?? "NA");
          rowData.add(DateFormat("dd-MM-y").format(DateTime.tryParse(
                      vehicleAndRepairData.value.first.createdAt!) ??
                  DateTime.now()) ??
              "NA");
          rowData.add(vehicleAndRepairData.value.first.jobCardId ?? "NA");

          rows.add(rowData);
        }
      }
    }

    // String csv = const ListToCsvConverter().convert(rows);

    // downlaodCSVFile(csv: csv);
  }

  Future<void> downlaodCSVFile() async {
    String csv = const ListToCsvConverter().convert(rows);

    try {
      await Utils.requestPermissions(permissionsList: [
        Platform.isIOS ? Permission.storage : Permission.manageExternalStorage
      ]);
      // Load the file from assets
      final Uint8List data = utf8.encode(csv);
      final buffer = data.buffer.asUint8List();

      Directory? directory;

      // Get the path to the local storage
      if (Platform.isAndroid) {
// Redirects it to download folder in android
        directory = Directory("/storage/emulated/0/Download");
      } else {
        directory = await getApplicationDocumentsDirectory();
      }
      final path = '${directory.path}/total_customers_report.csv';

      // Write the file to local storage
      final file = File(path);
      await file.writeAsBytes(buffer);

      Utils.showSnackBar(title: "File downloaded to $path");

      await Printing.sharePdf(
          bytes: await file.readAsBytes(),
          filename: "total_customers_report.csv");
    } catch (e) {
      Utils.showSnackBar(title: "Error $e");
    }
  }
}
