import 'dart:convert';

import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/vehicle_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';

class CustomerAllDetailsModel {
  final CustomerDetailsModel customerDetailsModel;

  Map<VehicleDetailsModel, List<RepairOrderModel>>? vehicleAndRepairOrdersMap;

  CustomerAllDetailsModel({
    required this.customerDetailsModel,
    this.vehicleAndRepairOrdersMap,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'customerDetailsModel': customerDetailsModel.toMap()});
    if (vehicleAndRepairOrdersMap != null) {
      result.addAll({'vehicleAndRepairOrdersMap': vehicleAndRepairOrdersMap});
    }

    return result;
  }

  factory CustomerAllDetailsModel.fromMap(Map<String, dynamic> map) {
    return CustomerAllDetailsModel(
      customerDetailsModel:
          CustomerDetailsModel.fromMap(map['customerDetailsModel']),
      vehicleAndRepairOrdersMap:
          Map<VehicleDetailsModel, List<RepairOrderModel>>.from(
              map['vehicleAndRepairOrdersMap']),
    );
  }

  String toJson() => json.encode(toMap());

  factory CustomerAllDetailsModel.fromJson(String source) =>
      CustomerAllDetailsModel.fromMap(json.decode(source));
}
