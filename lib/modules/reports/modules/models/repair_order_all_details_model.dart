import 'dart:convert';

import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/personal_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/vehicle_details_model.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';

class RepairOrderAllDetailsModel {
  RepairOrderModel repairOrderModel;
  CustomerDetailsModel? customerDetailsModel;
  VehicleDetailsModel? vehicleDetailsModel;

  RepairOrderAllDetailsModel({
    required this.repairOrderModel,
    this.customerDetailsModel,
    this.vehicleDetailsModel,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'repairOrderModel': repairOrderModel.toMap()});
    if (customerDetailsModel != null) {
      result.addAll({'customerDetailsModel': customerDetailsModel!.toMap()});
    }
    if (vehicleDetailsModel != null) {
      result.addAll({'vehicleDetailsModel': vehicleDetailsModel!.toMap()});
    }

    return result;
  }

  factory RepairOrderAllDetailsModel.fromMap(Map<String, dynamic> map) {
    return RepairOrderAllDetailsModel(
      repairOrderModel: RepairOrderModel.fromMap(null, map['repairOrderModel']),
      customerDetailsModel: map['customerDetailsModel'] != null
          ? CustomerDetailsModel.fromMap(map['customerDetailsModel'])
          : null,
      vehicleDetailsModel: map['vehicleDetailsModel'] != null
          ? VehicleDetailsModel.fromMap(map['vehicleDetailsModel'])
          : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory RepairOrderAllDetailsModel.fromJson(String source) =>
      RepairOrderAllDetailsModel.fromMap(json.decode(source));
}
