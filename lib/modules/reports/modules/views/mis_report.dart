import 'package:flutter/material.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:speed_force_franchise/modules/reports/modules/controllers/mis_reports_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class MISReport extends StatefulWidget {
  const MISReport({super.key});

  @override
  State<MISReport> createState() => _MISReportState();
}

class _MISReportState extends State<MISReport> {
  @override
  void initState() {
    super.initState();

    Get.put(MISReportController()..fetchMISReport());
  }

  @override
  void dispose() {
    Get.delete<MISReportController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<MISReportController>(builder: (misReportController) {
      if (misReportController.isLoading) {
        if (!context.loaderOverlay.visible) {
          context.loaderOverlay.show();
        }
      } else {
        if (context.loaderOverlay.visible) {
          context.loaderOverlay.hide();
        }
      }
      return GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Scaffold(
          appBar: AppBar(
            title: const Text("MIS Report"),
            elevation: 0.5,
            backgroundColor: colorsConstants.whiteColor,
            shadowColor: colorsConstants.whiteColor,
            surfaceTintColor: colorsConstants.whiteColor,
            actions: [
              IconButton(
                onPressed: () {
                  misReportController.downlaodCSVFile();
                },
                icon: const Icon(Icons.download),
              )
            ],
          ),
          body: Column(
            children: [
              HeightBox(20.h),
              if (misReportController.rows.isNotEmpty) ...[
                // Padding(
                //   padding: EdgeInsets.symmetric(horizontal: 20.w),
                //   child: Row(
                //     children: [
                //       Expanded(
                //         child: CustomTextField(
                //           hintText: "Search Here...",
                //           onChange: (value) {
                //             misReportController.searchCustomerByName(
                //                 name: value);
                //           },
                //         ),
                //       ),
                //     ],
                //   ),
                // ),
                // HeightBox(10.h),
                Expanded(
                  child: SingleChildScrollView(
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: GetBuilder<MISReportController>(
                        id: "misReportsTable",
                        builder: (_) {
                          return DataTable(
                            headingRowColor: WidgetStatePropertyAll<Color>(
                              colorsConstants.primaryRed,
                            ),
                            columns: misReportController.headers.map((header) {
                              return DataColumn(
                                label: Text(
                                  header.toString(),
                                  style: TextStyle(
                                      color: colorsConstants.whiteColor),
                                ),
                              );
                            }).toList(),
                            rows: misReportController.rows.mapIndexed(
                              (index, element) {
                                return DataRow(
                                  color: index % 2 == 0
                                      ? const WidgetStatePropertyAll<Color>(
                                          Colors.white)
                                      : WidgetStatePropertyAll<Color>(
                                          Colors.grey[50] ?? Colors.grey),
                                  cells: element.map((data) {
                                    return DataCell(
                                      Text(
                                        data.toString(),
                                      ),
                                    );
                                  }).toList(),
                                );
                              },
                            ).toList(),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ]
            ],
          ),
        ),
      );
    });
  }
}
