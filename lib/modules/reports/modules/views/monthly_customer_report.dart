import 'package:flutter/material.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:speed_force_franchise/modules/reports/modules/controllers/monthly_customers_report_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class MonthlyCustomersReport extends StatefulWidget {
  const MonthlyCustomersReport({super.key});

  @override
  State<MonthlyCustomersReport> createState() => _MonthlyCustomersReportState();
}

class _MonthlyCustomersReportState extends State<MonthlyCustomersReport> {
  @override
  void initState() {
    super.initState();

    Get.put(MonthlyCustomersReportController()..fetchMonthlyCustomersReport());
  }

  @override
  void dispose() {
    Get.delete<MonthlyCustomersReportController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<MonthlyCustomersReportController>(
        builder: (monthlyCustomersReportController) {
      if (monthlyCustomersReportController.isLoading) {
        if (!context.loaderOverlay.visible) {
          context.loaderOverlay.show();
        }
      } else {
        if (context.loaderOverlay.visible) {
          context.loaderOverlay.hide();
        }
      }
      return GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Scaffold(
          appBar: AppBar(
            title: const Text("Monthly Customers Report"),
            elevation: 0.5,
            backgroundColor: colorsConstants.whiteColor,
            shadowColor: colorsConstants.whiteColor,
            surfaceTintColor: colorsConstants.whiteColor,
            actions: [
              IconButton(
                onPressed: () {
                  monthlyCustomersReportController.downlaodCSVFile();
                },
                icon: const Icon(Icons.download),
              )
            ],
          ),
          body: Column(
            children: [
              HeightBox(20.h),
              if (monthlyCustomersReportController.rows.isNotEmpty) ...[
                // Padding(
                //   padding: EdgeInsets.symmetric(horizontal: 20.w),
                //   child: Row(
                //     children: [
                //       Expanded(
                //         child: CustomTextField(
                //           hintText: "Search Here...",
                //           onChange: (value) {
                //             monthlyCustomersReportController
                //                 .searchCustomerByName(name: value);
                //           },
                //         ),
                //       ),
                //     ],
                //   ),
                // ),
                // HeightBox(10.h),
                Expanded(
                  child: SingleChildScrollView(
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: GetBuilder<MonthlyCustomersReportController>(
                        id: "monthlyCustomersTable",
                        builder: (_) {
                          return DataTable(
                            headingRowColor: WidgetStatePropertyAll<Color>(
                              colorsConstants.primaryRed,
                            ),
                            columns: monthlyCustomersReportController.headers
                                .map((header) {
                              return DataColumn(
                                label: Text(
                                  header.toString(),
                                  style: TextStyle(
                                      color: colorsConstants.whiteColor),
                                ),
                              );
                            }).toList(),
                            rows: monthlyCustomersReportController.rows
                                .mapIndexed(
                              (index, element) {
                                return DataRow(
                                  color: index % 2 == 0
                                      ? const WidgetStatePropertyAll<Color>(
                                          Colors.white)
                                      : WidgetStatePropertyAll<Color>(
                                          Colors.grey[50] ?? Colors.grey),
                                  cells: element.map((data) {
                                    return DataCell(
                                      Text(
                                        data.toString(),
                                      ),
                                    );
                                  }).toList(),
                                );
                              },
                            ).toList(),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ]
            ],
          ),
        ),
      );
    });
  }
}
