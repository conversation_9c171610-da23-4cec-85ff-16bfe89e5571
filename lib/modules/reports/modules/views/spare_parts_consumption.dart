import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:speed_force_franchise/modules/reports/modules/controllers/spare_parts_consumption_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class SparePartsConsumption extends StatefulWidget {
  const SparePartsConsumption({super.key});

  @override
  State<SparePartsConsumption> createState() => _SparePartsConsumptionState();
}

class _SparePartsConsumptionState extends State<SparePartsConsumption> {
  @override
  void initState() {
    super.initState();

    Get.put(SparePartsConsumptionController()
      ..fetchMonthlySparePartsConsumptionReport());
  }

  @override
  void dispose() {
    Get.delete<SparePartsConsumptionController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SparePartsConsumptionController>(
        builder: (sparePartsConsumptionController) {
      if (sparePartsConsumptionController.isLoading) {
        if (!context.loaderOverlay.visible) {
          context.loaderOverlay.show();
        }
      } else {
        if (context.loaderOverlay.visible) {
          context.loaderOverlay.hide();
        }
      }
      return GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Scaffold(
          appBar: AppBar(
            title: const Text("Sales Report"),
            elevation: 0.5,
            backgroundColor: colorsConstants.whiteColor,
            shadowColor: colorsConstants.whiteColor,
            surfaceTintColor: colorsConstants.whiteColor,
            actions: [
              IconButton(
                onPressed: () {
                  sparePartsConsumptionController.downlaodCSVFile();
                },
                icon: const Icon(Icons.download),
              )
            ],
          ),
          body: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 15.0, horizontal: 5),
                    child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(5))),
                        onPressed: () async {
                          DateTime minDateTime = DateTime.utc(1985, 04, 20);

                          sparePartsConsumptionController.selectedDateRange =
                              await showDateRangePicker(
                                    context: context,
                                    firstDate: minDateTime,
                                    lastDate: DateTime.now(),
                                  ) ??
                                  DateTimeRange(
                                      start: DateTime.utc(
                                          DateTime.now().year,
                                          DateTime.now().month,
                                          DateTime.now().day),
                                      end: DateTime.utc(
                                          DateTime.now().year,
                                          DateTime.now().month,
                                          DateTime.now().day));

                          await sparePartsConsumptionController
                              .fetchMonthlySparePartsConsumptionReport();

                          sparePartsConsumptionController.update();
                          setState(() {});
                        },
                        child: Text(
                          "Select Date",
                          style: GoogleFonts.poppins(
                              color: Colors.white, fontWeight: FontWeight.w500),
                        )),
                  ),
                ],
              ),
              // HeightBox(20.h),
              if (sparePartsConsumptionController.rows.isNotEmpty) ...[
                // Padding(
                //   padding: EdgeInsets.symmetric(horizontal: 20.w),
                //   child: Row(
                //     children: [
                //       Expanded(
                //         child: CustomTextField(
                //           hintText: "Search Here...",
                //           onChange: (value) {
                //             sparePartsConsumptionController.searchFromTable(
                //                 searchTerm: value);
                //           },
                //         ),
                //       ),
                //     ],
                //   ),
                // ),
                // HeightBox(10.h),
                Expanded(
                  child: SingleChildScrollView(
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: GetBuilder<SparePartsConsumptionController>(
                        id: "sparePartsConsumptionTable",
                        builder: (_) {
                          return DataTable(
                            headingRowColor: WidgetStatePropertyAll<Color>(
                              colorsConstants.primaryRed,
                            ),
                            columns: sparePartsConsumptionController.headers
                                .map((header) {
                              return DataColumn(
                                label: Text(
                                  header.toString(),
                                  style: TextStyle(
                                      color: colorsConstants.whiteColor),
                                ),
                              );
                            }).toList(),
                            rows:
                                sparePartsConsumptionController.rows.mapIndexed(
                              (index, element) {
                                return DataRow(
                                  color: index % 2 == 0
                                      ? const WidgetStatePropertyAll<Color>(
                                          Colors.white)
                                      : WidgetStatePropertyAll<Color>(
                                          Colors.grey[50] ?? Colors.grey),
                                  cells: element.map((data) {
                                    return DataCell(
                                      Text(
                                        data.toString(),
                                      ),
                                    );
                                  }).toList(),
                                );
                              },
                            ).toList(),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ]
            ],
          ),
        ),
      );
    });
  }
}
