import 'package:flutter/material.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:speed_force_franchise/modules/reports/modules/controllers/total_customers_report_controller.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class TotalCustomersReport extends StatefulWidget {
  const TotalCustomersReport({super.key});

  @override
  State<TotalCustomersReport> createState() => _TotalCustomersReportState();
}

class _TotalCustomersReportState extends State<TotalCustomersReport> {
  @override
  void initState() {
    super.initState();

    Get.put(TotalCustomersReportController()..fetchTotalCustomersReport());
  }

  @override
  void dispose() {
    Get.delete<TotalCustomersReportController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<TotalCustomersReportController>(
        builder: (totalCustomersReportsController) {
      if (totalCustomersReportsController.isLoading) {
        if (!context.loaderOverlay.visible) {
          context.loaderOverlay.show();
        }
      } else {
        if (context.loaderOverlay.visible) {
          context.loaderOverlay.hide();
        }
      }
      return GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Scaffold(
          appBar: AppBar(
            title: const Text("Total Customers Report"),
            elevation: 0.5,
            backgroundColor: colorsConstants.whiteColor,
            shadowColor: colorsConstants.whiteColor,
            surfaceTintColor: colorsConstants.whiteColor,
            actions: [
              IconButton(
                onPressed: () {
                  totalCustomersReportsController.downlaodCSVFile();
                },
                icon: const Icon(Icons.download),
              )
            ],
          ),
          body: Column(
            children: [
              HeightBox(20.h),
              if (totalCustomersReportsController.rows.isNotEmpty) ...[
                // Padding(
                //   padding: EdgeInsets.symmetric(horizontal: 20.w),
                //   child: Row(
                //     children: [
                //       Expanded(
                //         child: CustomTextField(
                //           hintText: "Search Here...",
                //           onChange: (value) {
                //             totalCustomersReportsController
                //                 .searchCustomerByName(name: value);
                //           },
                //         ),
                //       ),
                //     ],
                //   ),
                // ),
                // HeightBox(10.h),
                Expanded(
                  child: SingleChildScrollView(
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: GetBuilder<TotalCustomersReportController>(
                        id: "totalCustomersTable",
                        builder: (_) {
                          return DataTable(
                            headingRowColor: WidgetStatePropertyAll<Color>(
                              colorsConstants.primaryRed,
                            ),
                            columns: totalCustomersReportsController.headers
                                .map((header) {
                              return DataColumn(
                                label: Text(
                                  header.toString(),
                                  style: TextStyle(
                                      color: colorsConstants.whiteColor),
                                ),
                              );
                            }).toList(),
                            rows:
                                totalCustomersReportsController.rows.mapIndexed(
                              (index, element) {
                                return DataRow(
                                  color: index % 2 == 0
                                      ? const WidgetStatePropertyAll<Color>(
                                          Colors.white)
                                      : WidgetStatePropertyAll<Color>(
                                          Colors.grey[50] ?? Colors.grey),
                                  cells: element.map((data) {
                                    return DataCell(
                                      Text(
                                        data.toString(),
                                      ),
                                    );
                                  }).toList(),
                                );
                              },
                            ).toList(),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ]
            ],
          ),
        ),
      );
    });
  }
}
