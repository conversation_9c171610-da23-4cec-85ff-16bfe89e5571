import 'package:flutter/material.dart';
import 'package:speed_force_franchise/modules/services/widgets/half_width_card.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

import '../../../utils/utils.dart';

class Reports extends StatefulWidget {
  const Reports({super.key});

  @override
  State<Reports> createState() => _ReportsState();
}

class _ReportsState extends State<Reports> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Reports"),
        elevation: 0.5,
        backgroundColor: colorsConstants.whiteColor,
        shadowColor: colorsConstants.whiteColor,
        surfaceTintColor: colorsConstants.whiteColor,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            HeightBox(16.h),
            Row(
              children: [
                WidthBox(10.w),
                Expanded(
                  child: HalfWidthCard(
                    onPress: () {
                      moveToTotalCustomersReportsScreen(context: context);
                    },
                    title: "Total Customer Report",
                    preFixIcon: assetsConstants.totalCustomerReport,
                    constentPadding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                  ),
                ),
                WidthBox(10.w),
                // Expanded(
                //   child: HalfWidthCard(
                //     onPress: () {
                //       moveToMonthlyCustomersReportsScreen(context: context);
                //     },
                //     title: "Monthly Customer Report",
                //     preFixIcon: assetsConstants.monthlyCustomerReport,
                //     constentPadding:
                //         EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                //   ),
                // ),
                Expanded(
                  child: HalfWidthCard(
                    onPress: () {
                      moveToSparePartsConsumptionReportsScreen(
                          context: context);
                      // Utils.showSnackBar(title: "Coming Soon...");
                    },
                    title: "Sales Report",
                    preFixIcon: assetsConstants.monthlyDetailedMISReport,
                    constentPadding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 20.h),
                  ),
                ),
                WidthBox(10.w),
              ],
            ),
            HeightBox(16.h),
            // Row(
            //   children: [
            //     WidthBox(10.w),
            //     Expanded(
            //       child: HalfWidthCard(
            //         onPress: () {
            //           moveToMISReportsScreen(context: context);
            //         },
            //         title: "Monthly Detailed MIS Report",
            //         preFixIcon: assetsConstants.monthlyDetailedMISReport,
            //         constentPadding:
            //             EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
            //       ),
            //     ),
            //     // Expanded(
            //     //   child: HalfWidthCard(
            //     //     onPress: () {
            //     //       Utils.showSnackBar(title: "Coming Soon...");
            //     //     },
            //     //     title: "Pie Chart",
            //     //     preFixIcon: assetsConstants.pieChart,
            //     //     constentPadding:
            //     //         EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
            //     //   ),
            //     // ),
            //     WidthBox(10.w),
            //     Expanded(
            //       child: HalfWidthCard(
            //         onPress: () {
            //           moveToSparePartsConsumptionReportsScreen(
            //               context: context);
            //         },
            //         title: "Spare Parts Consumption",
            //         preFixIcon: assetsConstants.sparePartsConsumption,
            //         constentPadding:
            //             EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
            //       ),
            //     ),
            //     WidthBox(10.w),
            //   ],
            // ),
            // HeightBox(16.h),
            // Row(
            //   children: [
            //     WidthBox(10.w),
            //     Expanded(
            //       child: HalfWidthCard(
            //         onPress: () {
            //           moveToSparePartsConsumptionReportsScreen(
            //               context: context);
            //           // Utils.showSnackBar(title: "Coming Soon...");
            //         },
            //         title: "Sales Report",
            //         preFixIcon: assetsConstants.monthlyDetailedMISReport,
            //         constentPadding:
            //             EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
            //       ),
            //     ),
            //     WidthBox(10.w),
            //     Expanded(
            //       child: HalfWidthCard(
            //         onPress: () {
            //           // Utils.showSnackBar(title: "Coming Soon...");
            //         },
            //         title: "Inventory Report",
            //         preFixIcon: assetsConstants.inventoryReport,
            //         constentPadding:
            //             EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
            //       ),
            //     ),
            //     WidthBox(10.w),
            //   ],
            // ),
            /*  
            HeightBox(16.h),
            Row(
              children: [
                WidthBox(10.w),
                Expanded(
                  child: HalfWidthCard(
                    onPress: () {
                      Utils.showSnackBar(title: "Coming Soon...");
                    },
                    title: "Business Comparison",
                    preFixIcon: assetsConstants.businessComparison,
                    constentPadding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                  ),
                ),
                WidthBox(10.w),
                Expanded(
                  child: HalfWidthCard(
                    onPress: () {
                      moveToMISReportsScreen(context: context);
                    },
                    title: "Monthly Detailed MIS Report",
                    preFixIcon: assetsConstants.monthlyDetailedMISReport,
                    constentPadding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                  ),
                ),
                WidthBox(10.w),
              ],
            ),
            HeightBox(16.h),
            Row(
              children: [
                Expanded(
                  child: HalfWidthCard(
                    onPress: () {
                      Utils.showSnackBar(title: "Coming Soon...");
                    },
                    title: "Lost Customer Data",
                    preFixIcon: assetsConstants.lostCustomerData,
                    constentPadding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                  ),
                ),
                Expanded(
                  child: HalfWidthCard(
                    onPress: () {
                      Utils.showSnackBar(title: "Coming Soon...");
                    },
                    title: "Inventory Report",
                    preFixIcon: assetsConstants.inventoryReport,
                    constentPadding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                  ),
                ),
              ],
            ),
            HeightBox(16.h),
            Row(
              children: [
                Expanded(
                  child: HalfWidthCard(
                    onPress: () {
                      Utils.showSnackBar(title: "Coming Soon...");
                    },
                    title: "Counter Sale Report",
                    preFixIcon: assetsConstants.customerSalesReport,
                    constentPadding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                  ),
                ),
                const Spacer(),
              ],
            ),
             */
            HeightBox(40.h),
          ],
        ),
      ),
    );
  }
}
