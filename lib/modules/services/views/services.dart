import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home_screen_controller.dart';
import 'package:speed_force_franchise/modules/login/views/update_alert.dart';
import 'package:speed_force_franchise/modules/services/widgets/full_width_card.dart';
import 'package:speed_force_franchise/modules/services/widgets/half_width_card.dart';
import 'package:speed_force_franchise/routing_manager/navigation_helper.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';
import 'package:speed_force_franchise/utils/utils.dart';
import 'package:speed_force_franchise/widgets/advertise_banner.dart';

class Services extends StatefulWidget {
  const Services({super.key});

  @override
  State<Services> createState() => _ServicesState();
}

class _ServicesState extends State<Services> {
  @override
  void didChangeDependencies() async {
    super.didChangeDependencies();
    if (!await Utils.checkVersionSupported()) {
      Navigator.pushReplacement(
          context, MaterialPageRoute(builder: (context) => UpdateAlert()));
      // showDialog(
      //   context: context,
      //   barrierDismissible: false,
      //   builder: (context) {
      //     return const AlertDialog(
      //       content: Text("Please update the app to continue"),
      //     );
      //   },
      // );
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: GetBuilder<HomeScreenController>(builder: (homescreenController) {
        return SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.zero,
            // padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              children: [
                if (homescreenController.advertises.isNotEmpty) ...[
                  HeightBox(16.h),
                  SizedBox(
                    width: double.maxFinite,
                    height: 100,
                    child: CarouselSlider(
                      options: CarouselOptions(
                        viewportFraction: 1,
                        enlargeCenterPage: true,
                        autoPlay: false,
                        enableInfiniteScroll: true,
                        autoPlayInterval: const Duration(seconds: 5),
                      ),
                      items: homescreenController.advertises.map((ad) {
                        print(ad.imageUrl);
                        return Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: ClipRRect(
                              borderRadius: BorderRadius.circular(10.r),
                              child: AdvertiseBanner(adBanner: ad)),
                        );
                      }).toList(),
                    ),
                  ),
                  HeightBox(16.h),
                ],
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Column(
                    children: [
                      // Container(
                      //   decoration:
                      //       BoxDecoration(boxShadow: Constants.boxShadow),
                      //   child: CustomTextField(
                      //     prefix: Icon(
                      //       Icons.search_rounded,
                      //       size: 22.sp,
                      //     ),
                      //     // hintText: "Name/jobcard/phone/email",
                      //     hintText: "Search here...",
                      //     contentPadding: EdgeInsets.zero,
                      //     filled: true,
                      //     fillColor: colorsConstants.whiteColor,

                      //     border: const OutlineInputBorder(
                      //       borderRadius: BorderRadius.all(
                      //         Radius.circular(5),
                      //       ),
                      //       borderSide: BorderSide.none,
                      //     ),
                      //     enabledBorder: const OutlineInputBorder(
                      //       borderRadius: BorderRadius.all(
                      //         Radius.circular(5),
                      //       ),
                      //       borderSide: BorderSide.none,
                      //     ),
                      //     focusedBorder: const OutlineInputBorder(
                      //       borderRadius: BorderRadius.all(
                      //         Radius.circular(5),
                      //       ),
                      //       borderSide: BorderSide.none,
                      //     ),
                      //   ),
                      // ),
                      // HeightBox(16.h),
                      FullWidthCard(
                        onPress: () async {
                          FocusManager.instance.primaryFocus?.unfocus();

                          moveToAddCreateRepairOrderAddCustomer(
                              context: context);
                        },
                        title: "Create Repair Order",
                        subTitle: "Click to open a new job card",
                        preFixIcon: assetsConstants.createRepairOrder,
                      ),
                      HeightBox(10.h),
                      FullWidthCard(
                        onPress: () => moveToCreateInvoiceScreen(
                          context: context,
                          isCounterSale: true,
                        ),
                        title: "Counter Sale",
                        subTitle: "Create direct parts/service invoice",
                        preFixIcon: assetsConstants.createInvoice,
                      ),
                      HeightBox(10.h),
                      Row(
                        children: [
                          Expanded(
                            child: HalfWidthCard(
                              onPress: () {
                                moveToViewOrdersScreen(
                                  context: context,
                                  orderStatus: OrderStatus.created,
                                );
                              },
                              title: "Open Orders",
                              subTitle: "Repair Order created",
                              preFixIcon: assetsConstants.openOrders,
                            ),
                          ),
                          WidthBox(10.w),
                          Expanded(
                            child: HalfWidthCard(
                              onPress: () {
                                moveToViewOrdersScreen(
                                  context: context,
                                  orderStatus: OrderStatus.workInProgress,
                                );
                              },
                              title: "WIP Orders",
                              subTitle: "Work in progress",
                              preFixIcon: assetsConstants.wipOrder,
                            ),
                          ),
                        ],
                      ),
                      HeightBox(10.h),
                      Row(
                        children: [
                          Expanded(
                            child: HalfWidthCard(
                              onPress: () {
                                moveToViewOrdersScreen(
                                  context: context,
                                  orderStatus: OrderStatus.ready,
                                );
                              },
                              title: "Ready Orders",
                              subTitle: "Vehicle is ready",
                              preFixIcon: assetsConstants.readyOrder,
                            ),
                          ),
                          WidthBox(10.w),
                          Expanded(
                            child: HalfWidthCard(
                              onPress: () {
                                moveToViewOrdersScreen(
                                  context: context,
                                  orderStatus: OrderStatus.paymentDue,
                                );
                              },
                              title: "Payment Due",
                              subTitle: "Invoice prepared",
                              preFixIcon: assetsConstants.paymentDue,
                            ),
                          ),
                        ],
                      ),
                      HeightBox(10.h),
                      FullWidthCard(
                        onPress: () {
                          moveToViewOrdersScreen(
                            context: context,
                            orderStatus: OrderStatus.completed,
                          );
                        },
                        title: "Completed Orders",
                        subTitle: "Click to view order history",
                        preFixIcon: assetsConstants.completedOrder,
                      ),
                      HeightBox(16.h),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }
}
