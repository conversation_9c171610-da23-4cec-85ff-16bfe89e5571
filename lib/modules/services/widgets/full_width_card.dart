import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class FullWidthCard extends StatefulWidget {
  const FullWidthCard({
    super.key,
    required this.title,
    required this.subTitle,
    required this.preFixIcon,
    this.onPress,
  });

  final String title;
  final String subTitle;
  final String preFixIcon;
  final VoidCallback? onPress;

  @override
  State<FullWidthCard> createState() => _FullWidthCardState();
}

class _FullWidthCardState extends State<FullWidthCard> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onPress,
      child: Container(
        height: 50.h,
        width: double.maxFinite,
        decoration: BoxDecoration(
            color: colorsConstants.whiteColor,
            boxShadow: Constants.boxShadow,
            borderRadius: BorderRadius.circular(5.r)),
        child: Row(
          children: [
            WidthBox(14.w),
            SvgPicture.asset(
              widget.preFixIcon,
              height: 40.sp,
            ),
            WidthBox(14.w),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                HeightBox(5.h),
                Text(
                  widget.title,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14.sp,
                  ),
                ),
                HeightBox(2.h),
                Text(
                  widget.subTitle,
                  style: TextStyle(
                    color: colorsConstants.hintGrey,
                    fontSize: 11.sp,
                  ),
                ),
              ],
            ),
            const Spacer(),
            Icon(
              Icons.arrow_forward_ios_rounded,
              size: 12.sp,
            ),
            WidthBox(14.w),
          ],
        ),
      ),
    );
  }
}
