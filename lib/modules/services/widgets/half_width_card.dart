import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class HalfWidthCard extends StatefulWidget {
  const HalfWidthCard({
    super.key,
    required this.title,
    this.subTitle,
    required this.preFixIcon,
    this.onPress,
    this.constentPadding,
  });

  final String title;
  final String? subTitle;
  final String preFixIcon;
  final VoidCallback? onPress;
  final EdgeInsets? constentPadding;

  @override
  State<HalfWidthCard> createState() => _HalfWidthCardState();
}

class _HalfWidthCardState extends State<HalfWidthCard> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onPress,
      child: Container(
        width: double.maxFinite,
        padding: widget.constentPadding ??
            EdgeInsets.symmetric(
              vertical: 10.h,
            ),
        decoration: BoxDecoration(
            color: colorsConstants.whiteColor,
            boxShadow: Constants.boxShadow,
            borderRadius: BorderRadius.circular(5.r)),
        child: Column(
          children: [
            HeightBox(14.h),
            SvgPicture.asset(
              widget.preFixIcon,
              height: 40.sp,
            ),
            HeightBox(14.h),
            Text(
              widget.title,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14.sp,
              ),
            ),
            if (widget.subTitle != null && widget.subTitle!.isNotEmpty) ...[
              HeightBox(2.h),
              Text(
                widget.subTitle!,
                style: TextStyle(
                  color: colorsConstants.hintGrey,
                  fontSize: 11.sp,
                ),
              ),
            ]
          ],
        ),
      ),
    );
  }
}
