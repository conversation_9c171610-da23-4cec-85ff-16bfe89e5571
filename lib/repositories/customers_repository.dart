import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/personal_details_model.dart';

class CustomersRepository {
  static Future<List<CustomerDetailsModel>> fetchAllCustomers() async {
    List<CustomerDetailsModel> allCustomers = [];

    QuerySnapshot<Map<String, dynamic>> allCustomersQuerySnapshot =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.customers.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .get();

    for (QueryDocumentSnapshot<
            Map<String, dynamic>> customerQueryDocumentSnapshot
        in allCustomersQuerySnapshot.docs) {
      allCustomers.add(
          CustomerDetailsModel.fromMap(customerQueryDocumentSnapshot.data()));
    }

    return allCustomers;
  }

  static Future<List<CustomerDetailsModel>> fetchLimitedCustomers(
      {required int limit, QueryDocumentSnapshot<Object?>? lastVisible}) async {
    List<CustomerDetailsModel> fetchedCustomers = [];

    QuerySnapshot<Map<String, dynamic>> allCustomersQuerySnapshot =
        lastVisible == null
            ? await FirebaseFirestore.instance
                .collection(FirebaseCollections.customers.name)
                .where("franchiseId",
                    isEqualTo: FirebaseAuth.instance.currentUser?.uid)
                .limit(limit)
                .get()
            : await FirebaseFirestore.instance
                .collection(FirebaseCollections.customers.name)
                .where("franchiseId",
                    isEqualTo: FirebaseAuth.instance.currentUser?.uid)
                .startAfter([lastVisible])
                .limit(limit)
                .get();

    for (QueryDocumentSnapshot<
            Map<String, dynamic>> customerQueryDocumentSnapshot
        in allCustomersQuerySnapshot.docs) {
      fetchedCustomers.add(
          CustomerDetailsModel.fromMap(customerQueryDocumentSnapshot.data()));
    }

    return fetchedCustomers;
  }

  static Future<CustomerDetailsModel?> fetchCustomer(
      {required String customerId}) async {
    CustomerDetailsModel? customerDetailsModel;

    DocumentSnapshot<Map<String, dynamic>> customerDocument =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.customers.name)
            .doc(customerId)
            .get();

    Map<String, dynamic>? customerDataMap = customerDocument.data();

    if (customerDataMap != null) {
      customerDetailsModel = CustomerDetailsModel.fromMap(customerDataMap);
    }
    return customerDetailsModel;
  }
}
