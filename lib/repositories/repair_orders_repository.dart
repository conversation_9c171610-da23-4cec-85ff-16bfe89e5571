import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/repositories/customers_repository.dart';
import 'package:speed_force_franchise/repositories/vehicles_repository.dart';

class RepairOrdersRepository {
  static Future<List<RepairOrderModel>> fetchAllRepairOrders() async {
    List<RepairOrderModel> allRepairOrders = [];

    QuerySnapshot<Map<String, dynamic>> allRepairOrdersSnapshot =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.orders.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .get();

    for (QueryDocumentSnapshot<Map<String, dynamic>> repairOrderQueryDoc
        in allRepairOrdersSnapshot.docs) {
      RepairOrderModel repairOrderModel = RepairOrderModel.fromMap(
          repairOrderQueryDoc.id, repairOrderQueryDoc.data());

      if (repairOrderModel.customerDetailsModel == null &&
          repairOrderModel.customerId != null) {
        repairOrderModel.customerDetailsModel =
            await CustomersRepository.fetchCustomer(
                customerId: repairOrderModel.customerId ?? "");
      }
      if (repairOrderModel.vehicleDetailsModel == null &&
          repairOrderModel.vehicleId != null) {
        repairOrderModel.vehicleDetailsModel =
            await VehiclesRepository.fetchVehicle(
                vehicleId: repairOrderModel.vehicleId ?? "");
      }

      allRepairOrders.add(repairOrderModel);
    }

    return allRepairOrders;
  }

  static Future<RepairOrderModel?> fetchRepairOrderDetails(
      {required String orderId}) async {
    RepairOrderModel? repairOrderModel;

    DocumentSnapshot<Map<String, dynamic>> orderDocument =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.orders.name)
            .doc(orderId)
            .get();

    Map<String, dynamic>? orderMap = orderDocument.data();
    if (orderMap != null) {
      repairOrderModel = RepairOrderModel.fromMap(orderDocument.id, orderMap);

      if (repairOrderModel.customerDetailsModel == null &&
          repairOrderModel.customerId != null) {
        repairOrderModel.customerDetailsModel =
            await CustomersRepository.fetchCustomer(
                customerId: repairOrderModel.customerId ?? "");
      }
      if (repairOrderModel.vehicleDetailsModel == null &&
          repairOrderModel.vehicleId != null) {
        repairOrderModel.vehicleDetailsModel =
            await VehiclesRepository.fetchVehicle(
                vehicleId: repairOrderModel.vehicleId ?? "");
      }
    }
    return repairOrderModel;
  }
}
