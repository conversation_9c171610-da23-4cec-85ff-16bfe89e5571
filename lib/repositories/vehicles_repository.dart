import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/modules/orders/models/create_repair_order_sub_models/vehicle_details_model.dart';

class VehiclesRepository {
  static Future<List<VehicleDetailsModel>> fetchAllCustomersVehicles() async {
    List<VehicleDetailsModel> allVehicles = [];

    QuerySnapshot<Map<String, dynamic>> allCustomersVehiclesQuerySnapshot =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.customerVehicles.name)
            .where("franchiseId",
                isEqualTo: FirebaseAuth.instance.currentUser?.uid)
            .get();

    for (QueryDocumentSnapshot<
            Map<String, dynamic>> customerVehicleQueryDocumentSnapshot
        in allCustomersVehiclesQuerySnapshot.docs) {
      allVehicles.add(VehicleDetailsModel.fromMap(
          customerVehicleQueryDocumentSnapshot.data()));
    }

    return allVehicles;
  }

  static Future<VehicleDetailsModel?> fetchVehicle(
      {required String vehicleId}) async {
    VehicleDetailsModel? vehicleDetailsModel;

    DocumentSnapshot<Map<String, dynamic>> vehicleDocument =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.customerVehicles.name)
            .doc(vehicleId)
            .get();

    Map<String, dynamic>? vehicleDataMap = vehicleDocument.data();

    if (vehicleDataMap != null) {
      vehicleDetailsModel = VehicleDetailsModel.fromMap(vehicleDataMap);
    }
    return vehicleDetailsModel;
  }
}
