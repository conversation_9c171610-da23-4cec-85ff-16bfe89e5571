import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/purchase_order_model.dart';
import 'package:speed_force_franchise/models/stock_model.dart';
import 'package:speed_force_franchise/modules/add_make_models/models/make_models_model.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/routing_manager/extensions.dart';

moveToLoginScreen({required BuildContext context}) {
  context.operation(
    operation: NavigationOperations.pushReplacementNamed,
    routeScreen: RouteScreens.login,
  );
}

moveToHomeScreen(
    {required BuildContext context,
    NavigationOperations? navigationOperation}) {
  context.operation(
    operation: navigationOperation ?? NavigationOperations.pushReplacementNamed,
    routeScreen: RouteScreens.homeScreen,
  );
}

moveToAddCreateRepairOrderAddCustomer({
  required BuildContext context,
  bool? addCustomerOnly = false,
  bool? vehicleOnly = false,
  String? customerId,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
    operation: NavigationOperations.pushNamed,
    routeScreen: RouteScreens.createRepairOrder,
    settings: SettingArguments(argumentsData: {
      "addCustomerOnly": addCustomerOnly,
      "vehicleOnly": vehicleOnly,
      "customerId": customerId,
    }),
    revertCallback: (data) {
      // handle setup if required

      if (revertCallback != null) {
        revertCallback.call(data);
      } else {
        debugPrint("null");
      }
    },
  );
}

moveToRepairDetails({
  required BuildContext context,
  required RepairOrderModel createRepairOrder,
  bool isInvoice = false,
}) {
  context.operation(
    operation: NavigationOperations.pushNamed,
    routeScreen: RouteScreens.repairDetails,
    settings: SettingArguments(argumentsData: {
      "createRepairOrder": createRepairOrder,
      "isInvoice": isInvoice,
    }),
  );
}

moveToConfirmOrder({
  required BuildContext context,
  required RepairOrderModel createRepairOrder,
  void Function()? onCreate,
  bool isInvoice = false,
}) {
  context.operation(
    operation: NavigationOperations.pushNamed,
    routeScreen: RouteScreens.confirmOrder,
    settings: SettingArguments(argumentsData: {
      "createRepairOrderModel": createRepairOrder,
      "isInvoice": isInvoice,
      "onCreate": onCreate
    }),
  );
}

moveToAddGarageScreen({required BuildContext context}) {
  context.operation(
    operation: NavigationOperations.pushNamed,
    routeScreen: RouteScreens.addGarage,
  );
}

moveToViewOrdersScreen({
  required BuildContext context,
  OrderStatus? orderStatus,
  String? vehicleId,
}) {
  context.operation(
    operation: NavigationOperations.pushNamed,
    routeScreen: RouteScreens.openOrders,
    settings: SettingArguments(argumentsData: {
      "vehicleId": vehicleId,
      "orderStatus": orderStatus,
    }),
  );
}

moveToCreateInvoiceScreen({
  required BuildContext context,
  bool? isCounterSale,
  RepairOrderModel? invoiceModel,
  void Function()? onInvoiceCreate,
}) {
  context.operation(
    operation: NavigationOperations.pushNamed,
    routeScreen: RouteScreens.createInvoice,
    settings: SettingArguments(
      argumentsData: {
        "isCounterSale": isCounterSale,
        "invoiceModel": invoiceModel,
        "onInvoiceCreate": onInvoiceCreate
      },
    ),
  );
}

moveToAddStockScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
    operation: NavigationOperations.pushNamed,
    routeScreen: RouteScreens.addStock,
    revertCallback: (data) {
      // handle setup if required

      if (revertCallback != null) {
        revertCallback.call(data);
      } else {
        debugPrint("null");
      }
    },
  );
}

moveToReportsScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
    operation: NavigationOperations.pushNamed,
    routeScreen: RouteScreens.reports,
    revertCallback: (data) {
      // handle setup if required

      if (revertCallback != null) {
        revertCallback.call(data);
      } else {
        debugPrint("null");
      }
    },
  );
}

moveToTotalCustomersReportsScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
    operation: NavigationOperations.pushNamed,
    routeScreen: RouteScreens.totalCustomersReports,
    revertCallback: (data) {
      // handle setup if required

      if (revertCallback != null) {
        revertCallback.call(data);
      } else {
        debugPrint("null");
      }
    },
  );
}

moveToMonthlyCustomersReportsScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
    operation: NavigationOperations.pushNamed,
    routeScreen: RouteScreens.monthlyCustomersReports,
    revertCallback: (data) {
      // handle setup if required

      if (revertCallback != null) {
        revertCallback.call(data);
      } else {
        debugPrint("null");
      }
    },
  );
}

moveToSparePartsConsumptionReportsScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
    operation: NavigationOperations.pushNamed,
    routeScreen: RouteScreens.sparePartsConsumption,
    revertCallback: (data) {
      // handle setup if required

      if (revertCallback != null) {
        revertCallback.call(data);
      } else {
        debugPrint("null");
      }
    },
  );
}

moveToInventoryReportsScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
    operation: NavigationOperations.pushNamed,
    routeScreen: RouteScreens.inventory,
    revertCallback: (data) {
      // handle setup if required

      if (revertCallback != null) {
        revertCallback.call(data);
      } else {
        debugPrint("null");
      }
    },
  );
}

moveToMISReportsScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
    operation: NavigationOperations.pushNamed,
    routeScreen: RouteScreens.misReports,
    revertCallback: (data) {
      // handle setup if required

      if (revertCallback != null) {
        revertCallback.call(data);
      } else {
        debugPrint("null");
      }
    },
  );
}

moveToAddVendorScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
    operation: NavigationOperations.pushNamed,
    routeScreen: RouteScreens.addVendor,
    revertCallback: (data) {
      // handle setup if required

      if (revertCallback != null) {
        revertCallback.call(data);
      } else {
        debugPrint("null");
      }
    },
  );
}

moveToAddServicePartScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
  required bool isAddingService,
}) {
  context.operation(
      operation: NavigationOperations.pushNamed,
      routeScreen: RouteScreens.addPart,
      settings: SettingArguments(argumentsData: isAddingService),
      revertCallback: (data) {
        // handle setup if required

        if (revertCallback != null) {
          revertCallback.call(data);
        } else {
          debugPrint("null");
        }
      });
}

moveToAddMakeModelsScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
  MakeModelsModel? makeModelsModel,
}) {
  context.operation(
      operation: NavigationOperations.pushNamed,
      routeScreen: RouteScreens.addMakeModels,
      settings: SettingArguments(argumentsData: makeModelsModel),
      revertCallback: (data) {
        // handle setup if required

        if (revertCallback != null) {
          revertCallback.call(data);
        } else {
          debugPrint("null");
        }
      });
}

moveToViewAlertScreen({
  required BuildContext context,
  required List<(PartModel, StockModel)> stockRecords,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
      operation: NavigationOperations.pushNamed,
      routeScreen: RouteScreens.viewAlert,
      settings: SettingArguments(argumentsData: stockRecords),
      revertCallback: (data) {
        // handle setup if required

        if (revertCallback != null) {
          revertCallback.call(data);
        } else {
          debugPrint("null");
        }
      });
}

moveToPurchaseOrderScreen({
  required BuildContext context,
  required List<(PartModel, StockModel)> stockRecords,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
      operation: NavigationOperations.pushNamed,
      routeScreen: RouteScreens.purchaseOrder,
      settings: SettingArguments(argumentsData: stockRecords),
      revertCallback: (data) {
        // handle setup if required

        if (revertCallback != null) {
          revertCallback.call(data);
        } else {
          debugPrint("null");
        }
      });
}

moveToWebViewScreen({
  required BuildContext context,
  required String url,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
      operation: NavigationOperations.pushNamed,
      routeScreen: RouteScreens.webview,
      settings: SettingArguments(argumentsData: url),
      revertCallback: (data) {
        // handle setup if required

        if (revertCallback != null) {
          revertCallback.call(data);
        } else {
          debugPrint("null");
        }
      });
}

moveToCsvUploadScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
      operation: NavigationOperations.pushNamed,
      routeScreen: RouteScreens.csvUpload,
      revertCallback: (data) {
        // handle setup if required

        if (revertCallback != null) {
          revertCallback.call(data);
        } else {
          debugPrint("null");
        }
      });
}

moveToEditPurchaseOrderScreen({
  required BuildContext context,
  required PurchaseOrderModel purchaseOrderModel,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
      operation: NavigationOperations.pushNamed,
      routeScreen: RouteScreens.editPurchaseOrder,
      settings: SettingArguments(argumentsData: purchaseOrderModel),
      revertCallback: (data) {
        // handle setup if required

        if (revertCallback != null) {
          revertCallback.call(data);
        } else {
          debugPrint("null");
        }
      });
}

moveToMyPurchaseOrdersScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
      operation: NavigationOperations.pushNamed,
      routeScreen: RouteScreens.myPurchaseOrders,
      revertCallback: (data) {
        // handle setup if required

        if (revertCallback != null) {
          revertCallback.call(data);
        } else {
          debugPrint("null");
        }
      });
}

moveToStockInScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
  required List<(PartModel, StockModel)> stockRecords,
}) {
  context.operation(
      operation: NavigationOperations.pushNamed,
      routeScreen: RouteScreens.stockIn,
      settings: SettingArguments(argumentsData: stockRecords),
      revertCallback: (data) {
        // handle setup if required

        if (revertCallback != null) {
          revertCallback.call(data);
        } else {
          debugPrint("null");
        }
      });
}

moveToMyCustomersScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
      operation: NavigationOperations.pushNamed,
      routeScreen: RouteScreens.myCustomers,
      revertCallback: (data) {
        // handle setup if required

        if (revertCallback != null) {
          revertCallback.call(data);
        } else {
          debugPrint("null");
        }
      });
}

moveToCustomerDetailsScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
  required String customerId,
}) {
  context.operation(
      operation: NavigationOperations.pushNamed,
      routeScreen: RouteScreens.customerDetails,
      settings: SettingArguments(argumentsData: customerId),
      revertCallback: (data) {
        // handle setup if required

        if (revertCallback != null) {
          revertCallback.call(data);
        } else {
          debugPrint("null");
        }
      });
}

moveToRepairOrderDetailsScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
  required String orderId,
}) {
  context.operation(
      operation: NavigationOperations.pushNamed,
      routeScreen: RouteScreens.repairOrderDetails,
      settings: SettingArguments(argumentsData: orderId),
      revertCallback: (data) {
        // handle setup if required

        if (revertCallback != null) {
          revertCallback.call(data);
        } else {
          debugPrint("null");
        }
      });
}

moveToMyVendorsScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
      operation: NavigationOperations.pushNamed,
      routeScreen: RouteScreens.myVendors,
      revertCallback: (data) {
        // handle setup if required

        if (revertCallback != null) {
          revertCallback.call(data);
        } else {
          debugPrint("null");
        }
      });
}

moveToOrderSearchScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
      operation: NavigationOperations.pushNamed,
      routeScreen: RouteScreens.orderSearch,
      revertCallback: (data) {
        // handle setup if required

        if (revertCallback != null) {
          revertCallback.call(data);
        } else {
          debugPrint("null");
        }
      });
}

moveToVehicleSearchScreen({
  required BuildContext context,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
      operation: NavigationOperations.pushNamed,
      routeScreen: RouteScreens.vehicleSearch,
      revertCallback: (data) {
        // handle setup if required

        if (revertCallback != null) {
          revertCallback.call(data);
        } else {
          debugPrint("null");
        }
      });
}

moveToAddExpenseScreen({
  required BuildContext context,
  required bool isPartPurchase,
  dynamic Function(dynamic)? revertCallback,
}) {
  context.operation(
      operation: NavigationOperations.pushNamed,
      routeScreen: RouteScreens.addExpense,
      settings: SettingArguments(argumentsData: isPartPurchase),
      revertCallback: (data) {
        // handle setup if required

        if (revertCallback != null) {
          revertCallback.call(data);
        } else {
          debugPrint("null");
        }
      });
}

moveToAddInsuranceProviderScreen({required BuildContext context}) {
  context.operation(
    operation: NavigationOperations.pushNamed,
    routeScreen: RouteScreens.addInsuranceProvider,
  );
}

moveToBaseModuleScreen({required BuildContext context}) {
  context.operation(
    operation: NavigationOperations.pushReplacementNamed,
    routeScreen: RouteScreens.baseModule,
  );
}
