import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/models/part_model.dart';
import 'package:speed_force_franchise/models/purchase_order_model.dart';
import 'package:speed_force_franchise/models/stock_model.dart';
import 'package:speed_force_franchise/modules/accounts/modules/add_expenses/views/add_expense.dart';
import 'package:speed_force_franchise/modules/add_garage/views/add_garage.dart';
import 'package:speed_force_franchise/modules/add_make_models/models/make_models_model.dart';
import 'package:speed_force_franchise/modules/add_make_models/views/add_make_models.dart';
import 'package:speed_force_franchise/modules/more/modules/customer_details/views/customer_details.dart';
import 'package:speed_force_franchise/modules/more/modules/my_customers/views/my_constomers.dart';
import 'package:speed_force_franchise/modules/more/modules/my_vendors/views/my_vendors.dart';
import 'package:speed_force_franchise/modules/more/modules/order_search/views/order_search.dart';
import 'package:speed_force_franchise/modules/more/modules/vehicle_search/views/vehicle_search.dart';
import 'package:speed_force_franchise/modules/orders/views/repair_order_details.dart';
import 'package:speed_force_franchise/modules/parts/modules/add_stock/views/add_stock.dart';
import 'package:speed_force_franchise/modules/home/<USER>/home.dart';
import 'package:speed_force_franchise/modules/login/views/login.dart';
import 'package:speed_force_franchise/modules/orders/models/repair_order_model.dart';
import 'package:speed_force_franchise/modules/orders/views/add_insurance_provider.dart';
import 'package:speed_force_franchise/modules/orders/views/confirm_order.dart';
import 'package:speed_force_franchise/modules/orders/views/create_invoice.dart';
import 'package:speed_force_franchise/modules/orders/views/create_repair_order.dart';
import 'package:speed_force_franchise/modules/orders/views/view_orders.dart';
import 'package:speed_force_franchise/modules/orders/views/repair_details.dart';
import 'package:speed_force_franchise/modules/parts/modules/add_service_part/views/add_service_part.dart';
import 'package:speed_force_franchise/modules/parts/modules/csv_upload/views/csv_upload.dart';
import 'package:speed_force_franchise/modules/parts/modules/purchase_order/views/edit_purchase_order.dart';
import 'package:speed_force_franchise/modules/parts/modules/purchase_order/views/my_purchase_orders.dart';
import 'package:speed_force_franchise/modules/parts/modules/purchase_order/views/purchase_order.dart';
import 'package:speed_force_franchise/modules/parts/modules/stock_in/views/stock_in.dart';
import 'package:speed_force_franchise/modules/parts/modules/view_alerts/views/view_alerts.dart';
import 'package:speed_force_franchise/modules/parts/views/add_vendor.dart';
import 'package:speed_force_franchise/modules/reports/modules/views/mis_report.dart';
import 'package:speed_force_franchise/modules/reports/modules/views/monthly_customer_report.dart';
import 'package:speed_force_franchise/modules/reports/modules/views/spare_parts_consumption.dart';
import 'package:speed_force_franchise/modules/reports/modules/views/total_customers_report.dart';
import 'package:speed_force_franchise/modules/reports/views/reports.dart';
import 'package:speed_force_franchise/routing_manager/extensions.dart';
import 'package:speed_force_franchise/widgets/custom_web_view.dart';

import '../modules/reports/modules/views/inventory_report.dart';

class NavigationRoutesGenerator {
  static Route<dynamic> onGenerateRoute(RouteSettings routeSettings) {
    final RouteScreens routeScreen = RouteScreens.values.firstWhereOrNull(
            (element) => element.name == routeSettings.name) ??
        RouteScreens.root;

    SettingArguments? args;
    if (routeSettings.arguments != null) {
      args = routeSettings.arguments as SettingArguments?;
    }

    switch (routeScreen) {
      case RouteScreens.splash:
      // return MaterialPageRoute(
      //   builder: (BuildContext context) {
      //     return const SplashScreen();
      //   },
      // );
      case RouteScreens.root:
      case RouteScreens.login:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const Login();
          },
        );

      case RouteScreens.baseModule:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const Placeholder();
          },
        );
      case RouteScreens.homeScreen:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const Home();
          },
        );

      case RouteScreens.createRepairOrder:
        Map<String, dynamic> data = args?.argumentsData as Map<String, dynamic>;

        return MaterialPageRoute(
          builder: (BuildContext context) {
            return CreateRepairOrderOrAddCustomer(
              addCustomerOnly: data["addCustomerOnly"] ?? false,
              vehicleOnly: data["vehicleOnly"] ?? false,
              customerId: data["customerId"],
            );
          },
        );
      case RouteScreens.repairDetails:
        Map<String, dynamic> data = args?.argumentsData as Map<String, dynamic>;
        RepairOrderModel? createRepairOrderModel =
            data["createRepairOrder"] != null
                ? data["createRepairOrder"] as RepairOrderModel
                : null;
        bool? isInvoice =
            data["isInvoice"] != null ? data["isInvoice"] as bool : null;

        return MaterialPageRoute(
          builder: (BuildContext context) {
            return RepairDetails(
              createRepairOrderModel: createRepairOrderModel!,
              isInvoice: isInvoice ?? false,
            );
          },
        );
      case RouteScreens.confirmOrder:
        Map<String, dynamic> data = args?.argumentsData as Map<String, dynamic>;
        RepairOrderModel? createRepairOrderModel =
            data["createRepairOrderModel"] != null
                ? data["createRepairOrderModel"] as RepairOrderModel
                : null;
        bool? isInvoice =
            data["isInvoice"] != null ? data["isInvoice"] as bool : null;

        void Function()? onCreate = data["onCreate"] != null
            ? data["onCreate"] as void Function()?
            : null;

        return MaterialPageRoute(
          builder: (BuildContext context) {
            return ConfirmOrder(
              createRepairOrderModel: createRepairOrderModel!,
              isInvoice: isInvoice ?? false,
              onCreate: onCreate,
            );
          },
        );

      case RouteScreens.createInvoice:
        Map<String, dynamic> data = args?.argumentsData as Map<String, dynamic>;

        RepairOrderModel? invoiceModel = data["invoiceModel"] != null
            ? data["invoiceModel"] as RepairOrderModel
            : null;
        void Function()? onInvoiceCreate = data["onInvoiceCreate"] != null
            ? data["onInvoiceCreate"] as void Function()?
            : null;

        bool? isCounterSale = data["isCounterSale"] != null
            ? data["isCounterSale"] as bool
            : null;

        return MaterialPageRoute(
          builder: (BuildContext context) {
            return CreateInvoice(
              isCounterSale: isCounterSale,
              invoiceModel: invoiceModel,
              onInvoiceCreate: onInvoiceCreate,
            );
          },
        );

      case RouteScreens.addGarage:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const AddGarage();
          },
        );

      case RouteScreens.reports:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const Reports();
          },
        );

      case RouteScreens.totalCustomersReports:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const TotalCustomersReport();
          },
        );

      case RouteScreens.monthlyCustomersReports:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const MonthlyCustomersReport();
          },
        );

      case RouteScreens.sparePartsConsumption:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const SparePartsConsumption();
          },
        );
      case RouteScreens.misReports:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const MISReport();
          },
        );
      case RouteScreens.inventory:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const InventoryReport();
          },
        );

      case RouteScreens.openOrders:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            Map<String, dynamic> data =
                args?.argumentsData as Map<String, dynamic>;
            OrderStatus? orderStatus = data["orderStatus"] != null
                ? data["orderStatus"] as OrderStatus
                : null;
            String? vehicleId =
                data["vehicleId"] != null ? data["vehicleId"] as String : null;

            return ViewOrders(
              orderStatus: orderStatus,
              vehicleId: vehicleId,
            );
          },
        );
      case RouteScreens.addStock:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const AddStock();
          },
        );
      case RouteScreens.addVendor:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const AddVendor();
          },
        );
      case RouteScreens.addPart:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return AddServicePart(
              isAddingService: args?.argumentsData as bool,
            );
          },
        );
      case RouteScreens.addMakeModels:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return AddMakeModels(
              makeModelsModel: args?.argumentsData != null
                  ? args?.argumentsData as MakeModelsModel
                  : null,
            );
          },
        );
      case RouteScreens.webview:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return CustomWebView(
              url: args?.argumentsData as String,
            );
          },
        );
      case RouteScreens.csvUpload:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const CsvUpload();
          },
        );
      case RouteScreens.addExpense:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return AddExpense(
              isPartPurchase: args?.argumentsData as bool,
            );
          },
        );
      case RouteScreens.viewAlert:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return ViewAlert(
                stockRecords:
                    args?.argumentsData as List<(PartModel, StockModel)>);
          },
        );
      case RouteScreens.purchaseOrder:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return PurchaseOrder(
                stockRecords: args?.argumentsData != null
                    ? args?.argumentsData as List<(PartModel, StockModel)>
                    : null);
          },
        );
      case RouteScreens.editPurchaseOrder:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return EditPurchaseOrder(
              purchaseOrderModel: args?.argumentsData as PurchaseOrderModel,
            );
          },
        );
      case RouteScreens.myPurchaseOrders:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const MyPurchaseOrders();
          },
        );
      case RouteScreens.stockIn:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return StockIn(
              stockRecords:
                  args?.argumentsData as List<(PartModel, StockModel)>,
            );
          },
        );
      case RouteScreens.myCustomers:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const MyCustomers();
          },
        );
      case RouteScreens.customerDetails:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return CustomerDetails(
              customerId: args?.argumentsData as String,
            );
          },
        );
      case RouteScreens.repairOrderDetails:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return RepairOrderDetails(
              orderId: args?.argumentsData as String,
            );
          },
        );
      case RouteScreens.myVendors:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const MyVendors();
          },
        );
      case RouteScreens.orderSearch:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const OrderSearch();
          },
        );
      case RouteScreens.vehicleSearch:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const VehicleSearch();
          },
        );
      case RouteScreens.addInsuranceProvider:
        return MaterialPageRoute(
          builder: (BuildContext context) {
            return const AddInsuranceProvider();
          },
        );
    }
  }
}
