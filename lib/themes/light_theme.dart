import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class LightTheme {
  static Color primaryColor = const Color(0xFFDD3333);
  static Color secondaryColor = const Color(0xFF343A40);

  static ThemeData getThemeData() {
    return ThemeData(
      fontFamily: GoogleFonts.poppins().fontFamily,
      primaryColor: primaryColor,
      colorScheme: ColorScheme.fromSeed(
          seedColor: primaryColor, background: colorsConstants.whiteColor),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: Colors.white,
        selectedItemColor: primaryColor,
      ),
      useMaterial3: true,
      textTheme: Typography.englishLike2018.apply(
        fontSizeFactor: 1.sp,
        bodyColor: colorsConstants.blackColor,
        fontFamily: GoogleFonts.poppins().fontFamily,
      ),
    );
  }
}
