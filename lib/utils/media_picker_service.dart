import 'package:flutter/foundation.dart';
import 'package:speed_force_franchise/models/selected_images_model.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class MediaPickerService {
  static final ImagePicker _imagePicker = ImagePicker();

  static Future<SelectedImageModel?> pickImage(
      {ImageSource? imageSource}) async {
    XFile? pickedImage =
        await _imagePicker.pickImage(source: ImageSource.gallery);

    if (pickedImage != null) {
      Uint8List imageBytes = await pickedImage.readAsBytes();

      return SelectedImageModel(name: pickedImage.name, uInt8List: imageBytes);
    }

    return null;
  }

  static Future<SelectedImageModel?> captureImage() async {
    XFile? capturedImage =
        await _imagePicker.pickImage(source: ImageSource.camera);

    if (capturedImage != null) {
      Uint8List imageBytes = await capturedImage.readAsBytes();

      return SelectedImageModel(
          name: capturedImage.name, uInt8List: imageBytes);
    }

    return null;
  }

  // Future<Uint8List> imageCompressor(Uint8List list) async {
  //   var result = await FlutterImageCompress.compressWithList(
  //     list,
  //     minHeight: 1920,
  //     minWidth: 1080,
  //     quality: 70,
  //     rotate: 135,
  //   );
  //   return result;
  // }
}
