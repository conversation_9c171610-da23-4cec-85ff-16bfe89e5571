import 'package:permission_handler/permission_handler.dart';
import 'package:speed_force_franchise/utils/utils.dart';

Future<bool> requestManageExternalStoragePermission() async {
  // Check if permission is already granted
  try {
    if (await Permission.manageExternalStorage.isGranted) {
      print("Manage External Storage Permission Granted");
      return true;
    }
    print("object");
    // Request permission
    final status = await Permission.manageExternalStorage.request();

    if (status.isGranted) {
      print("Permission Granted");
      return true;
    } else if (status.isDenied) {
      print("Permission Denied");
      return false;
    } else if (status.isPermanentlyDenied) {
      print("Permission Permanently Denied. Open App Settings.");
      // Open app settings for the user to grant permission manually
      await openAppSettings();
      return false;
    } else {
      return false;
    }
  } catch (e) {
    Utils.showSnackBar(title: e.toString());
    return false;
  }
}
