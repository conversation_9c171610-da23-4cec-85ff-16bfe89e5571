import 'dart:io';
import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:speed_force_franchise/enums/enums.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

extension MetaWid on DateTime {
  String goodDate() {
    try {
      return DateFormat.yMMMM().format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String goodDayDate() {
    try {
      return DateFormat.yMMMMd().format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String goodTime() {
    try {
      return DateFormat('hh:mm a').format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }
}

class Utils {
  static showSnackBar(
      {required String title, String? message, SnackBarType? snackBarType}) {
    Get.showSnackbar(
      GetSnackBar(
        title: title,
        message: message ?? " ",
        backgroundColor:
            _getSnackbarBackgroundColor(snackBarType: snackBarType),
        duration: const Duration(seconds: 2),
        snackStyle: SnackStyle.GROUNDED,
      ),
    );
  }

  static Color _getSnackbarBackgroundColor({SnackBarType? snackBarType}) {
    switch (snackBarType) {
      case SnackBarType.success:
        return colorsConstants.successGreen;
      case SnackBarType.error:
        return colorsConstants.errorRed;
      case SnackBarType.info:
        return colorsConstants.infoYellow;
      default:
        return colorsConstants.hintGrey;
    }
  }

  static const String _chars =
      'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
  static final Random _rnd = Random();

  static String getRandomId(int length, bool onlyUppercase) {
    String generatedString = String.fromCharCodes(Iterable.generate(
        length, (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length))));

    if (onlyUppercase) {
      return generatedString.toUpperCase();
    } else {
      return generatedString;
    }
  }

  static Future<void> requestPermissions(
      {required List<Permission> permissionsList}) async {
    for (Permission permission in permissionsList) {
      if (await permission.isGranted) {
        continue;
      }
      PermissionStatus status = await permission.request();

      if (status.isGranted) {
        continue;
      }
      if (status.isDenied) {
        PermissionStatus status = await permission.request();
        if (status.isGranted) {
          continue;
        }
      } else if (status.isPermanentlyDenied) {
        // Open app settings to allow the user to grant the permission manually
        openAppSettings();
      }
    }

    // if (Platform.isIOS) {
    //   final status = await Permission.storage.request();
    //   if (status.isGranted) {
    //     debugPrint('Storage permission granted');
    //   } else if (status.isDenied) {
    //     debugPrint('Storage permission denied');
    //   } else if (status.isPermanentlyDenied) {
    //     // Open app settings to allow the user to grant the permission manually
    //     openAppSettings();
    //   }
    // } else {
    //   final status = await Permission.manageExternalStorage.request();
    //   if (status.isGranted) {
    //     debugPrint('Storage permission granted');
    //   } else if (status.isDenied) {
    //     debugPrint('Storage permission denied');
    //   } else if (status.isPermanentlyDenied) {
    //     // Open app settings to allow the user to grant the permission manually
    //     openAppSettings();
    //   }
    // }
  }

  static Future<bool> checkVersionSupported() async {
    DocumentSnapshot<Map<String, dynamic>> appSettingsDocSnapshot =
        await FirebaseFirestore.instance
            .collection(FirebaseCollections.appSettings.name)
            .doc("versionSettings")
            .get();

    Map<String, dynamic>? appSettingsMap = appSettingsDocSnapshot.data();

    if (appSettingsMap != null) {
      if (Platform.isAndroid) {
        // String minSupportedVersion = appSettingsMap["minVersion"];
        String minSupportedBuildVersion = appSettingsMap["buildVersion"];
        final packageInfo = await PackageInfo.fromPlatform();
        // String currentAppVersion = packageInfo.version;
        int currentAppBuildVersion = int.tryParse(packageInfo.buildNumber) ?? 9;

        // int currentVersionConverted = getExtendedVersionNumber(currentAppVersion);

        // int firebaseVersionconverted =
        //     getExtendedVersionNumber(minSupportedVersion);
        int firebaseBuildVersionconverted =
            int.tryParse(minSupportedBuildVersion) ?? 9;
        // return currentVersionConverted >= firebaseVersionconverted;
        return currentAppBuildVersion >= firebaseBuildVersionconverted;
      } else {
        {
          String minSupportedBuildVersion = appSettingsMap["buildIOSVersion"];
          final packageInfo = await PackageInfo.fromPlatform();

          int currentAppBuildVersion =
              int.tryParse(packageInfo.buildNumber) ?? 9;

          int firebaseBuildVersionconverted =
              int.tryParse(minSupportedBuildVersion) ?? 9;

          return currentAppBuildVersion >= firebaseBuildVersionconverted;
        }
      }
    }
    return false;
  }

  // static int getExtendedVersionNumber(String version) {
  //   List versionCells = version.split('.');
  //   versionCells = versionCells.map((i) => int.parse(i)).toList();
  //   return versionCells[0] * 100000 + versionCells[1] * 1000 + versionCells[2];
  // }
}
