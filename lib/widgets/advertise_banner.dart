import 'package:flutter/material.dart';
import 'package:speed_force_franchise/models/advertise_model.dart';
import 'package:url_launcher/url_launcher.dart';

class AdvertiseBanner extends StatefulWidget {
  const AdvertiseBanner({super.key, required this.adBanner});

  final AdvertiseModel adBanner;

  @override
  State<AdvertiseBanner> createState() => _AdvertiseBannerState();
}

class _AdvertiseBannerState extends State<AdvertiseBanner> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        try {
          if (widget.adBanner.redirectLink != null &&
              widget.adBanner.redirectLink!.isNotEmpty) {
            Uri uriToBeLaunched = Uri.parse(widget.adBanner.redirectLink!);
            await launchUrl(uriToBeLaunched);
          }
        } catch (e) {
          debugPrint(e.toString());
        }
      },
      child: Container(
        height: double.infinity,
        alignment: Alignment.center, // This is needed
        decoration: const BoxDecoration(
          color: Colors.transparent,
        ),
        constraints: const BoxConstraints.expand(
          width: double.maxFinite,
        ), // add this line

        child: Image.network(
          widget.adBanner.imageUrl ?? "",
          fit: BoxFit.fill,
          width: double.maxFinite,
        ),
      ),
    );
  }
}
