import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class GlobalLoader extends StatelessWidget {
  final Color color;
  final double size;
  final String? description;

  const GlobalLoader({
    super.key,
    this.color = Colors.blue,
    this.size = 50.0,
    this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SpinKitCubeGrid(
            color: colorsConstants.primaryRed,
            size: 50.0,
          ),
          if (description != null) ...[
            HeightBox(10.h),
            Text(description ?? ""),
          ],
        ],
      ),
    );
  }
}
