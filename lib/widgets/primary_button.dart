import 'package:flutter/material.dart';

import '../utils/common_exports.dart';

class PrimaryButton extends StatefulWidget {
  const PrimaryButton({
    super.key,
    this.title,
    this.titleWidget,
    this.backgroundColor,
    this.foregroundColor,
    this.margin,
    this.padding,
    this.radius,
    this.onPress,
  });

  final String? title;
  final Widget? titleWidget;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final EdgeInsets? margin;
  final EdgeInsets? padding;
  final BorderRadiusGeometry? radius;
  final void Function()? onPress;

  @override
  State<PrimaryButton> createState() => _PrimaryButtonState();
}

class _PrimaryButtonState extends State<PrimaryButton> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(boxShadow: Constants.boxShadow),
      padding: widget.padding,
      margin: widget.margin, //?? EdgeInsets.symmetric(horizontal: 16.w),
      child: ElevatedButton(
        onPressed: () {
          widget.onPress?.call();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: widget.backgroundColor ?? colorsConstants.primaryRed,
          foregroundColor: widget.foregroundColor ?? colorsConstants.whiteColor,
          shape: ContinuousRectangleBorder(
            borderRadius: widget.radius ?? BorderRadius.circular(5.r),
          ),
        ),
        child: widget.titleWidget ?? Text(widget.title ?? ""),
      ),
    );
  }
}
