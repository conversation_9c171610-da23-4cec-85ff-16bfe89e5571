import 'package:flutter/material.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class SmallPrimaryButton extends StatelessWidget {
  const SmallPrimaryButton({
    super.key,
    required this.title,
    this.icon,
    this.onPress,
  });

  final String title;
  final IconData? icon;
  final void Function()? onPress;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
      decoration: BoxDecoration(
        color: colorsConstants.primaryRed,
        borderRadius: BorderRadius.circular(2.r),
        boxShadow: Constants.boxShadow,
      ),
      child: InkWell(
        onTap: () async {
          onPress?.call();
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                size: 14.sp,
                color: colorsConstants.whiteColor,
              ),
              WidthBox(5.w),
            ],
            Text(
              title,
              style: TextStyle(
                color: colorsConstants.whiteColor,
                fontSize: 10.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
