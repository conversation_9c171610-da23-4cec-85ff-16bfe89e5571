import 'package:flutter/material.dart';
import 'package:speed_force_franchise/utils/common_exports.dart';

class SmallTextField extends StatefulWidget {
  const SmallTextField({
    super.key,
    this.onChanged,
    this.controller,
    this.height,
    this.width,
    this.cursorHeight,
    this.initialValue,
  });

  final void Function(String)? onChanged;
  final TextEditingController? controller;
  final double? height;
  final double? width;
  final double? cursorHeight;
  final String? initialValue;

  @override
  State<SmallTextField> createState() => _SmallTextFieldState();
}

class _SmallTextFieldState extends State<SmallTextField> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height ?? 25.h,
      width: widget.width ?? 50.w,
      child: TextFormField(
        initialValue: widget.controller == null ? widget.initialValue : null,
        onChanged: widget.onChanged,
        controller: widget.controller,
        // cursorHeight: widget.cursorHeight ?? 20.h,
        style: TextStyle(
          fontSize: 10.sp,
        ),
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        decoration: InputDecoration(
          contentPadding: EdgeInsets.only(left: 5.w),
          border: OutlineInputBorder(
            borderSide: BorderSide(color: colorsConstants.hintGrey),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: colorsConstants.hintGrey),
          ),
        ),
      ),
    );
  }
}
